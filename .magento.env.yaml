stage:
  build:
    VERBOSE_COMMANDS: "-vvv"
    SCD_THREADS: 48
    SCD_STRATEGY: "standard"
    SCD_MATRIX:
      "Totaltools/base":
        language:
          - en_AU
      "Oci/base":
        language:
          - en_AU
    QUALITY_PATCHES:
      - ACSD-59514
      - ACSD-63244
      - ACP2E-3705
  global:
    SCD_COMPRESSION_LEVEL: 0
  deploy:
    MYSQL_USE_SLAVE_CONNECTION: true
    CACHE_CONFIGURATION:
      _merge: true
      frontend:
        default:
          backend_options:
            cleanup_percentage: 90
            read_timeout: 10
            remote_backend_options:
              read_timeout: 10
            compress_data: 4 # 0-9
            compress_tags: 4 # 0-9
            compress_threshold: 20480 # don't compress files smaller than this value
            compression_lib: 'gzip'
            use_stale_cache: false
          stale_cache_enabled:
            backend_options:
              use_stale_cache: true
            type:
              default:
                frontend: "default"
              layout:
                frontend: "stale_cache_enabled"
              block_html:
                frontend: "stale_cache_enabled"
              reflection:
                frontend: "stale_cache_enabled"
              config_integration:
                frontend: "stale_cache_enabled"
              config_integration_api:
                frontend: "stale_cache_enabled"
              full_page:
                frontend: "stale_cache_enabled"
              translate:
                frontend: "stale_cache_enabled"
    SKIP_SCD: true
    FORCE_UPDATE_URLS: false
    UPDATE_URLS: false
    SCD_COMPRESSION_LEVEL: 6
    REDIS_BACKEND: '\Magento\Framework\Cache\Backend\RemoteSynchronizedCache'
    CRON_CONSUMERS_RUNNER:
      cron_run: true
      consumers: []
    SESSION_CONFIGURATION:
      _merge: true
      redis:
        timeout: 5
        disable_locking: 1
        bot_first_lifetime: 60
        bot_lifetime: 7200
        max_lifetime: 2592000
        min_lifetime: 60
        max_concurrency: 100
  post-deploy:
    WARM_UP_PAGES: []
