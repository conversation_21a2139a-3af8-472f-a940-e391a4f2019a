<?php
return [
    'scopes' => [
        'websites' => [
            'admin' => [
                'website_id' => '0',
                'code' => 'admin',
                'name' => 'Admin',
                'sort_order' => '0',
                'default_group_id' => '0',
                'is_default' => '0'
            ],
            'base' => [
                'website_id' => '1',
                'code' => 'base',
                'name' => 'Default Website',
                'sort_order' => '0',
                'default_group_id' => '1',
                'is_default' => '1'
            ],
            'qld' => [
                'website_id' => '2',
                'code' => 'qld',
                'name' => 'Queensland Website',
                'sort_order' => '1',
                'default_group_id' => '2',
                'is_default' => '0'
            ],
            'sa' => [
                'website_id' => '3',
                'code' => 'sa',
                'name' => 'South Australia Website',
                'sort_order' => '2',
                'default_group_id' => '3',
                'is_default' => '0'
            ],
            'wa' => [
                'website_id' => '4',
                'code' => 'wa',
                'name' => 'Western Australia Website',
                'sort_order' => '3',
                'default_group_id' => '4',
                'is_default' => '0'
            ],
            'oci' => [
                'website_id' => '5',
                'code' => 'oci',
                'name' => 'OCI Website',
                'sort_order' => '0',
                'default_group_id' => '5',
                'is_default' => '0'
            ]
        ],
        'groups' => [
            [
                'group_id' => '0',
                'website_id' => '0',
                'code' => 'default',
                'name' => 'Default',
                'root_category_id' => '0',
                'default_store_id' => '0'
            ],
            [
                'group_id' => '1',
                'website_id' => '1',
                'code' => 'default_store',
                'name' => 'Default Store',
                'root_category_id' => '2',
                'default_store_id' => '1'
            ],
            [
                'group_id' => '2',
                'website_id' => '2',
                'code' => 'queensland_store',
                'name' => 'Queensland Store',
                'root_category_id' => '2',
                'default_store_id' => '2'
            ],
            [
                'group_id' => '3',
                'website_id' => '3',
                'code' => 'south_australia_store',
                'name' => 'South Australia Store',
                'root_category_id' => '2',
                'default_store_id' => '3'
            ],
            [
                'group_id' => '4',
                'website_id' => '4',
                'code' => 'western_australia_store',
                'name' => 'Western Australia Store',
                'root_category_id' => '2',
                'default_store_id' => '4'
            ],
            [
                'group_id' => '5',
                'website_id' => '5',
                'code' => 'oci_store',
                'name' => 'OCI Store',
                'root_category_id' => '4058',
                'default_store_id' => '5'
            ]
        ],
        'stores' => [
            'admin' => [
                'store_id' => '0',
                'code' => 'admin',
                'website_id' => '0',
                'group_id' => '0',
                'name' => 'Admin',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'default' => [
                'store_id' => '1',
                'code' => 'default',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'Default Store View',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'qld' => [
                'store_id' => '2',
                'code' => 'qld',
                'website_id' => '2',
                'group_id' => '2',
                'name' => 'Queensland Store View',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'sa' => [
                'store_id' => '3',
                'code' => 'sa',
                'website_id' => '3',
                'group_id' => '3',
                'name' => 'South Australia Store View',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'wa' => [
                'store_id' => '4',
                'code' => 'wa',
                'website_id' => '4',
                'group_id' => '4',
                'name' => 'Western Australia Store View',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'oci' => [
                'store_id' => '5',
                'code' => 'oci',
                'website_id' => '5',
                'group_id' => '5',
                'name' => 'OCI Store View',
                'sort_order' => '0',
                'is_active' => '1'
            ]
        ]
    ],
    'system' => [
        'default' => [
            'general' => [
                'locale' => [
                    'code' => 'en_AU'
                ]
            ],
            'dev' => [
                'static' => [
                    'sign' => '1'
                ],
                'template' => [
                    'allow_symlink' => '0',
                    'minify_html' => '0'
                ],
                'js' => [
                    'merge_files' => '0',
                    'enable_js_bundling' => '0',
                    'minify_files' => '1',
                    'move_script_to_bottom' => '0',
                    'translate_strategy' => 'dictionary',
                    'session_storage_logging' => '0',
                    'minify_exclude' => [
                        'tiny_mce' => '/tiny_mce/',
                        'cardinal_commerce' => '/v1/songbird'
                    ]
                ],
                'css' => [
                    'merge_css_files' => '0',
                    'minify_files' => '1',
                    'use_css_critical_path' => '0',
                    'minify_exclude' => [
                        'tiny_mce' => '/tiny_mce/'
                    ]
                ]
            ],
            'advanced' => [
                'modules_disable_output' => [
                    'Aheadworks_AdvancedReports' => '0',
                    'Algolia_AlgoliaSearch' => '0',
                    'Amasty_Base' => '0',
                    'Amasty_Label' => '0',
                    'Amasty_Promo' => '0',
                    'Amasty_Rgrid' => '0',
                    'Amasty_Rules' => '0',
                    'Amasty_RulesPro' => '0',
                    'Amasty_Shopby' => '0',
                    'Amasty_ShopbyBrand' => '0',
                    'Amasty_ShopbyPage' => '0',
                    'Amasty_ShopbyRoot' => '0',
                    'Amasty_ShopbySeo' => '0',
                    'Anowave_Ec' => '0',
                    'Anowave_Package' => '0',
                    'Apptrian_FacebookPixel' => '0',
                    'Balance_Amasty' => '0',
                    'Balance_Box' => '0',
                    'Balance_CacheInvalidate' => '0',
                    'Balance_Cookiecustom' => '0',
                    'Balance_Dev' => '0',
                    'Balance_GoogleAnalytics' => '0',
                    'Balance_ImportBlocks' => '0',
                    'Balance_Megamenu' => '0',
                    'Balance_ValidateData' => '0',
                    'Balance_ZenDesk' => '0',
                    'Balance_ZipMoney' => '0',
                    'DanielSousa_UrlRewrite' => '0',
                    'DannyNimmo_VisualMerchandiserRebuild' => '0',
                    'Ebizmarts_AbandonedCart' => '0',
                    'Ebizmarts_AutoResponder' => '0',
                    'Ebizmarts_MageMonkey' => '0',
                    'Ebizmarts_Mandrill' => '0',
                    'MSP_ReCaptcha' => '0',
                    'MSP_SecuritySuiteCommon' => '0',
                    'MageWorx_SeoMarkup' => '0',
                    'Magefan_Blog' => '0',
                    'Magento_AdminGws' => '0',
                    'Magento_AdminNotification' => '0',
                    'Magento_AdvancedCatalog' => '0',
                    'Magento_AdvancedCheckout' => '0',
                    'Magento_AdvancedPricingImportExport' => '0',
                    'Magento_AdvancedRule' => '0',
                    'Magento_AdvancedSalesRule' => '0',
                    'Magento_AdvancedSearch' => '0',
                    'Magento_Amqp' => '0',
                    'Magento_Authorization' => '0',
                    'Magento_Authorizenet' => '0',
                    'Magento_Backup' => '0',
                    'Magento_Banner' => '0',
                    'Magento_BannerCustomerSegment' => '0',
                    'Magento_Braintree' => '0',
                    'Magento_Bundle' => '0',
                    'Magento_BundleImportExport' => '0',
                    'Magento_BundleStaging' => '0',
                    'Magento_CacheInvalidate' => '0',
                    'Magento_Captcha' => '0',
                    'Magento_Catalog' => '0',
                    'Magento_CatalogEvent' => '0',
                    'Magento_CatalogImportExport' => '0',
                    'Magento_CatalogImportExportStaging' => '0',
                    'Magento_CatalogInventory' => '0',
                    'Magento_CatalogInventoryStaging' => '0',
                    'Magento_CatalogPermissions' => '0',
                    'Magento_CatalogRule' => '0',
                    'Magento_CatalogRuleConfigurable' => '0',
                    'Magento_CatalogRuleStaging' => '0',
                    'Magento_CatalogSearch' => '0',
                    'Magento_CatalogStaging' => '0',
                    'Magento_CatalogUrlRewrite' => '0',
                    'Magento_CatalogUrlRewriteStaging' => '0',
                    'Magento_CatalogWidget' => '0',
                    'Magento_Checkout' => '0',
                    'Magento_CheckoutAgreements' => '0',
                    'Magento_CheckoutStaging' => '0',
                    'Magento_Cms' => '0',
                    'Magento_CmsStaging' => '0',
                    'Magento_CmsUrlRewrite' => '0',
                    'Magento_Config' => '0',
                    'Magento_ConfigurableImportExport' => '0',
                    'Magento_ConfigurableProduct' => '0',
                    'Magento_ConfigurableProductStaging' => '0',
                    'Magento_Contact' => '0',
                    'Magento_Cookie' => '0',
                    'Magento_Cron' => '0',
                    'Magento_CurrencySymbol' => '0',
                    'Magento_CustomAttributeManagement' => '0',
                    'Magento_Customer' => '0',
                    'Magento_CustomerBalance' => '0',
                    'Magento_CustomerCustomAttributes' => '0',
                    'Magento_CustomerFinance' => '0',
                    'Magento_CustomerImportExport' => '0',
                    'Magento_CustomerSegment' => '0',
                    'Magento_Cybersource' => '0',
                    'Magento_Deploy' => '0',
                    'Magento_Developer' => '0',
                    'Magento_Dhl' => '0',
                    'Magento_Directory' => '0',
                    'Magento_Downloadable' => '0',
                    'Magento_DownloadableImportExport' => '0',
                    'Magento_DownloadableStaging' => '0',
                    'Magento_Eav' => '0',
                    'Magento_Elasticsearch' => '0',
                    'Magento_Email' => '0',
                    'Magento_EncryptionKey' => '0',
                    'Magento_Enterprise' => '0',
                    'Magento_Eway' => '0',
                    'Magento_Fedex' => '0',
                    'Magento_GiftCard' => '0',
                    'Magento_GiftCardAccount' => '0',
                    'Magento_GiftCardImportExport' => '0',
                    'Magento_GiftCardStaging' => '0',
                    'Magento_GiftMessage' => '0',
                    'Magento_GiftMessageStaging' => '0',
                    'Magento_GiftRegistry' => '0',
                    'Magento_GiftWrapping' => '0',
                    'Magento_GiftWrappingStaging' => '0',
                    'Magento_GoogleAdwords' => '0',
                    'Magento_GoogleAnalytics' => '0',
                    'Magento_GoogleOptimizer' => '0',
                    'Magento_GoogleOptimizerStaging' => '0',
                    'Magento_GoogleTagManager' => '0',
                    'Magento_GroupedImportExport' => '0',
                    'Magento_GroupedProduct' => '0',
                    'Magento_GroupedProductStaging' => '0',
                    'Magento_ImportExport' => '0',
                    'Magento_Indexer' => '0',
                    'Magento_Integration' => '0',
                    'Magento_Invitation' => '0',
                    'Magento_LayeredNavigation' => '0',
                    'Magento_LayeredNavigationStaging' => '0',
                    'Magento_Logging' => '0',
                    'Magento_Marketplace' => '0',
                    'Magento_MediaStorage' => '0',
                    'Magento_MessageQueue' => '0',
                    'Magento_Msrp' => '0',
                    'Magento_MsrpStaging' => '0',
                    'Magento_MultipleWishlist' => '0',
                    'Magento_Multishipping' => '0',
                    'Magento_MysqlMq' => '0',
                    'Magento_NewRelicReporting' => '0',
                    'Magento_Newsletter' => '0',
                    'Magento_OfflinePayments' => '0',
                    'Magento_OfflineShipping' => '0',
                    'Magento_PageCache' => '0',
                    'Magento_Payment' => '0',
                    'Magento_PaymentStaging' => '0',
                    'Magento_Paypal' => '0',
                    'Magento_Persistent' => '0',
                    'Magento_PersistentHistory' => '0',
                    'Magento_PricePermissions' => '0',
                    'Magento_ProductAlert' => '0',
                    'Magento_ProductVideo' => '0',
                    'Magento_ProductVideoStaging' => '0',
                    'Magento_PromotionPermissions' => '0',
                    'Magento_Quote' => '0',
                    'Magento_Reminder' => '0',
                    'Magento_Reports' => '0',
                    'Magento_RequireJs' => '0',
                    'Magento_ResourceConnections' => '0',
                    'Magento_Review' => '0',
                    'Magento_ReviewStaging' => '0',
                    'Magento_Reward' => '0',
                    'Magento_Rma' => '0',
                    'Magento_RmaStaging' => '0',
                    'Magento_Rss' => '0',
                    'Magento_Rule' => '0',
                    'Magento_Sales' => '0',
                    'Magento_SalesArchive' => '0',
                    'Magento_SalesInventory' => '0',
                    'Magento_SalesRule' => '0',
                    'Magento_SalesRuleStaging' => '0',
                    'Magento_SalesSequence' => '0',
                    'Magento_SampleData' => '0',
                    'Magento_ScalableCheckout' => '0',
                    'Magento_ScalableInventory' => '0',
                    'Magento_ScalableOms' => '0',
                    'Magento_ScheduledImportExport' => '0',
                    'Magento_Search' => '0',
                    'Magento_SearchStaging' => '0',
                    'Magento_Security' => '0',
                    'Magento_SendFriend' => '0',
                    'Magento_Shipping' => '0',
                    'Magento_Sitemap' => '0',
                    'Magento_Solr' => '0',
                    'Magento_Staging' => '0',
                    'Magento_Store' => '0',
                    'Magento_Support' => '0',
                    'Magento_Swagger' => '0',
                    'Magento_Swatches' => '0',
                    'Magento_SwatchesLayeredNavigation' => '0',
                    'Magento_TargetRule' => '0',
                    'Magento_Tax' => '0',
                    'Magento_TaxImportExport' => '0',
                    'Magento_Theme' => '0',
                    'Magento_Translation' => '0',
                    'Magento_Ui' => '0',
                    'Magento_Ups' => '0',
                    'Magento_UrlRewrite' => '0',
                    'Magento_User' => '0',
                    'Magento_Usps' => '0',
                    'Magento_Variable' => '0',
                    'Magento_Vault' => '0',
                    'Magento_Version' => '0',
                    'Magento_VersionsCms' => '0',
                    'Magento_VisualMerchandiser' => '0',
                    'Magento_Webapi' => '0',
                    'Magento_WebapiSecurity' => '0',
                    'Magento_WebsiteRestriction' => '0',
                    'Magento_Weee' => '0',
                    'Magento_WeeeStaging' => '0',
                    'Magento_Widget' => '0',
                    'Magento_Wishlist' => '0',
                    'Magento_Worldpay' => '0',
                    'Magestore_Sociallogin' => '0',
                    'Magestore_Storelocator' => '0',
                    'Magestore_Storepickup' => '0',
                    'OlegKoval_RegenerateUrlRewrites' => '0',
                    'PCAPredict_Tag' => '0',
                    'Totaltools_Autogeneratemeta' => '0',
                    'Totaltools_Backend' => '0',
                    'Totaltools_Box' => '0',
                    'Totaltools_Brand' => '0',
                    'Totaltools_Bundle' => '0',
                    'Totaltools_Captcha' => '0',
                    'Totaltools_Catalog' => '0',
                    'Totaltools_CatalogInventory' => '0',
                    'Totaltools_ChannelAdvisorAPI' => '0',
                    'Totaltools_Checkout' => '0',
                    'Totaltools_CheckoutAgreements' => '0',
                    'Totaltools_Cms' => '0',
                    'Totaltools_Contact' => '0',
                    'Totaltools_Customer' => '0',
                    'Totaltools_Data' => '0',
                    'Totaltools_Email' => '0',
                    'Totaltools_Geo' => '0',
                    'Totaltools_GiftCard' => '0',
                    'Totaltools_Importcontent' => '0',
                    'Totaltools_Importstores' => '0',
                    'Totaltools_Label' => '0',
                    'Totaltools_Loqate' => '0',
                    'Totaltools_Loyalty' => '0',
                    'Totaltools_Megamenu' => '0',
                    'Totaltools_Modification' => '0',
                    'Totaltools_Newsletter' => '0',
                    'Totaltools_OCI' => '0',
                    'Totaltools_Pronto' => '0',
                    'Totaltools_Quote' => '0',
                    'Totaltools_Reports' => '0',
                    'Totaltools_Sales' => '0',
                    'Totaltools_SendFriend' => '0',
                    'Totaltools_Setup' => '0',
                    'Totaltools_Shipping' => '0',
                    'Totaltools_Shippit' => 1,
                    'Totaltools_Sociallogin' => '0',
                    'Totaltools_Specialproduct' => '0',
                    'Totaltools_StagingEnvironment' => '0',
                    'Totaltools_Storelocator' => '0',
                    'Totaltools_Subcategories' => '0',
                    'Totaltools_TargetRule' => '0',
                    'Totaltools_Theme' => '0',
                    'Totaltools_Vii' => '0',
                    'Totaltools_Wishlist' => '0',
                    'Unirgy_RapidFlow' => '0',
                    'Unirgy_RapidFlowPro' => '0',
                    'Unirgy_SimpleLicense' => '0',
                    'Unirgy_SimpleUp' => '0',
                    'Wagento_Zendesk' => '0',
                    'WiseRobot_ChannelAdvisorAPI' => '0',
                    'Wyomind_Core' => '0',
                    'Wyomind_CronScheduler' => '1',
                    'Wyomind_GoogleCustomerReviews' => '0',
                    'Wyomind_SimpleGoogleShopping' => '0',
                    'Wyomind_Watchlog' => '0',
                    'ZipMoney_ZipMoneyPayment' => '0',
                    'Vertex_Tax' => '1'
                ]
            ]
        ],
        'stores' => [

        ],
        'websites' => [

        ]
    ],
    'modules' => [
        'Magento_Store' => 1,
        'Magento_Config' => 1,
        'Magento_AdminAnalytics' => 1,
        'Magento_Directory' => 1,
        'Magento_AdminNotification' => 1,
        'Magento_AdminGwsConfigurableProduct' => 1,
        'Magento_AdminGwsStaging' => 1,
        'Magento_Theme' => 1,
        'Magento_Eav' => 1,
        'Magento_AdobeCommerceOutOfProcessExtensibility' => 1,
        'Magento_AdobeIms' => 1,
        'Magento_AdobeIoEventsClient' => 1,
        'Magento_AdobeCommerceWebhooks' => 1,
        'Magento_AdobeCommerceWebhooksGenerator' => 1,
        'Magento_AdobeCommerceEventsClient' => 1,
        'Magento_AdobeImsApi' => 1,
        'Magento_AdobeCommerceEventsGenerator' => 1,
        'Magento_AdobeStockAdminUi' => 1,
        'Magento_MediaGallery' => 1,
        'Magento_AdobeStockAssetApi' => 1,
        'Magento_AdobeStockClient' => 1,
        'Magento_AdobeStockClientApi' => 1,
        'Magento_AdobeStockImage' => 1,
        'Magento_Variable' => 1,
        'Magento_AdobeStockImageApi' => 1,
        'Magento_Customer' => 1,
        'Magento_Indexer' => 1,
        'Magento_AdvancedPricingImportExport' => 1,
        'Magento_Rule' => 1,
        'Magento_Cms' => 1,
        'Magento_Backend' => 1,
        'Magento_Amqp' => 1,
        'Magento_Security' => 1,
        'Magento_ApplicationPerformanceMonitor' => 1,
        'Magento_ApplicationPerformanceMonitorNewRelic' => 1,
        'Magento_Cookie' => 1,
        'Magento_ApplicationServerNewRelic' => 1,
        'Magento_ApplicationServerPerformanceMonitor' => 1,
        'Magento_ApplicationServerStateMonitor' => 1,
        'Magento_ApplicationServerStateMonitorGraphQl' => 1,
        'Magento_AsyncConfig' => 1,
        'Magento_Catalog' => 1,
        'Magento_Authorization' => 1,
        'Magento_User' => 1,
        'Magento_GraphQl' => 1,
        'Magento_MediaStorage' => 1,
        'Magento_AwsS3CustomerCustomAttributes' => 1,
        'Magento_GiftCardImportExport' => 1,
        'Magento_Widget' => 1,
        'Magento_ImportExport' => 1,
        'Magento_Enterprise' => 1,
        'Magento_AdminAdobeIms' => 1,
        'Magento_Backup' => 1,
        'Magento_CatalogRule' => 1,
        'Magento_Payment' => 1,
        'Magento_Quote' => 1,
        'Magento_SalesSequence' => 1,
        'Magento_Sales' => 1,
        'Magento_SalesRule' => 1,
        'Magento_Bundle' => 1,
        'Magento_EavGraphQl' => 1,
        'Magento_BundleImportExport' => 1,
        'Magento_BundleImportExportStaging' => 1,
        'Magento_CatalogInventory' => 1,
        'Magento_RequisitionList' => 1,
        'Magento_GraphQlResolverCache' => 1,
        'Magento_Search' => 1,
        'Magento_Checkout' => 1,
        'Magento_CacheInvalidate' => 1,
        'Magento_CatalogEvent' => 1,
        'Magento_CardinalCommerce' => 1,
        'Magento_AdminGws' => 1,
        'Magento_Integration' => 1,
        'Magento_CmsGraphQl' => 1,
        'Magento_StoreGraphQl' => 1,
        'Magento_CatalogSearch' => 1,
        'Magento_AsyncOrder' => 1,
        'Magento_EavGraphQlAux' => 1,
        'Magento_CatalogImportExport' => 1,
        'Magento_CatalogImportExportStaging' => 1,
        'Magento_CommerceBackendUix' => 1,
        'Magento_CatalogInventoryGraphQl' => 1,
        'Magento_CatalogUrlRewrite' => 1,
        'Magento_CatalogPageBuilderAnalytics' => 1,
        'Magento_CatalogPageBuilderAnalyticsStaging' => 1,
        'Magento_CustomerCustomAttributes' => 1,
        'Magento_Ui' => 1,
        'Magento_CustomerSegment' => 1,
        'Magento_Msrp' => 1,
        'Magento_CatalogRuleGraphQl' => 1,
        'Magento_VersionsCms' => 1,
        'Magento_Downloadable' => 1,
        'Magento_GiftCard' => 1,
        'Magento_Staging' => 1,
        'Magento_Captcha' => 1,
        'Magento_ConfigurableProduct' => 1,
        'Magento_CatalogGraphQl' => 1,
        'Magento_PageCache' => 1,
        'Magento_Robots' => 1,
        'Magento_QuoteGraphQl' => 1,
        'Magento_CheckoutAddressSearch' => 1,
        'Magento_Wishlist' => 1,
        'Magento_AdvancedCheckout' => 1,
        'Magento_CheckoutAgreements' => 1,
        'Magento_CheckoutAgreementsGraphQl' => 1,
        'Magento_CheckoutAgreementsNegotiableQuote' => 1,
        'Magento_ScalableOms' => 1,
        'Magento_CheckoutStaging' => 1,
        'Magento_CloudComponents' => 1,
        'Magento_Company' => 1,
        'Magento_CatalogCmsGraphQl' => 1,
        'Magento_CmsPageBuilderAnalytics' => 1,
        'Magento_CmsPageBuilderAnalyticsStaging' => 1,
        'Magento_CmsStaging' => 1,
        'Magento_CmsUrlRewrite' => 1,
        'Magento_CmsUrlRewriteGraphQl' => 1,
        'Magento_AdminUiSdkCustomFees' => 1,
        'Magento_Tax' => 1,
        'Magento_MessageQueue' => 1,
        'Magento_CompanyCredit' => 1,
        'Magento_Newsletter' => 1,
        'Magento_CustomerImportExport' => 1,
        'Magento_CustomerGraphQl' => 1,
        'Magento_NegotiableQuote' => 1,
        'Magento_NegotiableQuoteTemplate' => 1,
        'Magento_CompanyPayment' => 1,
        'Magento_CompanyQuote' => 1,
        'Magento_NegotiableQuoteGraphQl' => 1,
        'Magento_Logging' => 1,
        'Magento_CompanyRelation' => 1,
        'Magento_Shipping' => 1,
        'Magento_CompareListGraphQl' => 1,
        'Magento_TwoFactorAuth' => 1,
        'Magento_ConfigurableImportExport' => 1,
        'Magento_ConfigurableNegotiableQuote' => 1,
        'Magento_CatalogRuleConfigurable' => 1,
        'Magento_ConfigurableProductGraphQl' => 1,
        'Magento_ConfigurableProductSales' => 1,
        'Magento_ProductAlert' => 1,
        'Magento_ConfigurableRequisitionList' => 1,
        'Magento_RequisitionListGraphQl' => 1,
        'Magento_WebsiteRestriction' => 1,
        'Magento_Contact' => 1,
        'Magento_ContactGraphQl' => 1,
        'Magento_ContactGraphQlPwa' => 1,
        'Magento_ApplicationServer' => 1,
        'Magento_Cron' => 1,
        'Magento_Csp' => 1,
        'Magento_CurrencySymbol' => 1,
        'Magento_CustomAttributeManagement' => 1,
        'Magento_AdvancedCatalog' => 1,
        'Magento_Analytics' => 1,
        'Magento_CustomerBalance' => 1,
        'Magento_CustomerBalanceGraphQl' => 1,
        'Magento_Banner' => 1,
        'Magento_CustomerCustomAttributesGraphQl' => 1,
        'Magento_DownloadableGraphQl' => 1,
        'Magento_CustomerFinance' => 1,
        'Magento_CompanyGraphQl' => 1,
        'Magento_CompanyCustomerImportExport' => 1,
        'Magento_CatalogWidget' => 1,
        'Magento_CustomerSegmentGraphQl' => 1,
        'Magento_DataExporter' => 1,
        'Magento_DeferredTotalCalculating' => 1,
        'Magento_Deploy' => 1,
        'Magento_Developer' => 1,
        'Magento_Dhl' => 0,
        'Magento_BundleGraphQl' => 1,
        'Magento_DirectoryGraphQl' => 1,
        'Magento_TargetRule' => 1,
        'Magento_DownloadableCompany' => 1,
        'Magento_CustomerDownloadableGraphQl' => 1,
        'Magento_DownloadableImportExport' => 1,
        'Magento_DownloadableRequisitionListGraphQl' => 1,
        'Magento_VisualMerchandiser' => 1,
        'Magento_CheckoutAddressSearchNegotiableQuote' => 1,
        'Magento_BundleRequisitionListGraphQl' => 1,
        'Magento_CatalogGraphQlAux' => 1,
        'Magento_AdvancedSearch' => 1,
        'Magento_Elasticsearch' => 1,
        'Magento_CatalogPermissions' => 1,
        'Magento_ElasticsearchCatalogPermissionsGraphQl' => 1,
        'Magento_Email' => 1,
        'Magento_EncryptionKey' => 1,
        'Magento_B2b' => 1,
        'Magento_Fedex' => 0,
        'Magento_Weee' => 1,
        'Magento_GiftCardAccount' => 1,
        'Magento_GiftCardAccountGraphQl' => 1,
        'Magento_WishlistGraphQl' => 1,
        'Magento_Sitemap' => 1,
        'Magento_GiftCardNegotiableQuote' => 1,
        'Magento_GiftCardRequisitionList' => 1,
        'Magento_GiftCardGraphQl' => 1,
        'Magento_SharedCatalog' => 1,
        'Magento_GiftRegistry' => 1,
        'Magento_GiftMessage' => 1,
        'Magento_GiftMessageGraphQl' => 1,
        'Magento_GiftMessageStaging' => 1,
        'Magento_CatalogStaging' => 1,
        'Magento_GiftRegistryGraphQl' => 1,
        'Magento_GiftWrapping' => 1,
        'Magento_GiftWrappingGraphQl' => 1,
        'Magento_GiftWrappingStaging' => 1,
        'Magento_GoogleAdwords' => 1,
        'Magento_GoogleAnalytics' => 1,
        'Magento_GoogleGtag' => 1,
        'Magento_GoogleOptimizer' => 1,
        'Magento_GoogleOptimizerStaging' => 1,
        'Magento_GoogleTagManager' => 1,
        'Magento_GraphQlServer' => 1,
        'Magento_GraphQlCache' => 1,
        'Magento_GraphQlNewRelic' => 1,
        'Magento_CatalogCustomerGraphQl' => 1,
        'Magento_AdminGraphQlServer' => 1,
        'Magento_GroupedProduct' => 1,
        'Magento_GroupedImportExport' => 1,
        'Magento_GroupedCatalogInventory' => 1,
        'Magento_GroupedProductGraphQl' => 1,
        'Magento_GroupedProductStaging' => 1,
        'Magento_GroupedRequisitionList' => 1,
        'Magento_GroupedSharedCatalog' => 1,
        'Magento_ImportCsv' => 1,
        'Magento_ImportCsvApi' => 1,
        'Magento_RemoteStorage' => 1,
        'Magento_ImportJson' => 1,
        'Magento_ImportJsonApi' => 1,
        'Magento_AdvancedRule' => 1,
        'Magento_InstantPurchase' => 1,
        'Magento_CatalogAnalytics' => 1,
        'Magento_IntegrationGraphQl' => 1,
        'Magento_Inventory' => 1,
        'Magento_InventoryAdminUi' => 1,
        'Magento_InventoryAdvancedCheckout' => 1,
        'Magento_InventoryApi' => 1,
        'Magento_InventoryBundleImportExport' => 1,
        'Magento_InventoryBundleProduct' => 1,
        'Magento_InventoryBundleProductAdminUi' => 1,
        'Magento_InventoryBundleProductIndexer' => 1,
        'Magento_InventoryCatalog' => 1,
        'Magento_InventorySales' => 1,
        'Magento_InventoryCatalogAdminUi' => 1,
        'Magento_InventoryCatalogApi' => 1,
        'Magento_InventoryCatalogFrontendUi' => 1,
        'Magento_InventoryCatalogSearch' => 1,
        'Magento_InventoryCatalogSearchBundleProduct' => 1,
        'Magento_InventoryCatalogSearchConfigurableProduct' => 1,
        'Magento_InventoryConfigurableProduct' => 1,
        'Magento_InventoryConfigurableProductAdminUi' => 1,
        'Magento_InventoryConfigurableProductFrontendUi' => 1,
        'Magento_InventoryConfigurableProductIndexer' => 1,
        'Magento_InventoryConfiguration' => 1,
        'Magento_InventoryConfigurationApi' => 1,
        'Magento_InventoryDistanceBasedSourceSelection' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionAdminUi' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionApi' => 1,
        'Magento_InventoryElasticsearch' => 1,
        'Magento_InventoryExportStockApi' => 1,
        'Magento_InventoryIndexer' => 1,
        'Magento_InventorySalesApi' => 1,
        'Magento_InventoryGroupedProduct' => 1,
        'Magento_InventoryGroupedProductAdminUi' => 1,
        'Magento_InventoryGroupedProductIndexer' => 1,
        'Magento_InventoryImportExport' => 1,
        'Magento_InventoryInStorePickupApi' => 1,
        'Magento_InventoryInStorePickupAdminUi' => 1,
        'Magento_InventorySourceSelectionApi' => 1,
        'Magento_InventoryInStorePickup' => 1,
        'Magento_InventoryInStorePickupGraphQl' => 1,
        'Magento_InventoryInStorePickupShippingApi' => 1,
        'Magento_InventoryInStorePickupQuote' => 1,
        'Magento_InventoryInStorePickupQuoteGraphQl' => 1,
        'Magento_InventoryInStorePickupSales' => 1,
        'Magento_InventoryInStorePickupSalesApi' => 1,
        'Magento_InventoryInStorePickupSalesAdminUi' => 1,
        'Magento_InventoryInStorePickupShipping' => 1,
        'Magento_InventoryInStorePickupShippingAdminUi' => 1,
        'Magento_Multishipping' => 0,
        'Magento_Webapi' => 1,
        'Magento_InventoryCache' => 1,
        'Magento_InventoryLowQuantityNotification' => 1,
        'Magento_Reports' => 1,
        'Magento_InventoryLowQuantityNotificationApi' => 1,
        'Magento_InventoryMultiDimensionalIndexerApi' => 1,
        'Magento_InventoryProductAlert' => 1,
        'Magento_InventoryQuoteGraphQl' => 1,
        'Magento_InventoryRequisitionList' => 1,
        'Magento_InventoryReservations' => 1,
        'Magento_InventoryReservationCli' => 1,
        'Magento_InventoryReservationsApi' => 1,
        'Magento_InventoryExportStock' => 1,
        'Magento_InventorySalesAdminUi' => 1,
        'Magento_InventoryGraphQl' => 1,
        'Magento_InventorySalesAsyncOrder' => 1,
        'Magento_InventorySalesFrontendUi' => 1,
        'Magento_InventorySetupFixtureGenerator' => 1,
        'Magento_InventoryShipping' => 1,
        'Magento_InventoryShippingAdminUi' => 1,
        'Magento_InventorySourceDeductionApi' => 1,
        'Magento_InventorySourceSelection' => 1,
        'Magento_InventoryInStorePickupFrontend' => 1,
        'Magento_InventorySwatchesFrontendUi' => 1,
        'Magento_InventoryVisualMerchandiser' => 1,
        'Magento_InventoryWishlist' => 1,
        'Magento_Invitation' => 1,
        'Magento_JwtFrameworkAdapter' => 1,
        'Magento_JwtUserToken' => 1,
        'Magento_LayeredNavigation' => 1,
        'Magento_LayeredNavigationStaging' => 1,
        'Magento_CompanyRelationSharedCatalog' => 1,
        'Magento_LoginAsCustomer' => 1,
        'Magento_LoginAsCustomerAdminUi' => 1,
        'Magento_LoginAsCustomerApi' => 1,
        'Magento_LoginAsCustomerAssistance' => 1,
        'Magento_LoginAsCustomerFrontendUi' => 1,
        'Magento_LoginAsCustomerGraphQl' => 1,
        'Magento_LoginAsCustomerLog' => 1,
        'Magento_LoginAsCustomerLogging' => 1,
        'Magento_LoginAsCustomerPageCache' => 1,
        'Magento_LoginAsCustomerQuote' => 1,
        'Magento_LoginAsCustomerSales' => 1,
        'Magento_LoginAsCustomerWebsiteRestriction' => 1,
        'Magento_Marketplace' => 0,
        'Magento_MediaContent' => 1,
        'Magento_MediaContentApi' => 1,
        'Magento_MediaContentCatalog' => 1,
        'Magento_MediaContentCatalogStaging' => 1,
        'Magento_MediaContentCms' => 1,
        'Magento_MediaContentSynchronization' => 1,
        'Magento_MediaContentSynchronizationApi' => 1,
        'Magento_MediaContentSynchronizationCatalog' => 1,
        'Magento_MediaContentSynchronizationCms' => 1,
        'Magento_AdobeStockAsset' => 1,
        'Magento_MediaGalleryApi' => 1,
        'Magento_MediaGalleryCatalog' => 1,
        'Magento_MediaGalleryCatalogIntegration' => 1,
        'Magento_MediaGalleryCatalogUi' => 1,
        'Magento_MediaGalleryCmsUi' => 1,
        'Magento_MediaGalleryIntegration' => 1,
        'Magento_MediaGalleryMetadata' => 1,
        'Magento_MediaGalleryMetadataApi' => 1,
        'Magento_MediaGalleryRenditions' => 1,
        'Magento_MediaGalleryRenditionsApi' => 1,
        'Magento_MediaGallerySynchronization' => 1,
        'Magento_MediaGallerySynchronizationApi' => 1,
        'Magento_MediaGallerySynchronizationMetadata' => 1,
        'Magento_MediaGalleryUi' => 1,
        'Magento_MediaGalleryUiApi' => 1,
        'Magento_AwsS3' => 1,
        'Magento_AsynchronousOperations' => 1,
        'Magento_UrlRewrite' => 1,
        'Magento_MsrpConfigurableProduct' => 1,
        'Magento_MsrpGroupedProduct' => 1,
        'Magento_MsrpStaging' => 1,
        'Magento_Multicoupon' => 1,
        'Magento_MulticouponGraphQl' => 1,
        'Magento_AdvancedSalesRule' => 1,
        'Magento_MultipleWishlist' => 1,
        'Magento_SalesGraphQl' => 1,
        'Magento_InventoryInStorePickupMultishipping' => 1,
        'Magento_MysqlMq' => 1,
        'Magento_BundleNegotiableQuote' => 1,
        'Magento_NegotiableQuoteAsyncOrder' => 1,
        'Magento_NegotiableQuoteDuplicate' => 1,
        'Magento_NegotiableQuoteDuplicateGraphQl' => 1,
        'Magento_CompanyQuoteGraphQl' => 1,
        'Magento_NegotiableQuoteRequisitionList' => 1,
        'Magento_NegotiableQuoteRequisitionListGraphQl' => 1,
        'Magento_NegotiableQuoteSharedCatalog' => 1,
        'Magento_CompanyNegotiableQuoteTemplate' => 1,
        'Magento_NegotiableQuoteTemplateGraphQl' => 1,
        'Magento_NegotiableQuoteTemplateSharedCatalog' => 1,
        'Magento_NegotiableQuoteWeee' => 1,
        'Magento_NewRelicReporting' => 1,
        'Magento_CompanyCreditGraphQl' => 1,
        'Magento_NewsletterGraphQl' => 1,
        'Magento_NewsletterGraphQlPwa' => 1,
        'Magento_OfflinePayments' => 1,
        'Magento_OfflineShipping' => 1,
        'Magento_OpenSearch' => 1,
        'Magento_OrderCancellation' => 1,
        'Magento_OrderCancellationGraphQl' => 1,
        'Magento_OrderCancellationUi' => 1,
        'Magento_OrderHistorySearch' => 1,
        'Magento_BannerCustomerSegment' => 1,
        'Magento_PageBuilder' => 1,
        'Magento_PageBuilderAdminGwsAdminUi' => 1,
        'Magento_PageBuilderAnalytics' => 1,
        'Magento_PageBuilderPwa' => 1,
        'Magento_ConfigurableProductStaging' => 1,
        'Magento_BannerGraphQl' => 1,
        'Magento_PaymentGraphQl' => 1,
        'Magento_ServiceProxy' => 1,
        'Magento_Vault' => 1,
        'Magento_PaymentServicesDashboard' => 1,
        'Magento_PaymentServicesPaypalGraphQl' => 1,
        'Magento_QueryXml' => 1,
        'Magento_PaymentStaging' => 1,
        'Magento_ServicesConnector' => 1,
        'Magento_Paypal' => 1,
        'Magento_PaypalGraphQl' => 1,
        'Magento_PaypalNegotiableQuote' => 1,
        'Magento_PaypalOnBoarding' => 1,
        'Magento_PurchaseOrder' => 1,
        'Magento_Persistent' => 1,
        'Magento_PersistentHistory' => 1,
        'Magento_PricePermissions' => 1,
        'Magento_CatalogInventoryStaging' => 1,
        'Magento_ProductVideo' => 1,
        'Magento_ProductVideoStaging' => 1,
        'Magento_PromotionPermissions' => 1,
        'Magento_CheckoutAgreementsPurchaseOrder' => 1,
        'Magento_PurchaseOrderGraphQl' => 1,
        'Magento_PurchaseOrderRule' => 1,
        'Magento_PurchaseOrderRuleGraphQl' => 1,
        'Magento_ServicesId' => 1,
        'Magento_QuickOrder' => 1,
        'Magento_QuickOrderGraphQl' => 1,
        'Magento_BannerPageBuilderAnalytics' => 1,
        'Magento_QuoteAnalytics' => 1,
        'Magento_QuoteBundleOptions' => 1,
        'Magento_QuoteCommerceGraphQl' => 1,
        'Magento_QuoteConfigurableOptions' => 1,
        'Magento_QuoteDownloadableLinks' => 1,
        'Magento_QuoteGiftCardOptions' => 1,
        'Magento_AsyncOrderGraphQl' => 1,
        'Magento_QuoteStaging' => 1,
        'Magento_ReCaptchaAdminUi' => 1,
        'Magento_ReCaptchaCheckout' => 1,
        'Magento_ReCaptchaCheckoutSalesRule' => 1,
        'Magento_ReCaptchaCompany' => 1,
        'Magento_ReCaptchaContact' => 1,
        'Magento_ReCaptchaCustomer' => 1,
        'Magento_ReCaptchaFrontendUi' => 1,
        'Magento_ReCaptchaGiftCard' => 1,
        'Magento_ReCaptchaGraphQlPwa' => 1,
        'Magento_ReCaptchaInvitation' => 1,
        'Magento_ReCaptchaMigration' => 1,
        'Magento_ReCaptchaMultipleWishlist' => 1,
        'Magento_ReCaptchaNewsletter' => 1,
        'Magento_ReCaptchaPaypal' => 1,
        'Magento_ReCaptchaUi' => 1,
        'Magento_ReCaptchaReview' => 1,
        'Magento_ReCaptchaSendFriend' => 1,
        'Magento_ReCaptchaStorePickup' => 1,
        'Magento_ReCaptchaValidationApi' => 1,
        'Magento_ReCaptchaUser' => 1,
        'Magento_ReCaptchaValidation' => 1,
        'Magento_ReCaptchaVersion2Checkbox' => 1,
        'Magento_ReCaptchaVersion2Invisible' => 1,
        'Magento_ReCaptchaWebapiApi' => 1,
        'Magento_ReCaptchaVersion3Invisible' => 1,
        'Magento_ReCaptchaPwa' => 1,
        'Magento_ReCaptchaWebapiGraphQl' => 1,
        'Magento_ReCaptchaWebapiRest' => 1,
        'Magento_ReCaptchaWebapiUi' => 1,
        'Magento_ReCaptchaWishlist' => 1,
        'Magento_RelatedProductGraphQl' => 1,
        'Magento_ReleaseNotification' => 1,
        'Magento_Reminder' => 1,
        'Magento_AwsS3GiftCardImportExport' => 1,
        'Magento_RemoteStorageCommerce' => 1,
        'Magento_InventoryLowQuantityNotificationAdminUi' => 1,
        'Magento_RequireJs' => 1,
        'Magento_BundleRequisitionList' => 1,
        'Magento_ConfigurableRequisitionListGraphQl' => 1,
        'Magento_ResourceConnections' => 1,
        'Magento_Review' => 1,
        'Magento_ReviewAnalytics' => 1,
        'Magento_ReviewGraphQl' => 1,
        'Magento_ReviewStaging' => 1,
        'Magento_Reward' => 1,
        'Magento_RewardGraphQl' => 1,
        'Magento_SalesRuleStaging' => 1,
        'Magento_Rma' => 1,
        'Magento_RmaGraphQl' => 1,
        'Magento_RmaStaging' => 1,
        'Magento_ScheduledImportExport' => 1,
        'Magento_Rss' => 0,
        'Magento_MulticouponUi' => 1,
        'Magento_ServicesIdGraphQlServer' => 1,
        'Magento_PaypalPurchaseOrder' => 1,
        'Magento_SalesAnalytics' => 1,
        'Magento_SalesArchive' => 1,
        'Magento_ServicesIdLayout' => 1,
        'Magento_MultipleWishlistGraphQl' => 1,
        'Magento_SalesGraphQlAux' => 1,
        'Magento_SalesInventory' => 1,
        'Magento_BannerStaging' => 1,
        'Magento_SalesRuleGraphQl' => 1,
        'Magento_RewardStaging' => 1,
        'Magento_CatalogRuleStaging' => 1,
        'Magento_SampleData' => 0,
        'Magento_ScalableCheckout' => 1,
        'Magento_ScalableInventory' => 1,
        'Magento_CompanyNegotiableQuote' => 1,
        'Magento_AwsS3ScheduledImportExport' => 1,
        'Magento_Elasticsearch7' => 1,
        'Magento_SearchStaging' => 1,
        'Magento_CustomerAnalytics' => 1,
        'Magento_Securitytxt' => 1,
        'Magento_SendFriend' => 0,
        'Magento_SendFriendGraphQl' => 0,
        'Magento_PaymentServicesBase' => 1,
        'Magento_SaaSCommon' => 1,
        'Magento_SalesDataExporter' => 1,
        'Magento_StoreDataExporter' => 1,
        'Magento_PaymentServicesPaypal' => 1,
        'Magento_BundleSharedCatalog' => 1,
        'Magento_SharedCatalogGraphQl' => 1,
        'Magento_CompanyShipping' => 1,
        'Magento_AwsS3PageBuilder' => 1,
        'Magento_StagingGraphQl' => 1,
        'Magento_CatalogStagingGraphQl' => 1,
        'Magento_StagingPageBuilder' => 1,
        'Magento_CatalogPermissionsGraphQl' => 1,
        'Magento_PaymentServicesSaaSExport' => 1,
        'Magento_UrlRewriteGraphQl' => 1,
        'Magento_Support' => 1,
        'Magento_Swagger' => 1,
        'Magento_SwaggerWebapi' => 1,
        'Magento_SwaggerWebapiAsync' => 1,
        'Magento_Swat' => 1,
        'Magento_Swatches' => 1,
        'Magento_SwatchesGraphQl' => 1,
        'Magento_SwatchesLayeredNavigation' => 1,
        'Magento_DownloadableStaging' => 1,
        'Magento_TargetRuleGraphQl' => 1,
        'Magento_ConfigurableSharedCatalog' => 1,
        'Magento_TaxGraphQl' => 1,
        'Magento_TaxImportExport' => 1,
        'Magento_AdobeStockImageAdminUi' => 1,
        'Magento_ThemeGraphQl' => 1,
        'Magento_Translation' => 1,
        'Magento_AdminAdobeImsTwoFactorAuth' => 1,
        'Magento_GiftCardSharedCatalog' => 1,
        'Magento_Ups' => 0,
        'Magento_CatalogUrlRewriteStaging' => 1,
        'Magento_CatalogUrlRewriteGraphQl' => 1,
        'Magento_UrlRewriteGraphQlPwa' => 1,
        'Magento_CompanyAsynchronousOperations' => 1,
        'Magento_Usps' => 0,
        'Magento_CheckoutAddressSearchGiftRegistry' => 1,
        'Magento_PaypalCaptcha' => 1,
        'Magento_VaultGraphQl' => 1,
        'Magento_Version' => 0,
        'Magento_BannerPageBuilder' => 1,
        'Magento_VersionsCmsPageCache' => 1,
        'Magento_VersionsCmsUrlRewrite' => 1,
        'Magento_VersionsCmsUrlRewriteGraphQl' => 1,
        'Magento_CatalogStagingPageBuilder' => 1,
        'Magento_InventoryInStorePickupWebapiExtension' => 1,
        'Magento_WebapiAsync' => 1,
        'Magento_WebapiSecurity' => 1,
        'Magento_ElasticsearchCatalogPermissions' => 0,
        'Magento_GiftCardStaging' => 1,
        'Magento_WeeeGraphQl' => 0,
        'Magento_WeeeGraphQlAux' => 1,
        'Magento_WeeeStaging' => 0,
        'Magento_PageBuilderAdminAnalytics' => 1,
        'Magento_BundleStaging' => 1,
        'Magento_WishlistAnalytics' => 1,
        'Magento_WishlistGiftCard' => 1,
        'Magento_WishlistGiftCardGraphQl' => 1,
        'Magento_GiftCardRequisitionListGraphQl' => 1,
        'Afterpay_Afterpay' => 1,
        'Afterpay_CashApp' => 1,
        'Aheadworks_AdvancedReports' => 1,
        'Amasty_BannersLite' => 1,
        'Amasty_Base' => 1,
        'Amasty_Geoip' => 1,
        'PayPal_Braintree' => 1,
        'Amasty_CheckoutCore' => 1,
        'Amasty_CheckoutGiftWrap' => 1,
        'Amasty_CheckoutGraphQl' => 1,
        'Amasty_CheckoutLayoutBuilder' => 1,
        'Amasty_CheckoutProPackage' => 1,
        'Amasty_CheckoutStyleSwitcher' => 1,
        'Amasty_CheckoutThankYouPage' => 1,
        'Amasty_CompositeProductPriceIndexer' => 1,
        'Amasty_Conditions' => 1,
        'Amasty_CronScheduleList' => 1,
        'Amasty_Checkout' => 1,
        'Amasty_GoogleAddressAutocomplete' => 1,
        'Amasty_ImageOptimizer' => 1,
        'Amasty_ImageOptimizerUi' => 1,
        'Amasty_ImageOptimizerSpeedSize' => 1,
        'Amasty_Label' => 1,
        'Amasty_LabelCacheCleanerForQtyVariable' => 1,
        'Amasty_LabelGraphQl' => 1,
        'Amasty_LazyLoad' => 1,
        'Amasty_LazyLoadUi' => 1,
        'Amasty_LibSwiperJs' => 1,
        'Amasty_Mage245Fix' => 1,
        'Amasty_PageSpeedOptimizer' => 1,
        'Amasty_PageSpeedOptimizerPro' => 1,
        'Amasty_PageSpeedTools' => 1,
        'Amasty_Promo' => 1,
        'Amasty_PromoBanners' => 1,
        'Amasty_Rgrid' => 1,
        'Amasty_Rules' => 1,
        'Amasty_RulesPro' => 1,
        'Amasty_SalesRuleWizard' => 1,
        'Amasty_ShopbyBase' => 1,
        'Amasty_Shopby' => 1,
        'Amasty_ShopbyBrand' => 1,
        'Amasty_ShopbyBrandGraphQl' => 1,
        'Amasty_ShopbyPage' => 1,
        'Amasty_ShopbySeo' => 1,
        'Anowave_Package' => 1,
        'Anowave_Ec' => 1,
        'Anowave_Ec4' => 1,
        'Balance_B2b' => 1,
        'Balance_Box' => 1,
        'Balance_ClearMedia' => 1,
        'Balance_Cookiecustom' => 1,
        'Balance_Dev' => 1,
        'Balance_GoogleAnalytics' => 1,
        'Balance_ImportBlocks' => 1,
        'Balance_ReviewsImporter' => 1,
        'Signifyd_Connect' => 1,
        'Balance_ValidateData' => 1,
        'Wagento_Zendesk' => 1,
        'Emartech_Emarsys' => 1,
        'Fastly_Cdn' => 1,
        'Humm_HummPaymentGateway' => 1,
        'MageWorx_OpenAI' => 1,
        'MageWorx_SeoAll' => 1,
        'MageWorx_Info' => 1,
        'MageWorx_GoogleAI' => 1,
        'MageWorx_SeoAI' => 1,
        'MageWorx_HtmlSitemap' => 1,
        'MageWorx_SeoBase' => 1,
        'MageWorx_SeoBreadcrumbs' => 1,
        'MageWorx_SeoCategoryGrid' => 1,
        'MageWorx_SeoCrossLinks' => 1,
        'MageWorx_SeoExtended' => 1,
        'MageWorx_SeoMarkup' => 1,
        'MageWorx_SeoRedirects' => 1,
        'MageWorx_SeoReports' => 1,
        'MageWorx_SeoUrls' => 1,
        'MageWorx_SeoXTemplates' => 1,
        'MageWorx_XmlSitemap' => 1,
        'Magedelight_Base' => 1,
        'Magedelight_Megamenu' => 1,
        'Magefan_AdminUserGuide' => 1,
        'Magefan_Community' => 1,
        'Magefan_Blog' => 1,
        'Magefan_BlogPlus' => 1,
        'Magefan_BlogGraphQl' => 1,
        'Magefan_WysiwygAdvanced' => 1,
        'Magestore_Storelocator' => 1,
        'Magestore_Storepickup' => 1,
        'Marwato_Core' => 1,
        'Marwato_Dev' => 1,
        'Marwato_DevTools' => 1,
        'Marwato_Sales' => 1,
        'Meta_BusinessExtension' => 1,
        'Meta_Catalog' => 1,
        'Meta_Conversion' => 1,
        'Meta_Promotions' => 1,
        'Meta_Sales' => 1,
        'OlegKoval_RegenerateUrlRewrites' => 1,
        'Openpay_Payment' => 1,
        'Openpay_Widgets' => 1,
        'Amasty_CheckoutDeliveryDate' => 1,
        'PayPal_BraintreeCustomerBalance' => 1,
        'PayPal_BraintreeGiftCardAccount' => 1,
        'PayPal_BraintreeGiftWrapping' => 1,
        'PayPal_BraintreeGraphQl' => 1,
        'Shippit_Shipping' => 1,
        'Balance_Signifyd' => 1,
        'Totalinfotech_Dev' => 1,
        'Totalinfotech_Learning' => 1,
        'Totalinfotech_TestModule' => 1,
        'Totalinfotech_VersionFix' => 1,
        'Totaltools_AddToCompareWishlist' => 1,
        'Totaltools_AdvancedReports' => 1,
        'Totaltools_Afterpay' => 1,
        'Totaltools_Autogeneratemeta' => 1,
        'Totaltools_Backend' => 1,
        'Totaltools_Blog' => 1,
        'Totaltools_Box' => 1,
        'Totaltools_Brand' => 1,
        'Totaltools_Bundle' => 1,
        'Totaltools_Captcha' => 1,
        'Totaltools_Catalog' => 1,
        'Totaltools_CatalogInventory' => 1,
        'Totaltools_CatalogSearchOrder' => 1,
        'Totaltools_Storelocator' => 1,
        'Totaltools_ClearCart' => 1,
        'Totaltools_ClickAndCollect' => 1,
        'Totaltools_Cms' => 1,
        'Totaltools_Commercial' => 1,
        'Totaltools_Company' => 1,
        'Totaltools_CompanyCredit' => 1,
        'Totaltools_Configuration' => 1,
        'Totaltools_Contact' => 1,
        'Totaltools_Csp' => 1,
        'Totaltools_CustomAttributeManagement' => 1,
        'Totaltools_Loqate' => 1,
        'Totaltools_CustomerImport' => 1,
        'Totaltools_Data' => 1,
        'Totaltools_Ec' => 1,
        'Totaltools_Email' => 1,
        'Totaltools_EmailCart' => 0,
        'Totaltools_Emarsys' => 1,
        'Totaltools_Geo' => 1,
        'Totaltools_HoverImage' => 1,
        'Totaltools_Importcontent' => 1,
        'Totaltools_Importstores' => 1,
        'Totaltools_Indexer' => 1,
        'Totaltools_InstantPurchase' => 1,
        'Totaltools_Label' => 1,
        'Totaltools_PriceImport' => 1,
        'Totaltools_Longtail' => 1,
        'Totaltools_Customer' => 1,
        'Totaltools_Pronto' => 1,
        'Totaltools_MagentoReward' => 1,
        'Totaltools_MassOrderAction' => 1,
        'Totaltools_Megamenu' => 1,
        'Totaltools_MultipleWishlist' => 1,
        'Totaltools_Newsletter' => 1,
        'Totaltools_PaypalPayinFour' => 1,
        'Totaltools_Performance' => 1,
        'Totaltools_Pixels' => 1,
        'Totaltools_Postcodes' => 1,
        'Totaltools_LabelsAttributeImport' => 1,
        'Totaltools_PriceMatch' => 1,
        'Totaltools_PromoBanners' => 1,
        'Totaltools_Loyalty' => 1,
        'Totaltools_ProntoAttributeImport' => 1,
        'Totaltools_ProntoReward' => 1,
        'Totaltools_Pwa' => 1,
        'Totaltools_Quote' => 1,
        'Totaltools_RapidFlow' => 1,
        'Totaltools_Reports' => 1,
        'Totaltools_RequisitionList' => 1,
        'Totaltools_RequisitionListGraphQl' => 1,
        'Totaltools_Reward' => 1,
        'Totaltools_Rule' => 1,
        'Totaltools_Sales' => 1,
        'Totaltools_SalesRule' => 1,
        'Totaltools_ScheduledImportExport' => 1,
        'Unbxd_ProductFeed' => 1,
        'Totaltools_SendCoupon' => 1,
        'Totaltools_SendFriend' => 1,
        'Totaltools_Setup' => 1,
        'Totaltools_SharedCatalog' => 1,
        'Totaltools_Shipping' => 1,
        'Totaltools_ShippingRule' => 1,
        'Totaltools_Checkout' => 1,
        'Totaltools_Signifyd' => 1,
        'Totaltools_Specialproduct' => 1,
        'Totaltools_StagingEnvironment' => 1,
        'Totaltools_StoreAccount' => 1,
        'Totaltools_Shippit' => 1,
        'Totaltools_Storepickup' => 1,
        'Totaltools_Subcategories' => 1,
        'Totaltools_SyncGrid' => 1,
        'Totaltools_Testingmodule' => 1,
        'Totaltools_Theme' => 1,
        'Totaltools_TwoFAWhitelist' => 1,
        'Totaltools_Unbxd' => 1,
        'Totaltools_Vii' => 1,
        'Totaltools_Wagento' => 1,
        'Totaltools_Wishlist' => 1,
        'Totaltools_WyomindCronScheduler' => 1,
        'Xtento_XtCore' => 1,
        'Zip_ZipPayment' => 1,
        'Totaltools_Search' => 1,
        'Unirgy_SimpleUp' => 1,
        'Unirgy_SimpleLicense' => 1,
        'Balance_ZenDesk' => 1,
        'Wyomind_Framework' => 1,
        'Wyomind_GoogleCustomerReviews' => 1,
        'Wyomind_SimpleGoogleShopping' => 1,
        'Xtento_CustomSmtp' => 1,
        'Xtento_ProductExport' => 1,
        'Xtento_OrderExport' => 1,
        'Totaltools_Xtento' => 1,
        'Totaltools_ZipMoney' => 1
    ],
    'admin_user' => [
        'locale' => [
            'code' => [
                'en_AU'
            ]
        ]
    ]
];
