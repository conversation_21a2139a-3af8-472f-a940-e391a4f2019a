& when (@media-common = true) {
    .customer-commercial-account,
    .customer-commercial-success,
    .customer-store-account,
    .customer-store-success,
    .storelocator-commercial-account,
    .customer-account-create {
        .page-title-wrapper {
            .page-title {
                color: @color-gray20;
                .lib-css(font-weight, @font-weight__bold);
                letter-spacing: -.25px;
                text-transform: uppercase;
            }
        }

        .customer-register-wrapper {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;

            .block {
                .block-box-wrapper {
                    border: 1px solid @color-gray80;

                    .form {
                        &.form-create-account {
                            width: 100%;
                            min-width: unset;

                            &.create {
                                &.account {
                                    margin-top: 20px;
                                }
                            }

                            .fieldset {
                                border: 0;
                                margin: 0;
                                padding: 0;
                                .lib-vendor-prefix-display;
                                .lib-vendor-prefix-flex-wrap;
                                .lib-css(justify-content, space-between, 1);

                                &._my-preferences {
                                    display: none;
                                }

                                .field {
                                    margin-bottom: 20px;

                                    &.required {
                                        .label {
                                            &::after {
                                                content: '*';
                                                color: @color-red6;
                                                margin-left: 2px;
                                            }
                                        }
                                    }

                                    .label {
                                        width: 100%;
                                        max-width: ~"calc(100% - 35px)";
                                        text-align: left;
                                        display: inline-block;
                                        margin-bottom: 10px;
                                        color: @color-gray40;
                                        .lib-font-size(16);
                                    }

                                    &.field-referred_by {
                                        display: block;
                                    }
                                    &.confirmation {
                                        display: none;
                                    }

                                    &.field-default_terms_and_conditions {
                                        max-width: 100%;
                                        width: 100%;

                                        .label {
                                            max-width: 100%;

                                            &.unlocked_text {
                                                margin-bottom: 0;
                                            }
                                        }
                                    }

                                    &.password {
                                        width: 100%;
                                    }
                                }

                                .field-recaptcha {
                                    margin-bottom: 20px;
                                }
                            }
                        }
                    }
                }
            }

            .block-title {
                padding: 0 0 11px;
                margin: 0 0 16px;
                border-bottom: 1px solid @color-gray90;
                text-transform: uppercase;
                .lib-css(letter-spacing, -0.3px);
                color: @color-gray20;
                .lib-css(font-weight, @font-weight__bold);
                line-height: 1.25;
                .lib-font-size(17);

                .required-note {
                    .lib-font-size(16);
                    .lib-line-height(22);
                    text-transform: none;
                    color: @color-gray40;
                    margin: 12px 0 0;
                    .lib-css(font-weight, @font-weight__regular);

                    &::before {
                        content: "*";
                        color: @primary__color;
                        margin: 0 8px 0 0;
                    }
                }
            }

            p {
                margin-bottom: 20px;
            }

            .block-top {
                .block-content {
                    p {
                        img {
                            max-width: 200px;
                        }
                    }
                }
            }

            .customer-register-benefits-wrapper {
                .block-register-benefits {
                    .block-box-wrapper {
                        border: 0;

                        hr {
                            display: none;
                        }
                    }
                }

                .block-top {
                    width: 100%;

                    .block-title {
                        padding: 0;
                        border: 0 none;

                        strong {
                            &::before {
                                line-height: inherit;
                                color: @color-red;
                                content: '\e855';
                                font-family: @font-tt-icons;
                                margin: -5px 10px 0 0;
                                vertical-align: middle;
                                display: inline-block;
                                .lib-css(font-weight, @font-weight__regular);
                                text-align: center;
                            }
                        }
                    }

                    ul {
                        list-style: none;
                        padding: 0;
                        margin: 0;

                        li {
                            position: relative;
                            padding-left: 25px;

                            &::before {
                                content: '\2022';
                                position: absolute;
                                display: block;
                                left: 0;
                                top: 8px;
                                color: @color-red;
                                .lib-font-size(40);
                                .lib-line-height(1);
                            }
                        }
                    }
                }
            }

            .unlock-info-img {
                font-size: 0;
                margin-bottom: 15px;

                img {
                    width: 100%;

                    &.desktop-img {
                        display: none;
                    }
                }
            }

            .unlock-signUp-img {
                order: 4;
                font-size: 0;
                margin-top: 20px;
                display: inline-block;

                img {
                    width: 100%;

                    &.unlock-desktop-img {
                        display: none;
                    }
                }
            }

            .block-bottom {
                border: 2px solid @color-gray90;
                padding: 20px;
                width: 100%;

                p {
                    margin-bottom: 20px
                }

                .action {
                    &.create {
                        background-color: transparent;
                        border: 2px solid @secondary__color;
                        display: inline-block;
                        width: auto;
                        padding: 12px 30px;
                        .lib-css(border-radius, 4px, 1);
                        color: @secondary__color;

                        &:hover {
                            background-color: @secondary__color;
                            color: @color-white;
                        }
                    }
                }
            }
        }

    }
    .customer-commercial-account {
        .customer-register-wrapper {
            .customer-register-benefits-wrapper {
                .block-top {
                    .block-title {
                        strong {
                            &::before {
                                display: none;
                            }
                        }
                    }
                }
            }
        }
    }
    .customer-store-account {
        .customer-register-wrapper {
            .customer-register-benefits-wrapper {
                .block-top {
                    .block-title {
                        strong {
                            &::before {
                                display: none;
                            }
                        }
                    }
                }
            }
        }
    }
    .customer-account-create {
        .customer-register-wrapper {
            .customer-register-benefits-wrapper {
                .block-top {
                    &.trade-reward {
                        .block-title {
                            strong {
                                &::before {
                                    display: none;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .street-note {
        .lib-css(color, @color-gray40);
        .lib-font-size(12) !important;
    }
    .hide {
        display: none;
    }
    .has-strength-meter {
        .lib-vendor-prefix-display !important;
        .lib-css(flex-direction, column, 1);

        .control {
            .input-text {
                .lib-css(order, 1, 1);
            }

            div {
                &#password-error {
                    .lib-css(order, 3, 1);
                }

                &.mage-error {
                    .lib-font-size(12);
                    color: @color-red7;
                    padding: 0;
                    background: transparent;
                }
            }

            #password-strength-meter-container {
                .lib-css(order, 2, 1);
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                .lib-css(justify-content, space-between, 1);

                .password-strength-meter {
                    display: inline-block;
                    background-color: @color-gray95;
                    padding: 5px 8px;
                    .lib-line-height(22);
                    min-height: 32px;
                    height: auto;

                    &::before {
                        display: none;
                    }

                    #password-strength-meter-label {
                        .lib-css(font-weight, @font-weight__bold);
                    }
                }

                &.password-weak {
                    #password-strength-meter-label {
                        color: @color-blue3;
                    }
                }

                &.password-strong {
                    #password-strength-meter-label {
                        color: @color-green3;
                    }
                }

                .show-password {
                    text-decoration: underline;
                    cursor: pointer;
                    margin-top: 6px;
                    .lib-font-size(14);

                    &:hover {
                        color: @secondary__color;
                    }
                }
            }
        }
    }

    .field {
        .tooltip {
            position: relative;

            .tooltip-toggle {
                cursor: help;

                &:hover {
                    &+.tooltip-content {
                        display: block;
                    }
                }
            }

            .tooltip-content {
                background: @color-white;
                max-width: 360px;
                padding: 12px 16px;
                z-index: 100;
                display: none;
                position: absolute;
                text-align: left;
                color: @color-gray20;
                .lib-line-height(23);
                border: 1px solid @color-gray-light2;
                margin-left: 5px;
                left: 100%;
                top: 0;
                min-width: 200px;
                white-space: normal;

                &::before {
                    border: solid transparent;
                    content: '';
                    height: 0;
                    position: absolute;
                    width: 0;
                    border-right-color: @color-gray-light2;
                    margin-top: -6px;
                    top: 15px;
                    border-width: 6px;
                    right: 100%;
                }

                &:after {
                    border: solid transparent;
                    content: '';
                    height: 0;
                    position: absolute;
                    width: 0;
                    border-right-color: @color-white;
                    margin-top: -5px;
                    top: 15px;
                    border-width: 5px;
                    right: 100%;
                }
            }
        }
    }
    
    .unlocked_text {
        display: block;
        margin-bottom: 20px;
        
        &::after {
            display: none;
        }

        .unlock-img {    
            display: block;
            max-width: 140px;
            margin-bottom: 10px;
        }
    }

    // login Page CSS
    .redirected-from-checkout {
        color: @color-green3;
        .lib-css(font-weight, @font-weight__bold);
    }

    .customer-account-login {
        .page-title-wrapper {
            .page-title {
                color: @color-gray20;
                .lib-css(font-weight, @font-weight__bold);
                letter-spacing: -.25px;
                text-transform: uppercase;
            }
        }

        .main {
            .login-container {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                .lib-css(align-items, flex-start, 1);

                .block-title {
                    padding: 0 0 11px;
                    margin: 0 0 16px;
                    border-bottom: 1px solid @color-gray90;
                    text-transform: uppercase;
                    .lib-css(letter-spacing, -0.3px);
                    color: @color-gray20;
                    .lib-css(font-weight, @font-weight__bold);
                    line-height: 1.25;
                    .lib-font-size(17);

                    .required-note {
                        .lib-font-size(16);
                        .lib-line-height(22);
                        text-transform: none;
                        color: @color-gray40;
                        margin: 12px 0 0;
                        .lib-css(font-weight, @font-weight__regular);

                        &::before {
                            content: "*";
                            color: @primary__color;
                            margin: 0 8px 0 0;
                        }
                    }
                }

                p {
                    margin-bottom: 20px;
                }

                .customer_login_top_text {
                    p {
                        margin-bottom: 0;
                    }
                }

                .block {
                    &.block-customer-login {
                        border: 1px solid @color-gray80;
                        
                        .form {
                            &.form-login {
                                .no-captcha {
                                    margin-top: 20px;
                                }

                                .actions-toolbar {
                                    .lib-vendor-prefix-display;
                                    .lib-vendor-prefix-flex-wrap;
                                    margin-bottom: 10px;

                                    .primary {
                                        display: inline-block;
                                        margin: 0 0 10px 0;

                                        button {
                                            background: @secondary__color;
                                            border: 2px solid @color-blue8;
                                            color: @color-white;
                                            cursor: pointer;
                                            display: inline-block;
                                            .lib-css(font-weight, @font-weight__bold);
                                            margin: 0;
                                            padding: 10px 20px;
                                            .lib-font-size(14);
                                            .lib-line-height(14);
                                            vertical-align: middle;
                                            text-transform: uppercase;
                                            .lib-css(letter-spacing, -0.26px);
                                            .lib-css(border-radius, 3px, 1);

                                            &:hover {
                                                background-color: @color-blue8;
                                                border-color: @color-blue5;
                                            }
                                        }
                                    }

                                    .secondary {
                                        padding-top: 10px;
                                        margin-left: 25px;

                                        a {
                                            color: @secondary__color;
                                            text-decoration: underline;
                                        }
                                    }
                                }

                                .required-note {
                                    color: @primary__color;
                                }

                                .fieldset {
                                    border: 0;
                                    margin: 0;
                                    padding: 0;

                                    &::after {
                                        display: none;
                                    }

                                    .field {
                                        &.note {
                                            margin: 0;
                                            padding-bottom: 20px;
                                        }

                                        &.required {
                                            &:after {
                                                color: @primary__color;
                                                .lib-font-size(16);
                                            }

                                            .label {
                                                &::after {
                                                    content: '*';
                                                    color: @color-red6;
                                                    margin-left: 2px;
                                                }
                                            }
                                        }

                                        .label {
                                            width: 100%;
                                            max-width: ~"calc(100% - 35px)";
                                            text-align: left;
                                            display: inline-block;
                                            margin-bottom: 10px;
                                            color: @color-gray40;
                                            .lib-font-size(16);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .login-content {
                    h2 {
                        font-size: 18px;
                        line-height: 1.3;
                        margin: 0 0 15px;
                        text-transform: uppercase;
                        font-weight: 900;
                        color: @color-white;
                        letter-spacing: -.25px;
                    }
                }

                .login-row {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap;
                    margin: 0 -7.5px;

                    .login-col {
                        padding: 0 7.5px;
                    }
                }

                .signup-card {
                    background: @color-white;
                    padding: 15px;
                    height: 100%;
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap;
                    .lib-css(flex-direction, column, 1);
                    .lib-css(justify-content, space-between, 1);

                    .signup-content {
                        img {
                            max-width: 180px;
                            width: 100%;
                            height: 60px;
                            object-fit: contain;
                            margin: 0 auto 15px;
                            display: block;
                        }

                        p {
                            font-size: 16px;
                            line-height: 19px;
                            color: @color-gray20;
                            margin-bottom: 20px;
                        }

                        ul {
                            margin: 0 auto;
                            padding: 0;
                            list-style: none;

                            li {
                                font-size: 16px;
                                line-height: 19px;
                                color: @color-gray20;
                                margin-bottom: 16px;
                                position: relative;
                                padding-left: 30px;
                                display: block;
                                text-decoration: none;

                                &:last-child {
                                    margin-bottom: 0;
                                }
                            
                                &::before {
                                    content: '\e81d';
                                    font-family: 'TT Icons';
                                    font-size: 12px;
                                    line-height: inherit;
                                    width: 19px;
                                    height: 19px;
                                    border-radius: 19px;
                                    transform: rotate(90deg);
                                    background: @color-blue;
                                    color: @color-white;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                }
                            }
                        }
                    }

                    .actions-toolbar {
                        text-align: center;
                        margin-top: 20px;

                        .signup-btn {
                            background: @color-blue;
                            border: 2px solid @color-blue8;
                            color: @color-white;
                            cursor: pointer;
                            display: inline-block;
                            font-weight: 700;
                            margin: 0;
                            padding: 12px 22px;
                            font-size: 16px;
                            line-height: 1;
                            vertical-align: middle;
                            text-transform: uppercase;
                            letter-spacing: -0.26px;
                            border-radius: 4px;


                            &:hover {
                                background-color: @color-blue8;
                                border-color: @color-blue5;
                                text-decoration: none;
                            }
                        }
                    }
                }

                .benefits {
                    .customer-login-benefits-wrapper {
                        background-color: @secondary__color;
                    }
                }
            }
        }

        .block-bottom {
            background: @color-gray15;

            h4 {
                margin: 0 0 16px;
                text-transform: uppercase;
                .lib-css(letter-spacing, -0.3px);
                color: @color-gray20;
                .lib-css(font-weight, @font-weight__bold);
                line-height: 1.25;
                .lib-font-size(17);
            }

            p {
                font-size: 16px;
                line-height: 19px;
                color: @color-gray20;
                margin-bottom: 16px;
            }

            .online-register-btn {
                background: @color-blue;
                border: 2px solid @color-blue8;
                color: @color-white;
                cursor: pointer;
                display: inline-block;
                font-weight: 700;
                margin: 0;
                padding: 10px 20px;
                font-size: 16px;
                line-height: 1;
                vertical-align: middle;
                text-transform: uppercase;
                letter-spacing: -0.26px;
                border-radius: 3px;

                &:hover {
                    background-color: @color-blue8;
                    border-color: @color-blue5;
                    text-decoration: none;
                }
            }
        }
    }

    // .customer-login-benefits-wrapper {
    //     * {
    //         color: @color-white;
    //     }
    // }

    .button,
    .action {
        &.button-primary-3 {
            background: @secondary__color;
            border: 2px solid @color-blue8;
            color: @color-white;
            cursor: pointer;
            display: inline-block;
            .lib-css(font-weight, @font-weight__bold);
            margin: 0;
            padding: 10px 20px;
            .lib-font-size(14);
            .lib-line-height(14);
            text-transform: uppercase;
            .lib-css(border-radius, 3px, 1);

            &:hover {
                background: @color-blue8;
                border: 2px solid @color-blue5;
                text-decoration: unset;
            }
        }
    }

    // Forget Password Page CSS
    .customer-account-register,
    .customer-account-forgotpassword,
    .customer-account-createpassword {
        .page-title-wrapper {
            .page-title {
                margin-bottom: 30px;
                letter-spacing: -1.2px;
                text-transform: uppercase;
                .lib-css(font-weight, @font-weight__bold);
                color: @color-gray20;
                .lib-font-size(28);
                .lib-line-height(42);
            }
        }

        .main {
            .block-title {
                padding: 0 0 11px 0;
                margin: 0 0 16px 0;
                border-bottom: 1px solid @color-gray90;
                text-transform: uppercase;
                letter-spacing: -0.3px;
                color: @color-gray20;
                .lib-css(font-weight, @font-weight__bold);
                .lib-font-size(17);
                .lib-line-height(12.5);
            }

            .form {
                .fieldset {
                    padding: 0;
                    border: 0;
                    margin-bottom: 0;

                    .field {
                        margin-bottom: 20px;

                        &.note {
                            margin-bottom: 28px;
                        }

                        .label {
                            width: 100%;
                            max-width: ~"calc(100% - 35px)";
                            text-align: left;
                            display: inline-block;
                            margin-bottom: 10px;
                            color: @color-gray40;
                        }

                        &.required {
                            .label {
                                &::after {
                                    content: '*';
                                    margin-left: 2px;
                                    color: @primary__color;
                                }
                            }
                        }

                        .options,
                        &.choice {
                            input[type="checkbox"] {
                                position: absolute;
                                box-shadow: unset;

                                &:checked {
                                    &+.label {
                                        &::after {
                                            .lib-css(opacity, 1, 1);
                                            .lib-css(transform, rotate(45deg) scale(1), 1);
                                        }
                                    }
                                }
                            }

                            .label {
                                margin: 0;
                                position: relative;
                                padding-left: 30px;

                                &::before {
                                    content: '';
                                    position: absolute;
                                    left: 0;
                                    top: 2px;
                                    width: 22px;
                                    height: 22px;
                                    background: @color-white;
                                    border: 1px solid @color-gray-middle2;
                                    box-sizing: border-box;
                                    border-radius: 3px;
                                }

                                &::after {
                                    content: '';
                                    left: 8px;
                                    top: 1px;
                                    width: 4px;
                                    height: 11px;
                                    border: solid @secondary__color;
                                    border-width: 0 3px 3px 0;
                                    .lib-css(opacity, 0, 1);
                                    .lib-css(transform, rotate(45deg) scale(0), 1);
                                    position: absolute;
                                    margin: 0;
                                }
                            }
                        }
                    }

                    .field-recaptcha {
                        margin-bottom: 20px;
                    }
                }

                .actions-toolbar {
                    &>.primary {
                        display: inline-block;
                        margin-bottom: 10px;
                        text-align: left;

                        button {
                            width: auto;
                            background: @secondary__color;
                            border: 2px solid @color-blue8;
                            color: @color-white;
                            cursor: pointer;
                            display: inline-block;
                            .lib-css(font-weight, @font-weight__bold);
                            margin: 0;
                            padding: 10px 20px;
                            .lib-font-size(14);
                            .lib-line-height(14);
                            text-transform: uppercase;
                            .lib-css(border-radius, 3px, 1);

                            &:hover {
                                background: @color-blue8;
                                border: 2px solid @color-blue5;
                            }
                        }
                    }

                    &>.secondary {
                        display: inline-block;
                        padding-top: 10px;
                        margin-left: 25px;

                        a {
                            color: @secondary__color;
                        }
                    }
                }

                .required-note {
                    color: @primary__color;
                    margin: 35px 0 0;
                }
            }
        }
    }

    .customer-account-forgotpassword {
        .main {
            .form {
                .actions-toolbar {
                    &>.secondary {
                        display: none
                    }
                }
            }
        }
    }

    .customer-account-register {
        .main {
            .form {
                .fieldset {
                    .field{
                        &.terms-and-condition {
                            .options {
                                .label {
                                    &::after {
                                        top: 4px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Order Tracking Page CSS
    .login-container {
        .block-new-customer {
            background-color: @secondary__color;
            border: 0 none !important;

            * {
                color: @color-white;
            }
        }
    }

    // Customer Dashboard Pages CSS
    .account {
        .page-top-title {
            .page-title-wrapper {
                display: block;

                .page-title {
                    color: @color-gray20;
                    .lib-css(font-weight, @font-weight__bold);
                    .lib-line-height(42);
                    text-transform: uppercase;
                }
            }
        }

        .columns {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);
        }

        &.page-layout-2columns-left {
            .sidebar-main {
                order: 1;
            }

            .column {
                &.main {
                    order: 2;
                }
            }
        }

        .account-nav {
            .content {
                &#account-nav {
                    padding: 0;

                    .items {
                        margin: 0;
                        padding: 0;
                        list-style: none none;
                        border: 1px solid @color-gray80;

                        .item {
                            padding: 0;
                            margin: 0;
                            border-bottom: 1px solid @color-gray80;
                            background-color: @color-white;
                            .lib-font-size(14);
                            .lib-line-height(27);

                            &:last-child {
                                border: 0;
                            }

                            &.current {
                                position: relative;
                                box-shadow: 0 0 3px @color-black-25;

                                a,
                                strong {
                                    color: @secondary__color;
                                }

                                &::before {
                                    content: '';
                                    display: block;
                                    width: 3px;
                                    height: ~"calc(100% + 2px)";
                                    background-color: @secondary__color;
                                    position: absolute;
                                    top: -1px;
                                    left: -3px;
                                }
                            }

                            a,
                            strong {
                                color: @color-gray30;
                                padding: 8px;
                                display: inline-block;
                                text-decoration: none;
                                display: block;
                                border: 0;
                                background: @color-white;

                                &:hover {
                                    color: @secondary__color;
                                }
                            }
                        }
                    }
                }
            }
        }

        .column {
            &.main {
                padding: 20px;
                margin-bottom: 20px;
                border: 1px solid @color-gray80;
                .lib-font-size(15);

                &>.block {
                    border: 1px solid @color-gray80;
                    margin-bottom: 30px;
                    padding: 35px 30px;

                    .block-title {
                        border-bottom: 1px solid @color-gray4;
                        .lib-font-size(17);
                        padding-bottom: 10px;
                        margin-bottom: 15px;

                        strong {
                            text-transform: uppercase;
                        }

                        a.action {
                            margin-left: 20px;
                        }
                    }

                    .block-content {
                        #lib-layout-columns();

                        .box {
                            &.box-contact-information {
                                width: 100%;
                                margin-bottom: 20px;

                                &:last-child {
                                    margin-bottom: 0;
                                }
                            }

                            .box-title {
                                padding-bottom: 5px;
                                display: inline-block;
                                font-size: 18px;
                                padding-bottom: 7px;
                            }

                            p {
                                margin-bottom: 10px;
                            }

                            .box-content {
                                line-height: 22px;
                            }
                        }

                        .payment-methods-list,
                        .shipping-methods-list {
                            li {
                                .lib-font-size(15);
                                .lib-line-height(22);

                                &:last-child {
                                    margin-bottom: 0;
                                }
                            }
                        }

                        &>.tree {
                            width: 100%;

                            .jstree-container-ul {
                                margin-left: -10px;
                            }
                        }
                    }

                    &.block-dashboard-text {
                        position: relative;
                        min-height: 128px;
                        padding: 32px 35px 10px;
                        border-width: 1px;
                        border-radius: 0;
                        overflow: hidden;

                        .dashboard-image {
                            position: absolute;
                            right: 1px;
                            top: 1px;
                            bottom: 1px;
                            overflow: hidden;
                            margin: 0 auto;
                            width: 367px;
                        }

                        .triangles-banner {
                            display: inline-block;
                            width: 0;
                            height: 0;
                            border-style: solid;
                            border-width: 0 0 146px 311px;
                            border-image: url('../images/dashboard-banner.jpg') 311 auto no-repeat;
                            // background: url('../images/dashboard-banner.jpg') no-repeat scroll -20px -20px;
                        }

                        .block-title {
                            border: none;
                            padding-bottom: 0;
                            margin-bottom: 5px;
                        }

                        .block-content {
                            width: 60%;
                        }

                        .fieldset {
                            margin: 0;
                            padding: 0;
                            border: 0;
                        }
                    }

                    &.block-dashboard-info {
                        margin-bottom: 20px;

                        .block-content {
                            .box-information {
                                .box-actions {
                                    .action.change-password {
                                        display: inline-block;

                                        &:before {
                                            content: '|';
                                            display: inline-block;
                                            .lib-font-size(16);
                                            padding: 0 8px;
                                            line-height: 1.1;
                                        }
                                    }
                                }
                            }
                        }

                        br {
                            line-height: 1.5;
                        }

                        .block-title {
                            padding-bottom: 10px;
                        }

                        .referral_code_title {
                            font-weight: bold;
                        }

                        .referral_code {
                            cursor: pointer;
                            color: blue;
                            text-decoration: underline;
                        }
                    }

                    &.block-dashboard-addresses {
                        .block-content {
                            .box {
                                .box-actions {
                                    margin-top: 10px;
                                }
                            }
                        }
                    }

                    &.block-reviews-dashboard {
                        .block-content {
                            .items {
                                margin: 0;
                                padding: 0;
                                list-style: none none;

                                .item {
                                    margin-bottom: 15px;

                                    &:last-child {
                                        margin-bottom: 0;
                                    }

                                    .rating-summary {
                                        .label {
                                            font-size: 15px;
                                            display: inline-block;
                                            margin-right: 4px;
                                        }

                                        .rating-result {
                                            &:before {
                                                .lib-rating-icons-content(@_icon-content: '\e809');
                                                color: @color-yellow;
                                                .lib-css(letter-spacing, 3px);
                                                .lib-font-size(16);
                                                .lib-line-height(16);
                                                height: 16px;
                                            }

                                            span {
                                                &:before {
                                                    .lib-rating-icons-content(@_icon-content: '\e808');
                                                    color: @color-yellow;
                                                    .lib-css(letter-spacing, 3px);
                                                    .lib-font-size(16);
                                                    .lib-line-height(16);
                                                    height: 16px;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    &.block-order-details-view {
                        .block-content {
                            &>div {
                                margin-right: 15px;
                            }
                        }
                    }

                }

                .data-grid-filters-wrap {
                    .action {
                        text-shadow: none;
                        vertical-align: sub;
                        line-height: 22px;
                    }
                }

                .actions-toolbar {
                    .secondary {
                        display: none;
                    }

                    &.verification-form-actions {
                        .secondary {
                            display: block;
                            margin-top: 20px;
                        }
                    }
                }
            }
        }

        .column.main {
            .page-title {
                margin-bottom: 20px;
                margin-top: 25px;
                line-height: 1;
            }

            .actions-toolbar {
                .secondary {
                    display: none;
                }

                &.requisition-view-buttons {
                    .secondary {
                        display: block;

                        &:first-child {
                            margin-right: 10px;
                        }
                    }
                }
            }

            .orders-history {
                .table-caption {
                    text-transform: uppercase;
                }

                thead {
                    th {
                        background: @color-white-fog;
                        border: 0 !important;
                        .lib-font-size(15);
                        .lib-css(font-weight, @font-weight__bold);
                        padding: 16px 20px;
                        line-height: 1;
                    }
                }

                tbody {
                    td {
                        padding: 16px 20px;
                        border-bottom: 1px solid @color-gray4;

                        &.actions {
                            text-align: right;

                            .action {
                                &:after {
                                    content: '|';
                                    display: inline-block;
                                    padding-left: 5px;
                                    color: @color-gray20;
                                }

                                margin-right: 5px;

                                &:last-child {
                                    margin-right: 0px;

                                    &:after {
                                        display: none;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .toolbar {
                &.bottom {
                    .pager {
                        .toolbar-amount {
                            .toolbar-number {
                                color: @color-gray20;
                                font-weight: normal;
                            }
                        }
                    }
                }
            }

            .filters {
                text-align: right;
            }

            .pages {
                display: block;
                text-align: center;

                .label {
                    display: none;
                }

                .items {
                    .lib-font-size(0);
                    .lib-css(letter-spacing, -1px, 1);
                    line-height: 0;
                    white-space: nowrap;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    display: inline-block;
                    .lib-css(font-weight, @font-weight__regular);

                    .item {
                        .lib-font-size(14);
                        .lib-line-height(32);
                        margin: 0 5px 0 0;
                        display: inline-block;
                        .lib-css(font-weight, @font-weight__bold);

                        strong {
                            border: 2px solid @color-gray80;
                            .lib-font-size(14);
                            .lib-line-height(32);
                            color: @color-gray20;
                            display: inline-block;
                            .lib-css(font-weight, @font-weight__regular);
                            padding: 3px 15px;
                            .lib-css(border-radius, 2px, 1);
                            min-width: 42px;

                            .label {
                                display: none;
                            }
                        }

                        a {
                            border: 2px solid @secondary__color;
                            color: @secondary__color;
                            display: inline-block;
                            padding: 3px 15px;
                            text-decoration: none;
                            .lib-css(border-radius, 2px, 1);
                            min-width: 42px;

                            .label {
                                display: none;
                            }

                            &.action {
                                span {
                                    display: none;
                                }

                                &.previous {
                                    &:before {
                                        .lib-font-size(28);
                                        .lib-line-height(32);
                                        color: @secondary__color;
                                        content: '\e801';
                                        font-family: @font-tt-icons;
                                        vertical-align: top;
                                        .lib-css(font-weight, @font-weight__regular);
                                    }
                                }

                                &.next {
                                    &:before {
                                        .lib-font-size(28);
                                        .lib-line-height(32);
                                        color: @secondary__color;
                                        content: '\e802';
                                        font-family: @font-tt-icons;
                                        vertical-align: top;
                                        .lib-css(font-weight, @font-weight__regular);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        &.review-customer-index,
        &.vault-cards-listaction,
        &.loyalty-insider-invoices,
        &.requisition-list-requisition-index,
        &.page-multiple-wishlist,
        &.requisition-list-requisition-view {
            .column.main {
                border: 1px solid @color-gray80;
                padding: 20px;
                margin-bottom: 30px;
            }
        }

        .form-edit-account {
            .customer-dob {
                position: relative;

                input {
                    width: 100%;
                    padding-right: 35px;
                }

                button {
                    position: absolute;
                    right: 10px;
                    top: 6px;
                    padding: 5px;
                    border: none;
                    background: none;
                    box-shadow: none;

                    .lib-icon-font(@_icon-font-content: @tt-icon-calendar-alt,
                        @_icon-font-size: 16px,
                        @_icon-font-line-height: @icon-font__line-height,
                        @_icon-font-color: @secondary__color,
                        @_icon-font-color-hover: @secondary__color,
                        @_icon-font-color-active: @secondary__color,
                        @_icon-font-text-hide: true);
                }
            }

            .field {
                &.choice {
                    position: relative;
                    display: inline-block;

                    .checkbox {
                        &[type="checkbox"] {
                            margin: 0;
                            opacity: 0;
                            z-index: 1;
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 100%;
                            height: 100%;

                            &:checked {
                                &~label {
                                    &.label {
                                        &::after {
                                            opacity: 1;
                                            .lib-css(transform, rotate(45deg) scale(1), 1);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    label {
                        &.label {
                            display: inline-block;
                            max-width: unset;
                            margin: 0 !important;
                            position: relative;
                            padding-left: 32px;

                            &::before {
                                content: '';
                                position: absolute;
                                left: 0px;
                                top: -3px;
                                width: 22px;
                                height: 22px;
                                background: @color-white;
                                border: 1px solid @color-gray-middle2;
                                .lib-css(border-radius, 3px, 1);
                            }

                            &::after {
                                content: '';
                                left: 8.5px;
                                top: 0;
                                width: 4px;
                                height: 11px;
                                border: solid @secondary__color;
                                border-width: 0 3px 3px 0;
                                opacity: 0;
                                .lib-css(transform, rotate(45deg) scale(0), 1);
                                position: absolute;
                            }
                        }
                    }
                }
            }

            .fieldset {

                br {
                    display: none;
                }

                .note.warning {
                    color: @color-orange3;
                }
            }

            .field-default_customer_preferences,
            .field-default-business_account {
                .control {
                    .options {
                        position: relative;

                        .customer_preferences {
                            position: relative;
                            display: inline-block;
                            padding: 0 14px 10px 32px;
                            margin: 0;

                            input {
                                &[type="checkbox"] {
                                    margin: 0;
                                    opacity: 0;
                                    z-index: 1;
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    width: 100%;
                                    height: 100%;

                                    &:checked {
                                        &~label {
                                            &.label {
                                                &::after {
                                                    opacity: 1;
                                                    .lib-css(transform, rotate(45deg) scale(1), 1);
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            label {
                                &.label {
                                    display: inline-block;
                                    width: auto;
                                    max-width: unset;
                                    margin: 0;
                                    position: relative;
                                    padding: 0;

                                    &::before {
                                        content: '';
                                        position: absolute;
                                        left: -32px;
                                        top: -3px;
                                        width: 22px;
                                        height: 22px;
                                        background: @color-white;
                                        border: 1px solid @color-gray-middle2;
                                        .lib-css(border-radius, 3px, 1);
                                    }

                                    &::after {
                                        content: '';
                                        left: -23px;
                                        top: 0;
                                        width: 4px;
                                        height: 11px;
                                        border: solid @secondary__color;
                                        border-width: 0 3px 3px 0;
                                        opacity: 0;
                                        .lib-css(transform, rotate(45deg) scale(0), 1);
                                        position: absolute;
                                    }
                                }
                            }
                            .business_account_logo{
                                width: 200px;
                            }
                        }
                    }
                }
            }
        }

        &.loyalty-insider-info {
            .loyalty_insider_info {

                .points,
                .level {
                    line-height: 1;
                    margin-bottom: 10px;
                }
            }

            .table-wrapper {
                margin-bottom: 20px;
            }

            .orders-history {
                .table-caption {
                    line-height: 1.25;
                    margin-bottom: 15px;
                }

                .table-order-items {
                    thead {
                        tr {
                            th {
                                padding: 20px 5px;
                                line-height: 1;
                            }
                        }
                    }

                    tbody {
                        tr {
                            td {
                                padding: 25px 5px;
                                line-height: 1;
                                font-size: 14px;

                                &.col {
                                    &.value {
                                        &::before {
                                            content: '$';
                                        }
                                    }
                                }

                                &.id,
                                &.date {
                                    padding-right: 40px;
                                }

                                .selected-order-id-wrap {
                                    position: relative;
                                    width: 22px;
                                    height: 22px;

                                    .selected-order-id {
                                        opacity: 0;
                                        position: absolute;
                                        margin: 0;
                                        z-index: 2;
                                        left: 0;
                                        top: 0;
                                        width: 22px;
                                        height: 22px;
                                    }

                                    label {
                                        padding: 0;
                                        margin: 0;
                                        height: 22px;
                                        width: 22px;
                                    }
                                }

                                .action-use {
                                    &.primary {
                                        background: @color-green3;
                                        border: 2px solid @color-green;
                                        color: @color-white;
                                        cursor: pointer;
                                        display: inline-block;
                                        .lib-css(font-weight, @font-weight__bold);
                                        margin: 0;
                                        vertical-align: middle;
                                        border-radius: 3px;
                                        padding: 8px 15px 6px;
                                        .lib-font-size(16);
                                        .lib-line-height(24);
                                        text-decoration: unset;

                                        &:hover {
                                            background: @color-green2;
                                        }

                                        &[disabled] {
                                            opacity: .5;
                                            cursor: default;
                                            pointer-events: none;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .toolbar {
                    margin-top: 15px;
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap;
                    .lib-css(align-items, flex-start, 1);
                    .lib-css(justify-content, space-between, 1);
                    .lib-css(flex-direction, row-reverse, 1);
                    text-align: left;

                    &.order-products-toolbar {
                        .pager {
                            .lib-vendor-prefix-display;
                            .lib-vendor-prefix-flex-wrap;
                            .lib-css(align-items, center, 1);
                            .lib-css(justify-content, space-between, 1);
                            width: 100%;

                            .toolbar-amount {
                                margin: 0;
                            }
                        }
                    }
                }

                .toolbar-amount {
                    font-size: 13px;
                }

                .pages {
                    float: right;

                    .action {
                        text-align: center;
                    }

                    .pages-item-next {
                        margin-right: 0;
                    }
                }

                .limiter {
                    display: none;
                }
            }

            .filters {
                margin-top: 15px;
                float: right;

                .label {
                    font-size: 13px;
                    line-height: 40px;
                    margin-right: 5px;
                }

                .input-date {
                    border-radius: 3px;
                    padding: 6px 12px !important;
                    font-size: 13px !important;

                    button {
                        padding: 0;
                        border: none;
                        .lib-font-size(16px);
                        background: none;

                        .lib-icon-font(@_icon-font-content: @tt-icon-calendar-alt,
                            @_icon-font-margin: 0,
                            @_icon-font-position: after,
                            @_icon-font-line-height: 1em);

                        span {
                            font-size: 0;
                        }
                    }
                }

                &>span {
                    font-size: 33px;
                    float: left;
                    display: inline-block;
                    line-height: 1;
                }

                button.button-secondary-2 {
                    font-size: 18px;
                    line-height: 1;
                    padding: 11px 9px 8px;
                    float: right;
                    margin-left: 12px;
                }
            }
        }

        .requisition-view-links .print {
            top: -65px;
			position:relative;
            padding-left: 25px;

            &::before {
                content: "";
                background: url(../images/printerIcons.svg) no-repeat center center;
                background-size: contain;
                width: 20px;
                height: 20px;
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
            }

            span {
                font-weight: @font-weight__bold;
            }
        }

        .requisition-grid {
            .col.number {
                display: none;
            }

            .checkbox-wrap {
                position: relative;

                input[type='checkbox'] {
                    left: 0;
                    top: 0;
                    width: 24px;
                    height: 24px;
                    z-index: 4;
                    cursor: pointer;
                }

                label {
                    width: 24px;
                    height: 24px;

                    &::after {
                        z-index: 3;
                    }
                }
            }

            .item .col.col-checkbox {
				max-width: none;
				float: left;
            }

            .product-item-name {
                .lib-font-size(16);
                font-weight: @font-weight__bold;

                a {
                    color: #333;
                }
            }

            .product-item-sku,
            .product-item-details {
                color: #111;
                font-size: 14px;
            }

            .col .price {
                .lib-font-size(14);
            }

            .actions-toolbar {
                > .action-delete {
                    .lib-icon-font(
                    @_icon-font-content: '\E90B',
                    @_icon-font-size: @font-size__l,
                    @_icon-font-text-hide: true,
                    @_icon-font-color: @requisition-icons__color,
                    @_icon-font-color-hover: @primary__color,
                    @_icon-font-color-active: @requisition-icons__color
                    );
                }
            }

            .col .qty {
                width: 47px;
                text-align: center;
            }

            .product-item-image {
                max-width: 80px;
            }

            .product-item-subtotal {
                .actions-toolbar {
                    display: none;
                }
            }
        }

        .requisition-content {
            .requisition-toolbar {
                .requisition-list-action {
                    padding: 0 0 0 15px;
                }
            }
        }

        .requisition-list-action {
            .action.new {
                &:before {
                    display: none;
                }
            }
        }

        .requisition-view-buttons {
            & > .primary {
                .secondary {
                    float: right;
                    background: #fff;
                    border: 2px solid #173f91;
                    color: #2e2d76;
                    margin-left: 10px;

                    &:hover {
                        background: #3c3b9b;
                        border: 2px solid #272664;
                        color: #fff;
                    }
                }
            }
        }

        // B2B Pages CSS Start
        &.company-users-index {
            .admin__data-grid-outer-wrap {
                margin-bottom: 20px;
            }
            
            .column {
                &.main {
                    .data-grid-filters-wrap {
                        margin: 0;

                        .action { 
                            .lib-line-height(18);
                            margin-bottom: 10px;
                        }
                    }
                }
            }
        }

        &.company-role-index {
            .admin__data-grid-outer-wrap {
                margin-bottom: 20px;
            }
        }

        &.company-index-index {
            .block-dashboard-company {
                .box-actions { 
                    button {
                        &.action {
                            color: @secondary__color;

                            &:hover {
                                text-decoration: underline;
                            }
                        }
                    }
                }
            }

            .manage-company-accounts {
                .block {
                    .block-title {
                        border-bottom: 1px solid @color-gray4;
                        .lib-font-size(17);
                        padding-bottom: 10px;
                        margin: 0 0 15px;
                    }
                }
            }
        }

        .jstree {
            li {
                a {
                    height: auto;

                    &.jstree-clicked {
                        .field-tooltip-content {
                            color: @color-white;
                        }
                    }
                }
            }

            & > ul {
                & > li {
                    & > {
                        a.company-admin {
                            padding-bottom: 0.4rem;
                        }
                    }
                }
            }
        }

        &.company-role-edit {
            .column {
                &.main {
                    .form-edit-role {
                        .fieldset {
                            margin-bottom: 25px;

                            .box-actions {
                                button {
                                    &.action {
                                        color: @secondary__color;

                                        &:hover {
                                            text-decoration: underline;
                                        }
                                    }
                                }
                            }

                            .legend {
                                border-bottom: 1px solid @color-gray4;
                                .lib-font-size(17);
                                font-weight: 700;
                                padding: 0 0 10px;
                                margin: 0px 0 20px 0;
                                width: 100%;
                            }

                            .field {
                                margin-bottom: 20px;

                                .label {
                                    width: 100%;
                                    display: inline-block;
                                    margin-bottom: 10px;
                                    color: @color-gray40;
                                }

                                &:last-child {
                                    margin-bottom: 0;
                                }
                            }
                        }
                    }
                }
            }
        }

        &.company-profile-index {
            .page-main {
                .page-title-wrapper {
                    .page-title {
                        display: block !important;
                        margin-right: 0 !important;
                    }
                }
            }
        }
        // B2B Pages CSS End
    }

    .block-requisition-management {
        .requisition-list-title h1 {
            display: inline-block;
            font-weight: 700;
            margin-bottom: 0;
            margin-right: 10px;
        }

        .requisition-toolbar{
            display: flex;
            align-items: center;

            .requisition-toolbar-actions {
                display: flex;
                align-items: center;

                .remove-selected,
                .requisition-list-button {
                    line-height: 1.25;
                    padding: 0;
                    color: #2e2d76;
                    text-decoration: none;
                    background: 0 0;
                    border: 0;
                    display: inline;
                    border-radius: 0;
                    font-size: inherit;
                    font-weight: 400;

                    &.toggle {
                        .lib-vendor-prefix-display;
                        .lib-css(align-items, center, 1);

                        span {
                            .lib-line-height(22);
                        }

                        .lib-icon-font(
                            @_icon-font-content: @tt-icon-down-dir,
                            @_icon-font-size: 14px,
                            @_icon-font-line-height: 22px,
                            @_icon-font-position: after
                        );

                        &.active {
                            .lib-icon-font(
                                @_icon-font-content: @tt-icon-up-dir,
                                @_icon-font-size: 14px,
                                @_icon-font-line-height: 22px,
                                @_icon-font-position: after
                            );
                        }

                        &:after {
                            margin: 0 0 0 5px;
                        }
                    }
                }

                .mobile-label {
                    display: none;
                }
            }
        }

        .requisition-toolbar-select {
            margin-right: 30px;
        }
	}

    .requisition-toolbar-select #uniform-requisition-select-all > span {
        margin-top: 3px;
    }

    .account .ui-hidden,
    .checkout-index-index .ui-hidden,
    .pca .ui-hidden {
        display: none !important;
    }

    .loyalty-insider-info {
        .form-sms-verification {
            padding: 0 0 20px;
            border: 0;

            .fieldset {
                border: 0;
                margin: 0;
                padding: 0;

                &>.legend {
                    width: 100%;
                    padding: 0 0 16px;
                    margin: 10px 0 16px;
                    border-bottom: 1px solid @color-gray90;
                    text-transform: uppercase;
                    color: @color-gray20;
                    .lib-css(font-weight, @font-weight__bold);
                    .lib-font-size(17);
                }
            }
        }

        .actions-toolbar {
            padding-top: 20px;
            padding-bottom: 20px;
        }

        .page-title-wrapper {
            .page-title {
                margin-bottom: 5px;
            }
        }

        .invoice-view-all {
            // float: right;
            margin-top: 6px;
            font-weight: @font-weight__bold;
            .lib-line-height(20);
            .lib-font-size(14);
            text-decoration: underline;
        }

        .insider-description {
            margin: 0 0 30px;
            .lib-font-size(15);
            line-height: 28px;
        }

        .trade-reward-banner {
            margin-bottom: 15px;
            .lib-font-size(0);
            padding: 2.5px 0px;
            background: @secondary__color;
            position: relative;
            clear: both;

            .trade-reward-inner {
                overflow: hidden;
                z-index: 1;
                width: 100%;
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;

                &::before {
                    content: "";
                    background: @primary__color;
                    right: -10px;
                    top: 0;
                    width: 20%;
                    height: 100%;
                    position: absolute;
                    z-index: 1;
                    transform: skewX(-15deg);
                }

                &::after {
                    content: "";
                    background: @color-yellow;
                    right: -10px;
                    top: 0;
                    width: 25px;
                    height: 100%;
                    position: absolute;
                    z-index: 1;
                    transform: skewX(-15deg);
                }
            }

            img {
                max-width: 135.65px;
                top: -7px;
                margin-left: auto;
                display: block;
                margin-right: 21%;
                margin-bottom: -7px;
                position: relative;
            }
        }

        .loyalty_insider_reward {
            margin-bottom: 40px;
        }

        h2 {
            text-transform: uppercase;
        }

        .loyalty_insider_reward_header {
            margin: 20px 0;

            h2 {
                font-weight: @font-weight__bold;
                .lib-line-height(28);
                .lib-font-size(17);
                text-transform: uppercase;
            }
        }

        .loyalty-insider-wrapper {
            text-align: center;
        }

        .actions a.action {
            text-decoration: underline;
        }

        .loyalty_insider_info {
            border: 1px solid @color-gray80;
            padding: 25px 25px 15px;
            margin-bottom: 20px;

            .loyalty_insider_header {
                padding-bottom: 15px;
                border-bottom: 1px solid @color-gray4;
                margin-bottom: 15px;
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                .lib-css(align-items, center, 1);
                .lib-css(justify-content, space-between, 1);

                h2 {
                    margin: 0;
                    .lib-font-size(17);
                    .lib-line-height(28);
                    font-weight: @font-weight__bold;
                }

                .loyalty_header_right {
                    color: @secondary__color;
                    .lib-font-size(14);
                    font-weight: @font-weight__semibold;
                    margin-top: 2px;
                    .lib-icon-font(@_icon-font-content: @tt-icon-info,
                        @_icon-font-margin: -2px 8px 0 0,
                        @_icon-font-size: 22px);
                }
            }

            .under {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                .lib-css(align-items, flex-start, 1);
                .lib-css(justify-content, space-between, 1);
            }

            .loyalty_insider_points_content_point {
                color: @secondary__color;
                .lib-font-size(68);
                letter-spacing: -1px;

                .currency-symbol,
                .price-decimal {
                    .lib-font-size(28);
                    vertical-align: super;
                    position: relative;
                    top: -2px;
                    font-weight: @font-weight__bold;
                }

                .decimal-dot {
                    font-size: 20%;
                    line-height: inherit;
                    color: transparent;
                    vertical-align: super;
                }
            }

            .left-info {
                width: 100%;

                .loyalty_insider_points_sign {
                    .lib-font-size(24);
                    line-height: 1;
                    vertical-align: super;
                    position: relative;
                    top: -24px;
                    font-weight: 700;
                    margin-left: 2px;
                    color: @color-blue;
                }
            }

            .right-info {
                .lib-font-size(19);
                .lib-line-height(25);
                margin-top: 7px;
                width: 100%;

                span {
                    text-transform: uppercase;
                }

                .loyalty_insider_points_note {
                    font-size: 14px;
                    line-height: 1.25;
                    display: inline-block;
                    margin: 5px 0 0;
                }
            }

            .points,
            .level {
                margin-bottom: 5px;
                .lib-font-size(18);
                .lib-line-height(27);
                font-weight: @font-weight__bold;
                // span {
                //     color: @text-focus-color;
                // }
            }
        }

        .filters {
            .input-date {
                display: inline-block;
                box-sizing: border-box;
                padding: 3px 10px;
                margin: 0 5px;

                input {
                    width: 86px;
                    border: 0;
                    outline: 0;
                    outline-style: none;
                    box-shadow: none;
                    color: @color-gray-light6;

                    &:focus {
                        outline: 0;
                        outline-style: none;
                        box-shadow: none;
                    }
                }

                border: 1px solid @color-gray4;
            }

            .hasDatepicker+.ui-datepicker-trigger:before {
                .lib-font-size(18);
                .lib-line-height(18);
            }
        }

        img {
            display: inline;
        }

        .loyalty_member_image {
            max-width: 250px;
            display: block;
            margin: 0 auto;
        }
    }

    .loyalty-insider-info,
    .newsletter-manage-index,
    .customer-address-form,
    .customer-address-edit,
    .customer-account-index,
    .customer-account-edit {

        .form-address-edit,
        .form-edit-account,
        .form-newsletter-manage {
            padding: 30px;
            border: 1px solid @color-gray80;

            .fieldset {
                >.legend {
                    width: 100%;
                    padding: 0 0 16px 0;
                    margin: 10px 0 16px 0;
                    border-bottom: 1px solid @color-gray90;
                    text-transform: uppercase;
                    .lib-typography(@_color: @color-gray20,
                        @_font-size: 17px,
                        @_font-weight: @font-weight__bold,
                        @_line-height: @line-height__base);
                }

                .field {
                    .label {
                        width: 100%;
                        max-width: ~"calc(100% - 35px)";
                        display: inline-block;
                        margin-bottom: 10px;
                        color: @color-gray40;
                    }

                    &.choice {
                        &:before {
                            display: none;
                        }

                        .label {
                            width: auto;
                            margin-left: 5px;
                        }
                    }
                }
            }

            .actions-toolbar {
                margin-left: 0 !important;

                .primary {
                    .action.primary {
                        .lib-font-size(14);
                        .lib-line-height(14);
                        padding: 10px 20px;
                        background: @secondary__color;
                        border: 2px solid @color-blue2;
                        color: @color-white;
                        .lib-css(border-radius, 3px, 1);
                        text-transform: uppercase;

                        &:hover {
                            background: @secondary__color;
                            border: 2px solid @color-blue2;
                            color: @color-white;
                        }
                    }
                }
            }
        }

        .privacy_block {
            padding-top: 10px;
        }
    }

    .loyalty-insider-info {
        &.loyalty-insider-invoices {
            .column {
                &.main {
                    padding: 20px 30px !important;
                }
            }
        }
    }

    .customer-account-edit {
        .column.main {
            .form-edit-account {
                padding: 35px 30px;

                .fieldset {
                    &._more-profiles {
                        margin-bottom: 20px;
                    }

                    .legend {
                        padding: 0 0 10px;
                        margin: 0px 0 20px 0;
                    }

                    &.info {
                        margin-bottom: 25px;
                    }
                }

                .privacy_block {
                    margin: 0;
                }
            }
        }
    }

    .loyalty-insider-info,
    .customer-account-index,
    .customer-account-edit,
    .customer-address-form,
    .company-profile-index {
        .column {
            &.main {
                border: 0;
                padding: 0 !important;
            }
        }
    }

    #deselect_all_invoices {
        display: none;
    }
    .loyalty-insider-invoices {
        .selected-orders-action-buttons .button3 {
            margin-right: 5px;
            margin-bottom: 10px;
            font-weight: @font-weight__regular;
            .lib-css(border-radius, 3px, 1);
            background: @secondary__color;
            border: 2px solid @color-blue5;
            color: @color-white;
            text-decoration: unset;

            &:hover {
                background: @color-blue7;
            }
        }

        .actions a.action {
            text-decoration: underline;
        }

        .column.main .filters {
            .lib-vendor-prefix-display();
            .lib-css(align-items, flex-end, 1);
            text-align: left;

            .date-box {
                width: 145px;
            }

            .date-text {
                line-height: 42px;
                padding: 0 10px;
            }

            .input-date {
                margin: 0;
                width: 100%;
                text-align: right;

                button {
                    float: left;
                    color: @secondary__color;
                    .lib-font-size(22);
                    line-height: 1;
                }

                input {
                    .lib-font-size(16);
                    line-height: 1.5;
                    padding: 0;
                    margin: 0;
                }
            }

            .label {
                line-height: 1.75;
                .lib-font-size(16);
            }
        }
    }

    .review-customer-index {
        .main {
            .reviews {
                margin-bottom: 20px;

                .table-reviews {
                    .table-caption {
                        display: none
                    }
                }
            }
        }
    }

    .products-reviews-toolbar,
    .quotes-grid-toolbar {
        .pager {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            .lib-css(align-items, flex-start, 1);
            .lib-css(justify-content, space-between, 1);
        }

        .toolbar-amount {
            display: block;
            .lib-line-height(26);
            margin: 0;
            padding: 6px 0 0;
            vertical-align: middle;
            .lib-font-size(18);
            margin-right: 16px
        }

        .limiter {
            .limiter-label {
                .lib-css(font-weight, @font-weight__regular);
            }

            .limiter-options {
                height: 32px;
                .lib-line-height(32);
                .lib-font-size(14);
                margin: 0 0 0 7px;
            }
        }
    }

    .quotes-grid-toolbar {
        .lib-vendor-prefix-display;
        .lib-vendor-prefix-flex-wrap;
        .lib-css(align-items, flex-start, 1);
        .lib-css(justify-content, space-between, 1);

        .pager {
            display: inline-block;
        }
    }

    .data-grid-wrap .list-name-field .cell-label-line-name {
        font-size: 20px;
        .lib-css(font-weight, @font-weight__bold);
    }

    // Orders Address Page CSS
    .customer-address-form {
        .column.main {
            border: 0;
            padding: 0 !important;

            .form-address-edit {
                padding: 17px;
                border: 1px solid @color-gray80;

                .fieldset {
                    .lib-css(display, flex, 1);
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(justify-content, space-between, 1);

                    .legend {
                        padding: 0 0 10px;
                        margin: 0px 0 20px 0;
                    }

                    .field {
                        width: 100%;
                        margin-bottom: 20px;

                        &.field-name-firstname {
                            width: 49%;
                            margin-right: 2%;
                        }

                        &.field-name-lastname {
                            width: 49%;
                        }

                        .control {
                            width: 100%;
                        }

                        .selected-address {
                            margin-bottom: 0;
                        }

                        &.choice {
                            position: relative;

                            input[type="checkbox"] {
                                position: absolute;
                                left: 0;
                                top: 0;
                                opacity: 0;
                                z-index: 1;

                                &:checked {
                                    & ~ .label {
                                        &::after {
                                            opacity: 1;
                                            .lib-css(transform, rotate(45deg) scale(1), 1);
                                        }
                                    }
                                }
                            }

                            .label {
                                position: relative;
                                padding-left: 34px;
                                cursor: pointer;
                                display: inline-block !important;
                                .lib-font-size(16);
                                .lib-line-height(24);

                                &::before {
                                    content: '';
                                    position: absolute;
                                    left: 0;
                                    top: -1px;
                                    width: 22px;
                                    height: 22px;
                                    background: @color-white;
                                    border: 1px solid @color-gray-middle2;
                                    .lib-css(border-radius, 3px, 1);
                                }

                                &::after {
                                    content: '';
                                    left: 8px;
                                    top: 2px;
                                    width: 4px;
                                    height: 11px;
                                    border: solid @secondary__color;
                                    border-width: 0 3px 3px 0;
                                    opacity: 0;
                                    .lib-css(transform, rotate(45deg) scale(0), 1);
                                    position: absolute;
                                }
                            }
                        }
                    }
                }

                .actions-toolbar {
                    .lib-css(display, flex, 1);
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, center, 1);
                    .lib-css(justify-content, space-between, 1);

                    .secondary {
                        display: inline-block;
                    }
                }
            }

            .block-addresses-default,
            .block-addresses-list {
                padding: 17px;
                border: 1px solid @color-gray80;
                margin-bottom: 20px;
            }

            .actions-toolbar {
                .lib-css(display, flex, 1);
                .lib-vendor-prefix-flex-wrap(wrap);
                .lib-css(align-items, center, 1);
                .lib-css(justify-content, space-between, 1);

                .secondary {
                    display: inline-block;
                }
            }

            &>.actions-toolbar {
                .primary {
                    .action {
                        background: @color-green3;
                        border: 2px solid @color-green;
                        color: @color-white;
                        cursor: pointer;
                        .lib-css(font-weight, @font-weight__bold);
                        padding: 7px 15px;
                        .lib-font-size(16);
                        .lib-line-height(22);
                        .lib-css(border-radius, 3px, 1);

                        &:hover {
                            background: @color-green2;
                        }
                    }
                }

                .secondary {
                    display: none;
                }
            }
        }
    }

    .customer-address-index {
        .columns {
            .column {
                &.main {
                    padding: 0 !important;
                    border: 0;
                }
            }
        }

        .actions-toolbar {
            .action {
                &.primary {
                    background: @color-green3;
                    border: 2px solid @color-green;
                    color: @color-white;
                    cursor: pointer;
                    display: inline-block;
                    .lib-css(font-weight, @font-weight__bold);
                    margin: 0;
                    padding: 8px 15px 6px;
                    .lib-font-size(16);
                    .lib-line-height(22);
                    border-radius: 3px;

                    &:hover {
                        background: @color-green2;
                    }
                }
            }
        }
    }

    // Store payment Detail Page CSS
    .my-credit-cards {
        &.table-wrapper {
            margin-bottom: 20px;

            .table-caption {
                display: none;
            }

            .table-credit-cards {
                thead {
                    background-color: #f8f8f8;
                    tr {
                        th {
                            .lib-css(font-weight, @font-weight__bold);
                            padding: 8px 10px;
                        }
                    }
                }

                tr {
                    td {
                        border: 0;
                        padding: 8px 10px;

                        &.card-type {
                            &::before {
                                display: block;
                            }
                        }

                        .action {
                            &.delete {
                                color: @secondary__color;
                            }
                        }
                    }
                }
            }
        }
    }

    .customer-addresses-toolbar {
        .pager {
            text-align: left !important;
        }

        .toolbar-amount {
            display: inline-block;
            .lib-line-height(26px);
            margin: 0;
            padding: 6px 0 0;
            vertical-align: middle;
            .lib-font-size(18);
            margin-right: 16px;
        }

        .limiter {
            margin: 0;
            display: block;
        }

        .limiter-options {
            margin: 0 5px 0 7px;
            height: 32px;
            line-height: 32px;
            margin: 0 0 0 7px;
        }
    }

    .additional-addresses {
        overflow-x: auto;

        &.table-wrapper {
            margin-bottom: 20px;

            .table-caption {
                display: none;
            }

            .table-additional-addresses-items {
                thead {
                    tr {
                        th {
                            .lib-css(font-weight, @font-weight__bold);
                            border-bottom: 1px solid @color-gray80;
                            padding: 8px 10px;
                        }
                    }
                }

                tr {
                    td {
                        border: 0;
                        padding: 8px 10px;

                        &.card-type {
                            &::before {
                                display: block;
                            }
                        }

                        .action {
                            &.delete {
                                color: @secondary__color;
                            }
                        }
                    }
                }
            }
        }
    }

    // wishlist Page CSS
    .abs-dropdown-simple,
    .giftregisty-dropdown,
    .wishlist-dropdown {
        display: inline-block;
        position: relative;

        .action {
            &.toggle {
                cursor: pointer;
                display: inline-block;
                text-decoration: none;

                &::after {
                    font-size: 26px;
                    line-height: inherit;
                    color: inherit;
                    content: '\e800';
                    font-family: 'TT Icons';
                    vertical-align: middle;
                    display: inline-block;
                    font-weight: 400;
                    text-align: center;
                }

                &.active {
                    &::after {
                        content: '\e803';
                    }
                }
            }
        }

        ul {
            &.dropdown {
                margin: 0;
                padding: 0;
                list-style: none none;
                background: @color-white;
                border: 1px solid @color-gray-light2;
                margin-top: 4px;
                min-width: 200px;
                z-index: 5;
                box-sizing: border-box;
                display: none;
                position: absolute;
                top: 100%;
                box-shadow: 0 3px 3px @color-black-15;

                &::before,
                &::after {
                    border-bottom-style: solid;
                    content: '';
                    display: block;
                    height: 0;
                    position: absolute;
                    width: 0;
                }

                &:before {
                    left: 10px;
                    top: -12px;
                    border: 6px solid;
                    border-color: transparent transparent @color-white;
                    z-index: 4;
                }

                &:after {
                    left: 9px;
                    top: -14px;
                    border: 7px solid;
                    border-color: transparent transparent @color-gray-light2;
                    z-index: 3;
                }

                li {
                    margin: 0;
                    padding: 5px 5px 5px 23px;

                    &:hover {
                        background: @color-gray91;
                        cursor: pointer;
                    }

                    span {
                        font-size: 1.5rem;
                    }
                }
            }
        }

        &.active {
            ul {
                &.dropdown {
                    display: block;
                }
            }
        }
    }

    .wishlist-index-index {
        .block-wishlist-management {
            position: relative;
            margin-top: 15px;

            .wishlist-title {
                margin-bottom: 12px;

                strong {
                    letter-spacing: -0.9px;
                }
            }

            .wishlist-select {
                font-weight: 400;
                margin-bottom: 25px;

                .wishlist-name {
                    padding-left: 0;
                    padding-bottom: 5px;
                    padding-top: 5px;
                    line-height: 1;
                    max-width: 94px;

                }

                .wishlist-name-label {

                    font-weight: 400;
                    font-size: 18px;
                }
            }

            .wishlist-select-items {
                padding-left: 0;

                .item {
                    margin-bottom: 0;

                    &.wishlist-add {
                        a {
                            background-image: none;
                            background: @color-white;
                            border: 2px solid @color-blue2;
                            color: @secondary__color;
                            cursor: pointer;
                            display: inline-block;
                            .lib-css(font-weight, @font-weight__bold);
                            margin: 0;
                            .lib-font-size(14);
                            .lib-line-height(14);
                            box-sizing: border-box;
                            vertical-align: middle;
                            text-transform: uppercase;
                            .lib-css(border-radius, 3px, 1);

                            &:hover {
                                background: @color-blue7;
                                border: 2px solid @color-blue5;
                                color: @color-white;
                                text-decoration: unset;
                            }
                        }
                    }
                }
            }

            .private {
                &::before {
                    background-image: url(../images/sprite.png);
                    background-position-x: -3px;
                    background-position-y: -2px;
                    line-height: 16px;
                    vertical-align: middle;
                    height: 16px;
                    width: 13px;
                    background-repeat: no-repeat;
                    content: '';
                    display: inline-block;
                    margin: 0 5px 5px 0;
                }
            }
        }

        .wishlist-toolbar {
            .pager {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                .lib-css(align-items, flex-start, 1);
                .lib-css(justify-content, space-between, 1);

                .toolbar-amount {
                    display: block;
                    .lib-line-height(26);
                    margin: 0;
                    padding: 6px 0 0;
                    vertical-align: middle;
                    .lib-font-size(18);

                    .toolbar-number {
                        color: @secondary__color;
                        .lib-css(font-weight, @font-weight__bold);
                    }
                }

                .limiter-options {
                    width: auto;
                    height: 32px;
                    line-height: 32px;
                }
            }
        }

        .wishlist-dropdown ul.dropdown {
            text-align: left;
        }

        .products-grid {
            .product-items {
                width: 100%;
                margin: 0;
                padding: 0;
                list-style: none none;
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;

                .product-item {
                    position: relative;
                    margin-bottom: 20px;
                    display: inline-block;

                    .product-item-info {
                        position: relative;
                        text-align: center;
                        border-color: transparent;
                        display: flex;
                        flex-direction: column;
                        .lib-vendor-prefix-display;
                        .lib-vendor-prefix-flex-wrap;
                        .lib-css(flex-direction, column, 1);

                        &:hover {
                            border-color: @secondary__color;
                        }

                        .product-item-photo {
                            order: 1;
                            width: 100%;
                        }

                        .product-item-name {
                            min-height: 56px;
                            width: 100%;
                            display: block;
                            margin: 0;
                            font-size: 14px;
                            line-height: 1;
                            .lib-css(font-weight, @font-weight__bold);
                            order: 3;

                            .product-item-link {
                                color: @color-gray20;
                            }
                        }

                        &>.product-item-tooltip {
                            display: none;
                        }

                        .price-box {
                            order: 4;
                        }

                        .product-item-checkbox {
                            float: none;
                            width: 24px;
                            height: 24px;
                            margin: 0 auto;
                            order: 5;
                            top: 0;
                            opacity: 1;
                            position: static;
                        }

                        .product-reviews-summary {
                            top: auto;
                            .lib-css(transform, unset, 1);
                            order: 2;
                            margin-top: -20px;
                            position: static;
                        }

                        .product-item-inner {
                            order: 6;
                            z-index: 1;
                            .lib-css(box-shadow, 0);
                            border: 2px solid @secondary__color;
                            border-top: 0;
                            margin-top: 0;
                            border-radius: 3px !important;

                            .comment-box {
                                .label {
                                    display: none;
                                }

                                .control {
                                    textarea {
                                        height: 47px;
                                        line-height: 1;
                                        color: @color-gray40;
                                        font-size: 14px;

                                    }

                                    textarea::-moz-placeholder {
                                        color: @color-gray40;
                                    }
                                }
                            }

                            .field.qty {
                                .lib-css(width, 65px) !important;

                                .label {
                                    .lib-clearfix();
                                    float: left;
                                    width: 100% !important;
                                    text-align: left !important;
                                    padding-top: 0;
                                    font-weight: 400;
                                    color: @color-gray40;
                                    line-height: 1;
                                    font-size: 14px;
                                    margin: 6px 0 4px;
                                }

                                .control {
                                    input[type="number"] {
                                        width: 47px;
                                        height: 39px;
                                        text-align: center;
                                    }
                                }
                            }

                            .product-item-actions {
                                .action {
                                    &.button-primary-2 {
                                        background: @color-green3;
                                        border: 2px solid @color-green;
                                        color: @color-white;
                                        padding: 0px 20px;
                                        .lib-font-size(14);
                                        .lib-line-height(14);
                                        .lib-css(border-radius, 3px, 1);
                                        height: 39px;

                                        &:hover {
                                            background: @color-green2;
                                        }
                                    }
                                }

                                a,
                                .wishlist-dropdown .action.toggle {
                                    float: left;
                                    color: @secondary__color;
                                    font-size: 12px;
                                    display: inline-block;
                                }

                                .edit {
                                    border-right: 1px solid @color-gray80;
                                    padding-right: 10px;
                                    margin-right: 10px;
                                }

                                .wishlist-dropdown {
                                    margin-right: 7px;

                                    .action.toggle {

                                        &.active:after,
                                        &:after {
                                            color: @secondary__color;
                                            font-size: 18px;
                                        }
                                    }

                                    &.copy {
                                        margin-right: 0;
                                    }

                                    .items {
                                        .item {
                                            .action:after {
                                                display: none !important;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }

        .wishlist-toolbar-actions {
            .wishlist-dropdown {

                &.move,
                &.copy {
                    .items.dropdown .item {
                        padding-bottom: 0;
                    }

                    .action.new {
                        display: inline-block;
                        line-height: 28px;

                        &:before {
                            margin-top: 3px;
                        }
                    }
                }
            }
        }

        .actions-toolbar {
            margin-left: 0 !important;

            .primary {
                float: unset;
                margin-top: 30px;
                margin-bottom: 50px;
                margin-right: 12px;
                padding-left: 15px;
                padding-right: 15px;
                letter-spacing: -0.5px;
                margin-right: 10px;

                .action {
                    background: @color-white;
                    border: 2px solid @color-blue2;
                    color: @secondary__color;
                    cursor: pointer;
                    display: inline-block;
                    .lib-css(font-weight, @font-weight__bold);
                    margin: 0;
                    padding: 10px 15px;
                    .lib-font-size(14);
                    .lib-line-height(14);
                    vertical-align: middle;
                    text-transform: uppercase;
                    .lib-css(border-radius, 3px, 1);
                    margin-right: 10px;
                    float: unset;

                    &:hover {
                        background: @color-blue7;
                        border: 2px solid @color-blue5;
                        color: @color-white;
                    }

                    &.share {
                        display: none;
                    }

                    &.button-primary-2 {
                        background: @color-green3;
                        border: 2px solid @color-green;
                        color: @color-white;

                        &:hover {
                            background: @color-green2;
                        }
                    }
                }
            }

            .secondary {
                display: none;
            }
        }

        .modal-popup {
            &.confirm {
                .modal-footer {
                    button {
                        cursor: pointer;
                        display: inline-block;
                        .lib-css(font-weight, @font-weight__bold);
                        margin: 0;
                        padding: 6px 12px;
                        font-size: 1.4rem;
                        line-height: 1.4rem;
                        box-sizing: border-box;
                        vertical-align: middle;
                        text-shadow: 1px 1px 2px @color-black-45;

                        &.action-primary {
                            background: @color-green3;
                            border: 2px solid @color-green;
                            color: @color-white;

                            &:hover {
                                background: @color-green2;
                            }
                        }

                        &.action-secondary {
                            background: @color-red2;
                            border: 2px solid @color-red;
                            color: @color-white;
                        }
                    }
                }
            }
        }
    }

    .form.wishlist {
        .add-to-links {
            margin-bottom: @indent__base;
        }

        .product-item-comment {
            .label {
                display: block;
            }
        }
    }

    .wishlist-index-configure {
        .product-options-wrapper {
            .fieldset {
                .legend {
                    margin: @indent__base 0;
                }
            }
        }
    }

    .wishlist.window.popup {
        width: 450px !important;
        top: 20%;
        left: 50%;
        margin-left: 0 !important;
        transform: translateX(-50%);
        right: auto;
        bottom: auto;
        max-width: 100%;

        &.active {
            z-index: 999;
        }

        .popup-actions {
            .action {
                &.close {
                    &:before {
                        .lib-font-size(16);
                        .lib-line-height(16);
                        color: #8f8f8f;
                    }
                    &:hover {
                        &::before {
                            color: #b5150f;
                        }
                    }
                }
            }
        }

        .popup-content {
            .fieldset {

                input[type="text"],
                input[type="password"],
                input[type="url"],
                input[type="tel"],
                input[type="search"],
                input[type="number"],
                input[type="datetime"],
                input[type="email"] {
                    display: inline-block;
                    cursor: pointer;
                    vertical-align: top;
                }

                .field {
                    &.name {
                        display: flex;
                        flex-wrap: wrap;
                        margin-bottom: 15px;

                        .label {
                            width: 140px;
                            text-align: left;
                            display: inline-block;
                        }

                        .control {
                            width: calc(~"100% - 140px");
                            display: inline-block;
                        }
                    }

                    &.choice {
                        margin: 0 0 5px;

                        &:before {
                            width: 140px;
                        }
                    }
                }

                .actions-toolbar {
                    button {
                        .lib-font-size(14);
                        .lib-line-height(14);
                        border-radius: 3px;
                    }

                    .primary {
                        margin: 0;
                        float: right;
                        padding: 0;

                        .action {
                            background: @color-green3;
                            border: 2px solid @color-green;
                            color: @color-white;

                            &:hover {
                                background: @color-green2;
                            }
                        }
                    }

                    .secondary {
                        float: left;
                        display: inline-block;

                        .action {
                            background: @color-gray95;
                            border: 1px solid @color-gray-darken2;
                            padding: 7px 15px;
                            color: @color-gray20;

                            &:hover {
                                background: @color-gray-light5;
                            }
                        }
                    }

                    .save {
                        padding: 6px 10px;
                    }
                }
            }
        }
    }

    .wishlist-dropdown {
        display: inline-block;
    }

    // Insider Rewards Page CSS
    .modals-wrapper {
        .insider-modal {
            &.overview-modal {
                .modal-inner-wrap {
                    .modal-footer {
                        button {
                            text-transform: uppercase;
                        }
                    }
                }
            }
        }
    }

    @media(max-width: 1090px) {
        .wishlist-index-index {
            .block-wishlist-management {
                .wishlist-toolbar {
                    float: left !important;
                    margin-top: 10px;
                }
            }
        }
    }

    @media only screen and (min-width: 768px) and (max-width: 800px) {
        .products-grid.wishlist .product-item .box-tocart .actions-primary button.button-primary-2 {
            padding-left: 5px !important;
            padding-right: 5px !important;
        }
    }

    @media only screen and (max-width: 330px) {
        .wishlist-index-index {
            .products-grid {
                .product-items {
                    .product-item {
                        .product-item-info {
                            .product-item-inner {
                                .field.qty {
                                    width: 55px !important;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .insider-barcode {
        margin: 0 auto 32px auto;
        float:left;
        width: 100%;
    }

    .insider-barcode img {
        margin: 0 auto;
        display: block;
        min-width: 250px;
        min-height: 142px;
    }

    .barcode-id {
        text-align: center;
    }

    .loyalty-insider-info .loyalty-insider-wrapper{
        clear:both;
    }

    .buy-keyword-index {
		.page-main {
			.columns {
				.column {
					&.main {
						.category-short-description {
							h3 {
								margin-top: 0
							}
						}
					}
				}
			}
		}
	}

    @media only screen and (max-width: 374px) {
        .loyalty-insider-info {
            .loyalty_insider_info {
                .loyalty_insider_points_content_point {
                    .lib-font-size(58);
                    letter-spacing: -1.5px;

                    .currency-symbol,
                    .price-decimal {
                        .lib-font-size(22);
                        top: -2px;
                        letter-spacing: -1px;
                    }
                }
                .left-info {
                    .loyalty_insider_points_sign {
                        .lib-font-size(22);
                        top: -20px;
                        margin-left: 0;
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .customer-commercial-account,
    .customer-commercial-success,
    .customer-store-account,
    .customer-store-success,
    .storelocator-commercial-account,
    .customer-account-create {
        .page-title-wrapper {
            .page-title {
                .lib-font-size(22);
                .lib-line-height(22);
                border-bottom: 1px solid @color-gray80;
                margin-bottom: 20px;
                padding-bottom: 15px;
            }
        }

        .customer-register-wrapper {
            padding-bottom: 30px;

            .customer-register-form-wrapper {
                order: 2;
                margin-bottom: 20px;

                .block {
                    .block-box-wrapper {
                        padding: 28px;
                    }
                }
            }

            .customer-register-benefits-wrapper {
                order: 1;

                .block-top {
                    padding: 0;
                    border: 0 none;
                    margin-bottom: 20px;

                    .block-title {
                        margin-bottom: 20px !important;

                        strong {
                            .lib-font-size(18);

                            &::before {
                                .lib-font-size(22);
                            }
                        }
                    }

                    p {
                        margin-bottom: 15px;
                    }

                    ul {
                        li {
                            margin-bottom: 6.5px;
                        }
                    }
                }
            }

            .block {
                .block-box-wrapper {
                    .form {
                        &.form-create-account {
                            .fieldset {
                                .field {
                                    width: 100%;

                                    &.field-name-firstname,
                                    &.field-name-lastname {
                                        width: 49%;
                                    }

                                    .control {
                                        width: 100%;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .block-bottom {
                order: 3;
            }
        }
    }

    .customer-account-create {
        .customer-register-wrapper {
            padding-bottom: 0;
        }
    }

    // login Page CSS
    .customer-account-login {
        .page-title-wrapper {
            .page-title {
                .lib-font-size(22);
                .lib-line-height(22);
                border-bottom: 1px solid @color-gray80;
                margin-bottom: 20px;
                padding-bottom: 15px;
            }
        }

        .main {
            padding-bottom: 30px;

            .login-container {
                .block {

                    &.block-customer-login,
                    &.block-new-customer {
                        padding: 25px;
                        margin-bottom: 20px;
                        width: 100%;

                        .form {
                            &.form-login {
                                .required-note {
                                    margin: 7px 0 0;
                                }

                                .fieldset {
                                    .field {
                                        margin-bottom: 25px;
                                    }
                                }
                            }
                        }
                    }
                }

                .login-row {
                    .login-col {
                        width: 50%;
                    }
                }
            }

            .benefits {
                .customer-login-benefits-wrapper {
                    padding: 20px;
                }
            }
        }

        .block-bottom {
            padding: 20px 25px; 
            margin: 0 -25px;
        }
    }

    // Forget Password Page CSS
    .customer-account-register,
    .customer-account-forgotpassword,
    .customer-account-createpassword {
        .page-title-wrapper {
            .page-title {
                .lib-line-height(28) !important;
                padding-bottom: 15px;
                margin-bottom: 15px !important;
                border-bottom: 1px solid @color-gray80;
            }
        }

        .main {
            .block-title {
                display: none;
            }
        }

        .columns {
            .column {
                &.main {
                    padding-bottom: 25px;
                }
            }
        }
    }

    // Customer Dashboard Pages CSS
    .account {
        .page-main {
            padding-left: 20px;
            padding-right: 20px;
            padding-top: 55px;
            position: relative;
        }

        .page-top-title {
            .page-title-wrapper {
                .page-title {
                    margin: 10px 0 20px;
                    padding-bottom: 15px;
                    .lib-font-size(24);
                    border-bottom: 1px solid @color-gray80;
                }

                .main-description {
                    margin-bottom: 20px;
                }
            }
        }

        .sidebar {
            &.sidebar-main {
                position: absolute;
                padding: 0;
                overflow: unset;
                z-index: 9;
                right: auto;
                left: 0;
                min-width: 100%;
                height: auto;
                background-color: @color-white;
                width: 100%;
                top: 0;
            }
        }

        .account-nav {
            position: relative;

            .title {
                border-bottom: 1px solid @color-gray80;
                .lib-line-height(48);
                padding: 0 20px;
                margin-bottom: 0;

                &.active {
                    &::after {
                        content: '--';
                        letter-spacing: -3px;
                        .lib-line-height(45);
                    }
                }

                &::after {
                    content: '+';
                    color: @secondary__color;
                    float: right;
                    .lib-font-size(35);
                    .lib-line-height(50);
                }

                strong {
                    .lib-css(font-weight, @font-weight__regular);
                }
            }

            .content {
                background: @color-white-fog;
                position: absolute;
                width: 100%;
                padding: 20px !important;
                border: 1px solid @color-gray80;
                box-sizing: border-box;
                z-index: 2;
                display: none;

                &.active {
                    display: block;
                }
            }
        }

        &.loyalty-insider-info {
            .orders-history {
                .table-order-items {
                    thead {
                        display: none;
                    }

                    tbody {
                        tr {
                            border-bottom: 1px solid @color-gray4;

                            td {
                                padding: 9px 0 !important;
                                font-size: 16px;
                                display: block;
                                border: 0;

                                &:first-child {
                                    padding-top: 20px !important;

                                    &::before {
                                        font-size: 0;
                                        display: none;
                                    }
                                }

                                &::before {
                                    padding-right: 10px;
                                    content: attr(data-th) ': ';
                                    display: inline-block;
                                    color: @color-gray16;
                                    .lib-css(font-weight, @font-weight__regular) !important;
                                    width: 50%;
                                }

                                &.actions {
                                    text-align: left !important;
                                    font-size: 15px;
                                    padding: 10px 0 20px !important;

                                    &::before {
                                        display: none;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }


        // B2B Pages CSS Start
        .jstree {
            overflow-x: auto;
            max-width: calc(100vw - 82px);
        }
        // B2B Pages CSS End 
    }

    .customer-account-edit {
        .column.main {

            .form-edit-account {
                padding: 17px !important;
            }
        }
    }

    .account {
        .loyalty-insider-info {
            &.loyalty-insider-invoices {
                .column {
                    &.main {
                        padding: 17px !important;
                    }
                }
            }
        }
    }
    .customer-account-index {
        .page-title-wrapper {
            .page-title {
                margin-top: 12px !important;
            }

            .main-description {
                margin-bottom: 15px;
                line-height: 20px;
            }
        }

        .column.main {
            padding-bottom: 55px;

            .block {
                padding: 17px !important;
                margin-bottom: 20px;

                .block-title {
                    line-height: 1.25;
                    padding-bottom: 10px !important;
                    margin-bottom: 20px;

                    a.action {
                        margin-left: 20px;
                    }
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .block-content {
                .box {
                    .lib-layout-column(1, 1, 100%);
                    margin-bottom: 20px;

                    &:last-child {
                        margin-bottom: 0 !important;
                    }
                }
            }

            .block-dashboard-text {
                // display: none;
            }
        }
    }

    .customer-address-index {
        .customer-account-index;
    }

    #deselect_all_invoices {
        display: none;
    }
    .loyalty-insider-info,
    .loyalty-insider-invoices {

        .selected-orders-action-buttons {
            width: 100%;

            .button3 {
                width: 100%;
                margin: 10px 0 0 !important;
            }
        }

        .column {
            &.main {
                .filters {
                    width: 100%;
                    .lib-vendor-prefix-flex-wrap;
                    float: unset !important;

                    .date-box {
                        width: 100% !important;
                        margin-bottom: 15px;
                    }

                    .date-text {
                        display: none;
                        line-height: 28px !important;
                        padding: 0 !important;

                        &.clear-filters {
                            display: inline-block;
                        }
                    }
                }
            }
        }
    }

    // Customer Dashboard Review Pages CSS
    .review-customer-index {
        .main {
            .reviews {
                margin-bottom: 0;

                .table-reviews {
                    tr {
                        border-bottom: 1px solid @color-gray4;
                        margin-bottom: 20px;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }

            .products-reviews-toolbar {
                .limiter {
                    display: none;
                }
            }
        }
    }

    .customer-address-form {
        .column {
            &.main {
                .block-content {
                    .box {
                        width: 100%;
                        margin-bottom: 20px;

                        &:last-child {
                            margin: 0;
                        }
                    }
                }

                &>.actions-toolbar {
                    .primary {
                        width: 100%;

                        .action {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }

    // wishlist Page CSS
    .products-grid.wishlist {
        .product-item {
            .product-reviews-summary {
                position: static;
                .lib-css(transform, translate(0, 0), 1);
            }

            .product-item-actions {
                float: none;
            }

            .price-box {
                .lib-css(margin, @indent__s 0);
                .lib-css(justify-content, flex-start, 1);
            }
        }
    }

    .cart.table-wrapper {
        .action-towishlist {
            .lib-icon-font(@icon-wishlist-full,
                @_icon-font-size: 18px,
                @_icon-font-line-height: 20px,
                @_icon-font-text-hide: true,
                @_icon-font-color: @minicart-icons-color,
                @_icon-font-color-hover: @primary__color,
                @_icon-font-color-active: @minicart-icons-color );
        }
    }

    .wishlist-index-index {
        .block-collapsible-nav {
            z-index: 880;
        }

        .block-wishlist-management {
            margin: 0 -20px;

            .wishlist-select {
                margin: 0 0 20px !important;
                display: table;
                width: 100%;
                border-bottom: 1px solid @color-gray91;

                .wishlist-name {
                    display: none;
                }
            }

            .wishlist-select-items {
                letter-spacing: 0;
                background: none;
                border: none;
                display: table-cell;
                padding: 0 20px !important;
                vertical-align: top;

                .item {
                    padding: 5px 0 0;
                    line-height: 0.7;
                    letter-spacing: -0.4px;
                    font-weight: 400;
                    margin-bottom: 0;
                    display: inline-block;

                    &.current {
                        line-height: 1.7 !important;
                        padding-bottom: 0 !important;
                        border-bottom: 4px solid @color-red5;
                        display: inline-block !important;
                    }

                    a {
                        position: relative;
                        padding: 5px 18px;
                        text-decoration: none;
                    }

                    &.wishlist-add {
                        float: right;
                        padding: 0;
                    }
                }
            }

            .wishlist-title {
                padding: 0 20px;

                &>strong {
                    font-size: 24px;
                    text-transform: uppercase;
                    letter-spacing: -1.35px;
                }

                &>a.action {
                    font-size: 15px;
                    float: right;
                    line-height: 2.2;
                }
            }

            .wishlist-info,
            .wishlist-toolbar {
                display: none;
            }
        }

        .products-grid {
            .product-items {
                .product-item {
                    padding: 0;
                    margin-left: 0;
                    width: ~"calc((100% - 0%) / 3)";

                    .product-item-info {
                        border: none !important;

                        .product-item-name {
                            margin-left: 0 !important;
                        }

                        .product-item-inner {
                            border: 0 !important;
                            padding: 0 10px;
                        }

                        .price-box {
                            margin: 0;
                        }

                        .product-item-checkbox {
                            position: static;
                        }

                        .box-tocart {
                            margin-right: 0;
                            width: 100%;

                            .fieldset {
                                .lib-vendor-prefix-display;
                            }

                            .field.qty {
                                display: block;
                                margin: 0;
                                padding-right: 15px;

                                .label {
                                    line-height: 1;
                                }
                            }

                            .product-item-actions {
                                margin: 0;
                            }

                            button.button-primary-2 {
                                margin-top: 25px;
                                padding-left: 12px;
                                padding-right: 12px;
                            }
                        }

                        .wishlist-dropdown {
                            display: block !important;
                        }
                    }
                }
            }

        }

        .actions-toolbar {
            .primary {
                .action {
                    margin-right: 0;
                }

                button {
                    float: left;
                    width: auto;
                    display: inline-block;
                }
            }
        }
    }

    .form.wishlist {
        .products {
            margin-top: 0;
        }

        .products-list {
            .products.list.items {
                .product-item-info {
                    display: block;

                    &:hover {
                        .product-item-inner {
                            z-index: 11;
                        }
                    }
                }

                .product-item-photo {
                    .lib-css(box-sizing, border-box, 1);
                    display: inline-block;
                    padding-right: 20px;
                    width: 110px;
                }

                .product-item-details {
                    display: block;
                    padding-bottom: 10px;
                    width: auto;
                }

                .right-info {
                    clear: both;
                    display: block;
                    padding: 0;
                    width: 100%;
                    .lib-css(box-sizing, border-box, 1);

                    .more-info-wrapper {
                        width: 100%;
                    }
                }
            }
        }
    }

    .wishlist {
        &.window {
            &.popup {
                position: absolute !important;

                .popup-content {
                    .fieldset {
                        .field {
                            &.name {
                                .control {
                                    width: 100%;
                                }
                            }
                        }
                    }

                    .primary {
                        padding-left: 0;
                    }
                }
            }
        }
    }

    .has-strength-meter {
        .control {
            #password-strength-meter-container {
                .lib-css(align-items, flex-start, 1);
                .lib-css(flex-direction, column, 1);
            }
        }
    }

    // Standard Order Popup
    .modals-wrapper {
        .modal-popup {
            &.requisition-popup {
                left: 44px !important;
            }
        }
    }

    .insider-barcode {
        display: flex;
        align-items: center;

        .box-barcode {
            img {
                min-width: auto;
                min-height: auto;
            }
        }
    }

    .box-barcode {
        float: left;
        width: 50%;
    }

    .buy-keyword-index {
		.page-main {
			&>.page-title-wrapper {
				.page-title {
                    font-weight: 700;
                    border-bottom: 1px solid #ccc;
					margin: 0;
                    text-transform: uppercase;
				}
			}

            .category-short-description {
                margin: 15px 0 20px;
            }
		}
	}

    .account {
        .requisition-grid {
            margin: 0;

            .data-table {
                display: block;
                border: none;

                thead {
                    display: none;
                }

                tbody {
                    width: 100%;
                    display: block;

                    tr {
                        display: block;

                        td {
                            border: 0;
                            display: block;
                            padding: 5px 0;

                            &:first-child {
                                padding: 5px 10px 0 0 !important;
                            }

                            &.col-checkbox {
                                &::before {
                                    display: none;
                                }
                            }

                            &:last-child {
                                padding-bottom: 20px;
                            }

                            &::before {
                                padding-right: 10px;
                                content: attr(data-th) ': ';
                                display: inline-block;
                                color: #111;
                                font-weight: 700;
                            }

                            &.product {
                                &::before {
                                    display: none;
                                }
                            }

                            .qty {
                                height: 2.6rem;
                                line-height: 2.4rem;
                            }

                            .price {
                                .lib-font-size(16);
                            }
                        }
                    }
                }
            }

            .product-item-image {
                display: none;
            }

            .product-item-subtotal {
                display: inline;
            }
        }
    }

    .requisition-list-title.page-title-wrapper {
        .page-title {
            margin: 10px 0 20px;
            padding-bottom: 15px;
            .lib-font-size(24);
            border-bottom: 1px solid #ccc;
            font-weight: 700;
            text-transform: uppercase;
        }
    }

    .requisition-content {
        .block-requisition-management {
            .requisition-info {
                float: unset;
                margin-bottom: 1.5rem;
            }

            .requisition-toolbar-select {
                margin: 0 0 5px;
                padding-right: 24px;
                width: 24px;
                height: 24px;

                .label {
                    font-size: 0;
                    padding: 0;

                    &:after {
                        z-index: 1;
                        font-size: 15px;
                    }
                }
            }

            .requisition-toolbar-actions {
                justify-content: space-around;
                width: 100%;
                margin-bottom: 0;

                .block-requisition-list {
                    padding: 0;
                    display: block;
                }
            }
        }
    }

    .actions-toolbar {
        &.requisition-view-buttons {
            .primary {
                button {
                    width: 100%;
                    margin-bottom: 10px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {

    // Store payment Detail Page CSS
    .my-credit-cards {
        &.table-wrapper {
            overflow-x: auto;
            overflow-y: hidden;
            width: 100%;
            position: relative;

            .table-credit-cards {
                border: none;
                display: block;

                thead {
                    display: none;
                }

                tbody {
                    display: block;

                    tr {
                        display: block;

                        td {
                            border-bottom: none;
                            display: block;
                            padding: 5px 0;

                            &:first-child {
                                padding-top: 20px;
                            }

                            &:last-child {
                                padding-bottom: 20px;
                            }

                            &::before {
                                padding-right: 10px;
                                content: attr(data-th) ': ';
                                display: inline-block;
                                color: @color-gray16;
                                .lib-css(font-weight, @font-weight__bold);
                            }
                        }
                    }
                }
            }
        }
    }

    .additional-addresses {
        &.table-wrapper {
            overflow-x: auto;
            overflow-y: hidden;
            width: 100%;
            position: relative;

            .table-additional-addresses-items {
                border: none;
                display: block;

                thead {
                    display: none;
                }

                tbody {
                    display: block;

                    tr {
                        display: block;

                        td {
                            border-bottom: none;
                            display: block;
                            padding: 5px 0;

                            &:first-child {
                                padding-top: 20px;
                            }

                            &:last-child {
                                padding-bottom: 20px;
                            }

                            &::before {
                                padding-right: 10px;
                                content: attr(data-th) ': ';
                                display: inline-block;
                                color: @color-gray16;
                                .lib-css(font-weight, @font-weight__bold);
                            }
                        }
                    }
                }
            }
        }
    }

    // Customer Dashboard Review Pages CSS
    .review-customer-index {
        .main {
            .table-reviews {
                border: none;
                display: block;

                thead {
                    display: none;
                }

                tbody {
                    display: block;

                    tr {
                        display: block;

                        td {
                            border: 0;
                            display: block;
                            padding: 0 0 10px;

                            &::before {
                                padding-right: 10px;
                                content: attr(data-th) ': ';
                                display: inline-block;
                                color: @color-gray16;
                                .lib-css(font-weight, @font-weight__bold);
                            }
                        }
                    }
                }
            }
        }
    }

    // Standard Order Page CSS
    .data-grid-wrap {
        &.table-wrapper {
            overflow-x: auto;
            overflow-y: hidden;
            width: 100%;
            position: relative;

            .data-grid {
                border: none;
                display: block;

                thead {
                    display: none;
                }

                tbody {
                    display: block;

                    tr {
                        display: block;

                        td {
                            border: 0;
                            display: block;
                            padding: 5px 0;

                            &:first-child {
                                padding-top: 20px;
                            }

                            &:last-child {
                                padding-bottom: 20px;
                            }

                            &::before {
                                padding-right: 10px;
                                content: attr(data-th) ': ';
                                display: inline-block;
                                color: @color-gray16;
                                .lib-css(font-weight, @font-weight__bold);
                            }
                        }
                    }
                }
            }
        }
    }

    // wishlist Page CSS
    .wishlist.window.popup {
        width: 300px;
        left: 0;
        right: 0;

        .popup-content {
            .fieldset {
                .field {
                    &.name {
                        .control {
                            width: 100% !important;
                            display: block !important;
                        }
                    }
                }

                .actions-toolbar {
                    .primary,
                    .secondary,
                    .action {
                        margin-bottom: 5px !important;
                        width: 100%;
                    }
                }
            }
        }
    }

    .wishlist-index-index {
        .block-wishlist-management {
            margin-top: 0 !important;
        }

        .products-grid.wishlist {
            margin: 0;

            .product-item {
                border-top: 1px solid @color-gray4;
                padding: 0 !important;
                margin: 0 !important;
                width: 100%;

                .product-item-info {
                    border: none !important;
                    padding: 20px 0 16px;
                    position: relative;
                    text-align: left !important;
                    float: left;
                    width: 100%;

                    .product-item-photo {
                        max-width: 80px;
                        margin-left: 0 !important;
                        float: left;
                        margin-right: 20px;
                        position: absolute;
                        left: 0;
                        top: 20px;

                        .amasty-label-conatiner {
                            display: none !important;
                        }
                    }

                    .product-item-name {
                        padding-left: 100px !important;
                        font-size: 14px;
                        min-height: auto !important;
                        float: none;
                        width: auto;
                        order: 2 !important;
                    }

                    .product-reviews-summary {
                        margin-left: 100px;
                        order: 3 !important;
                        margin: 0 0 0 100px !important;
                    }

                    .price-box {
                        min-height: unset !important;
                    }

                    .checker,
                    .product-item-checkbox {
                        display: none;
                    }

                    .product-item-inner {
                        border: none !important;
                        padding: 0;

                        .comment-box {
                            .control textarea {
                                height: 70px;
                            }
                        }

                        .field.qty {
                            float: left;

                            .label {
                                line-height: 1 !important;
                            }
                        }
                    }

                    .box-tocart {
                        float: left;
                        margin-right: 20px;
                        width: 100%;

                        .product-item-actions {
                            width: auto;
                            display: inline-block !important;

                            button.button-primary-2 {
                                margin-top: 25px;
                                padding-left: 12px;
                                padding-right: 12px;
                            }
                        }
                    }

                    .product-item-actions {
                        float: left;
                        width: 100%;
                        position: relative;
                        display: block !important;

                        .wishlist-dropdown {
                            display: inline-block !important;
                            line-height: 1;

                            .action {
                                &.toggle {
                                    margin: 0;
                                }
                            }

                            .items {
                                .item {
                                    .action {
                                        &::before {
                                            display: none !important;
                                        }
                                    }
                                }
                            }
                        }

                        &>a {

                            padding-right: 10px;

                            &.action {
                                margin: 0;

                                .edit {
                                    right: 62px;
                                    padding-right: 6px;
                                }
                            }
                        }
                    }
                }

                &:last-child {
                    border-bottom: 1px solid @color-gray4;
                }
            }
        }

        .form-wishlist-items {
            .actions-toolbar {
                .primary {
                    padding-left: 0;
                    padding-right: 0;
                    margin-right: 0;
                    margin-top: 10px;
                    margin-bottom: 0;
                    width: 100%;

                    button {
                        width: 100%;
                        letter-spacing: -.1px;
                        padding: 10px 15px;
                        margin: 0 0 10px;

                        &:last-child {
                            margin: 0;
                        }
                    }
                }
            }
        }

        .product-item {
            width: 100%;

            .product-item-info {
                width: auto;
            }
        }

        .wishlist-toolbar {
            .pager {
                .lib-css(flex-direction, column, 1);
                .lib-css(justify-content, flex-start, 1);
            }
        }

        .main {
            padding-bottom: 20px !important;
        }
    }

    .products-grid.wishlist {
        .product-item {

            .product-item-name,
            .product-item-description,
            .price-box,
            .product-item-tooltip {
                margin-left: 95px;
            }

            .box-tocart {
                .stock {
                    margin-top: 7px;
                }

                .product-item-actions {
                    button.button-primary-2 {
                        height: 39px;
                    }
                }
            }

            .giftregisty-dropdown {
                display: none;
            }
        }
    }

    .buy-keyword-index {
		.breadcrumbs {
			display: none;
		}
	}

    .orders-history {
        &.table-wrapper {
            overflow-x: auto;
            overflow-y: hidden;
            width: 100%;
            position: relative;

            .table-order-items {
                &.history {
                    border: none;
                    display: block;

                    thead {
                        display: none;
                    }

                    tbody {
                        display: block;

                        tr {
                            display: block;

                            td {
                                border-bottom: none;
                                display: block;
                                padding: 5px 0;

                                &:first-child {
                                    padding-top: 20px;
                                }

                                &:last-child {
                                    padding-bottom: 20px;
                                }

                                &::before {
                                    padding-right: 10px;
                                    content: attr(data-th) ': ';
                                    display: inline-block;
                                    color: @color-gray16;
                                    .lib-css(font-weight, @font-weight__bold) !important;
                                    width: auto;
                                }
                            }
                        }
                    }

                    tfoot {
                        display: block;

                        tr {
                            display: block;

                            &:first-child {
                                th {
                                    padding-top: 20px;
                                    border-top: 1px solid #ccc;
                                }
                            }

                            th {
                                box-sizing: border-box;
                                float: left;
                                padding-left: 0;
                                padding-right: 0;
                                text-align: left;
                                width: 70%;

                                &.mark {
                                    font-weight: 400;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    // login Page CSS
    .customer-account-login {
        .main {
            .login-container {
                .login-row {
                    .login-col {
                        width: 100%;
                        margin-bottom: 15px;

                        &:last-child {
                            margin-bottom: 0
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .customer-commercial-account,
    .customer-commercial-success,
    .customer-store-account,
    .customer-store-success,
    .storelocator-commercial-account,
    .customer-account-create {
        .page-title-wrapper {
            .page-title {
                .lib-font-size(28);
                .lib-line-height(42);
                margin: 0 0 10px;
            }
        }

        .customer-register-wrapper {
            padding-bottom: 40px;

            .customer-register-form-wrapper {
                width: 50%;
                padding-right: 8px;

                .block {
                    .block-box-wrapper {
                        padding: 37px 30px 44px;
                        margin-bottom: 30px;
                    }
                }
            }

            .customer-register-benefits-wrapper {
                width: 50%;
                padding-left: 8px;

                .block-top {
                    border: 5px solid @secondary__color;
                    padding: 20px;
                    margin-bottom: 15px;

                    .block-title {

                        strong {
                            .lib-font-size(28);

                            &::before {
                                .lib-font-size(32);
                            }
                        }
                    }
                }
            }

            .unlock-signUp-img {
                margin: 15px 0 0;

                img {
                    &.unlock-mobile-img {
                        display: none;
                    }

                    &.unlock-desktop-img {
                        display: block;
                    }
                }
            }

            .block {
                .block-box-wrapper {
                    .form {
                        &.form-create-account {
                            .fieldset {
                                .field {
                                    width: 100%;

                                    .control {
                                        width: 100%;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .customer-account-create {
        .customer-register-wrapper {
            padding-bottom: 0;
            .customer-register-form-wrapper {
                .block {
                    .block-box-wrapper {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }

    // login Page CSS
    .customer-account-login {
        .page-title-wrapper {
            .page-title {
                .lib-font-size(28);
                .lib-line-height(42);
                margin: 0 0 10px;
            }
        }

        .main {
            padding-bottom: 40px;

            .login-container {
                .block {

                    &.block-customer-login,
                    &.block-new-customer {
                        width: ~"calc(50% - 8px)";
                        margin-right: 8px;
                        padding: 37px 30px 44px;

                        &:last-child {
                            margin-left: 8px;
                            margin-right: 0;
                        }

                        .form {
                            &.form-login {
                                .required-note {
                                    margin: 40px 0 0;
                                }

                                .fieldset {
                                    .field {
                                        margin-bottom: 20px;
                                    }
                                }
                            }
                        }
                    }

                    &.block-customer-login {
                        .form {
                            &.form-login {
                                .required-note {
                                    margin: 10px 0 20px;
                                }
                            }
                        }
                    }
                }
            }

            .login-container {
                .login-row {
                    .login-col {
                        width: 100%;
                        margin-bottom: 20px;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }

            .benefits {
                width: 50%;
                padding-left: 8px;

                .customer-login-benefits-wrapper {
                    padding: 20px;
                }
            }
        }

        .block-bottom {
            padding: 20px 30px; 
            margin: 0 -30px;
        }
    }

    // Forget Password Page CSS
    .customer-account-register,
    .customer-account-forgotpassword,
    .customer-account-createpassword {
        .columns {
            .column {
                &.main {
                    padding-bottom: 40px;
                }
            }
        }

        .main {
            .form {
                .fieldset {
                    .field {
                        .control {
                            width: 60%;
                        }
                    }
                }
            }
        }
    }

    // Customer Dashboard Pages CSS
    .account {
        .page-top-title {
            .page-title-wrapper {
                margin-bottom: 30px;

                .page-title {
                    .lib-font-size(28);
                    margin: 30px 0 5px;
                }

                .main-description {
                    margin-bottom: 0;
                }
            }
        }

        &.page-layout-2columns-left {
            .sidebar-main {
                width: 20%;
                padding-right: 1.6%;
                margin: 0;
            }

            .column {
                &.main {
                    width: 80%;
                }
            }
        }

        .account-nav {
            .title {
                &.account-nav-title {
                    display: none;
                }
            }
        }

        .column {
            &.main {
                &>.block {
                    &.block-dashboard-info {
                        .box-information {
                            width: 53%;
                            margin-bottom: 0;
                        }

                        .box-role,
                        .box-newsletter {
                            width: 47%;
                        }
                    }

                    &.block-dashboard-addresses {
                        .box-billing-address {
                            width: 53%;
                        }

                        .box-shipping-address {
                            width: 47%;

                        }
                    }
                }
            }
        }
    }

    .form-edit-account {
        fieldset {
            &.fieldset {
                width: 48%;
                float: left;
                margin: 0;

                br {
                    display: none;
                }

                .fieldset {
                    width: 100%;
                }

                &.info {
                    &._left {
                        margin-right: 4%;
                    }
                }

                &.password {
                    margin-bottom: 30px
                }
            }
        }
    }

    .account {

        &.loyalty-insider-info,
        &.loyalty-insider-invoices {
            .orders-history {
                .toolbar {
                    .lib-css(flex-direction, column, 1);
                }

                .pages {
                    float: none;
                    margin-bottom: 15px;
                }
            }
        }
    }

    // Customer Dashboard Review Pages CSS
    .review-customer-index {
        .page-title-wrapper {
            .page-title {
                margin-bottom: 10px;
            }
        }

        .main {
            .table-reviews {
                &>thead {
                    background-color: @color-white-fog;

                    &>tr {
                        &>th {
                            padding: 20px 25px;
                            border-bottom: none;
                        }
                    }
                }

                &>tbody {
                    &>tr {
                        &>td {
                            padding: 25px;
                            border-bottom: 1px solid @color-gray4;
                            vertical-align: middle;
                        }
                    }
                }
            }
        }
    }

    // Store payment Detail Page CSS
    .customer-address-form {
        .column.main {
            .form-address-edit {
                padding: 35px 30px;
                margin-bottom: 30px;

                .fieldset {

                    .field {
                        &.field.company {
                            width: 49%;
                            margin-right: 2%;
                        }

                        &.telephone {
                            width: 49%;
                        }
                    }
                }
            }

            .block-addresses-default,
            .block-addresses-list {
                padding: 35px 30px;
                margin-bottom: 30px;
            }

            .block-content {
                .box {
                    width: 50%;
                }
            }
        }
    }

    .my-credit-cards {
        &.table-wrapper {
            .table-credit-cards {
                thead {
                    tr {
                        th {
                            padding: 20px 25px;
                        }
                    }
                }

                tr {
                    td {
                        padding: 25px;
                    }
                }
            }
        }
    }

    .customer-address-index {
        .block {
            .block-content {
                .box-address-billing,
                .box-address-shipping {
                    width: 50%;
                }
            }
        }
    }

    // wishlist Page CSS
    .products-grid.wishlist {
        .product-item {
            .product-item-info {
                &:hover {
                    .product-item-inner {
                        display: block;
                    }
                }

                .price-box {
                    clear: both;
                }
            }

            .product-item-tooltip {
                display: inline-block;
            }

            .product-item-actions {
                margin: @indent__s 0 0;
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start;

                &>* {
                    display: inline-block;
                    margin-bottom: 7px;
                    margin-top: 7px;
                }

                .wishlist-dropdown {
                    .items {
                        .item {
                            .action:before {
                                display: none !important;
                            }
                        }
                    }
                }
            }

            .fieldset {
                display: table;

                .field.qty,
                .product-item-actions {
                    display: table-cell;
                    vertical-align: bottom;
                }

                .field.qty {
                    padding-right: 15px;

                }
            }

            .box-tocart {
                .actions-primary {
                    margin: 0;

                    button.button-primary-2 {
                        height: 39px;
                        padding-left: 12px;
                        padding-right: 12px;
                    }
                }

                .stock {
                    margin: @indent__base 0 0;
                }
            }

            .tocart {
                width: auto;
            }
        }
    }

    .wishlist-index-index {
        .product-item-info {
            width: auto;
        }

        .block-wishlist-management {
            .wishlist-select-items {
                .item {

                    &.wishlist-add {
                        position: absolute;
                        right: 0;
                        top: -15px;

                        a {
                            padding: 10px 20px;
                        }
                    }
                }
            }

            .wishlist-info {
                .lib-font-size(15);
                float: unset;

                .wishlist-notice {
                    color: @color-gray1;
                    display: inline-block;
                    margin-right: 20px;
                }
            }

            .wishlist-toolbar {
                .lib-font-size(15);
            }
        }

        .products-grid {
            .product-items {
                .product-item {
                    .product-item-info {
                        .product-item-inner {
                            .product-item-actions {
                                .wishlist-dropdown {
                                    float: unset;
                                    margin-right: 25px;
                                }
                            }
                        }
                    }
                }
            }
        }

        .product-item-inner {
            .lib-css(background, @color-white);
            border-top: none;
            .lib-css(box-shadow, 0);
            display: none;
            left: 0;
            margin: 0px 0 0 -2px;
            padding: 0 0 15px;
            position: relative;
            border: 0 !important;
            right: -2px;
            z-index: 2;

            .comment-box {
                margin: 5px 0 20px;
            }
        }
    }

    .wishlist-shared-index {
        #maincontent {
            form {
                .actions-toolbar {
                    margin-left: 0;
                }
            }
        }

        .actions-toolbar {
            .primary {
                float: right;
            }

            .secondary {
                float: left;
            }
        }
    }

    // Insider RRewards Page CSS
    .modals-wrapper {
        .insider-modal {
            &.overview-modal {
                .modal-inner-wrap {
                    width: 75% !important;
                }
            }
        }
    }

    // Standard Order Popup
    .modals-wrapper {
        .modal-popup {
            &.requisition-popup {
                .modal-inner-wrap {
                    width: 768px !important;
                }
            }
        }
    }

    .buy-keyword-index {
		.page-main {
			&>.page-title-wrapper {
                display: none;

				.page-title {
					margin: 10px 0;
				}
			}

			.columns {
				.column {
					&.main {
						.category-short-description {
                            display: none;
							margin-bottom: 10px;
						}
					}
				}
			}
		}
        
        .instant-search-sidebar {
            margin-top: 20px;
        }
	}

    .requisition-list-title.page-title-wrapper{
        margin-bottom: 30px;

        .page-title {
            margin: 30px 0 5px;
            font-weight: 700;
            display: inline;
            text-transform: uppercase;
        }
    }

    .requisition-view-buttons {
        & > .primary {
            display: block;
            float: none;
            text-align: left;
        }
    }

    .block-requisition-management {
        .requisition-info {
            float: left;
        }

        .requisition-toolbar {
            float: right;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .customer-commercial-account,
    .customer-commercial-success,
    .customer-store-account,
    .customer-store-success,
    .storelocator-commercial-account,
    .customer-account-create {
        .customer-register-wrapper {
            .block-title {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                .lib-css(justify-content, space-between, 1);

                .required-note {
                    margin-top: 0 !important;
                }
            }
        }

        .customer-register-wrapper {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;

            .block {
                .block-box-wrapper {

                    .form {
                        &.form-create-account {
                            .fieldset {
                                .lib-vendor-prefix-display;
                                .lib-vendor-prefix-flex-wrap;
                                .lib-css(justify-content, space-between, 1);

                                .field {
                                    width: 49%;

                                    &.note {
                                        width: 100%;
                                        color: @color-gray40;
                                        .lib-font-size(16);
                                    }

                                    .control {
                                        width: 100%;
                                    }

                                    &.field-default_terms_and_conditions {
                                        .label {
                                           
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .unlock-info-img {
                img {
                    &.desktop-img {
                        display: block;
                    }

                    &.mobile-img {
                        display: none;
                    }
                }
            }
        }
    }
    
    .unlocked_text {
        div {
            display: flex;
            flex-wrap: nowrap;
            align-items: flex-start;
            gap: 15px;
        }

        .unlock-img {
            margin-bottom: 0px;
        }
    }
    // login Page CSS
    .customer-account-login {
        .main {
            .login-container {
                .block-title {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap;
                    .lib-css(justify-content, space-between, 1);

                    .required-note {
                        margin-top: 0 !important;
                    }
                }

                .block {
                    &.block-customer-login {
                        .form {
                            &.form-login {
                                .fieldset {
                                    .field {
                                        .control {
                                            width: 60%;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .login-row {
                    .login-col {
                        width: 50%;
                        margin: 0;
                    }
                }
            }
        }
    }



    // Forget Password Page CSS
    .customer-account-forgotpassword,
    .customer-account-createpassword {
        .main {
            .form {
                width: 50%;
            }
        }
    }

    .customer-account-register {
        .main {
            .register-container {
                .block {
                    &.block-customer-register {
                        width: 50%;
                    }
                }
            }
        }
    }


    // Customer Dashboard Pages CSS
    .loyalty-insider-info {
        .loyalty-insider-wrapper {
            .lib-vendor-prefix-display();
            text-align: left;

            .loyalty_insider_info {
                width: 49%;
                box-sizing: border-box;
                margin-right: 0;
                margin-left: 0;
                margin-bottom: 20px;

                &._member {
                    margin-right: 2%;
                }
            }

            .under {
                .left-info {
                    width: 50%;
                    margin: 0;
                    padding-right: 20px;

                    &.loyalty_insider_points_content_left {
                        padding-right: 10px;
                    }
                }

                .right-info {
                    width: 50%;
                }
            }
        }
    }

    .account {

        &.loyalty-insider-info,
        &.loyalty-insider-invoices {
            .orders-history {
                .table-order-items {
                    thead {
                        tr {
                            th {
                                padding-left: 25px;
                                padding-right: 25px !important;
                            }
                        }
                    }

                    tbody {
                        tr {
                            td {
                                padding-left: 25px;
                                padding-right: 25px !important;
                            }
                        }
                    }
                }

                .toolbar {
                    .lib-vendor-prefix-flex-wrap;
                    .lib-css(flex-direction, row-reverse, 1);
                }

                .pages {
                    margin-bottom: 0;
                }
            }
        }
    }

    .loyalty-insider-info {
        .loyalty_insider_info {
            .loyalty_insider_points_content_point {
                .lib-font-size(40);
                letter-spacing: -2px;
                .currency-symbol,
                .price-decimal {
                    .lib-font-size(20);
                    top: 0;
                    letter-spacing: -1px;
                }
            }
            .left-info {
                .loyalty_insider_points_sign {
                    .lib-font-size(20);
                    line-height: 1;
                    top: -8px;
                    margin-left: 0;
                }
            }
        }
    }

    // wishlist Page CSS
    .wishlist-index-index {
        .column.main {
            margin-bottom: 120px;
        }

        .products-grid {
            .product-items {
                margin: 0;
            }

            .product-item {
                margin-bottom: @indent__base;
                margin-left: calc(~'(100% - 4 * 24.439%) / 3');
                padding: 0;
                width: 24.439%;

                &:nth-child(4n + 1) {
                    margin-left: 0;
                }
            }

            &.wishlist {
                .product-item-actions {
                    margin-top: 15px;
                }
            }
        }

        .block-wishlist-management {
            margin-bottom: 30px;

            .wishlist-select {
                margin-bottom: 20px;
            }

            .wishlist-select-items {
                .current {
                    border-bottom: 4px solid @color-red5 !important;
                    line-height: 1;
                }
            }

            .wishlist-title {
                margin-bottom: 15px;

                strong {
                    .lib-css(font-weight, @font-weight__bold);
                    font-size: 2.8rem;
                    letter-spacing: -1.2px;
                    text-transform: uppercase;
                    line-height: 1;
                }
            }

            .wishlist-info,
            .wishlist-toolbar {
                line-height: 1;
                color: @color-gray20;
                .lib-font-size(17);
            }

            .wishlist-notice {
                color: @color-gray20;
            }

            .wishlist-toolbar {
                .copy {
                    .dropdown {
                        right: 0;

                        &::before {
                            left: auto;
                            right: 11px;
                        }

                        &::after {
                            left: auto;
                            right: 10px;
                        }
                    }
                }

                .wishlist-toolbar-select {
                    margin-right: 40px;
                    float: left;

                    .checker {
                        margin-top: -4px;
                    }

                    label {
                        margin-top: 0;
                    }

                }

                .wishlist-toolbar-actions {
                    margin-top: -1px;

                    .wishlist-dropdown {
                        .action.toggle {

                            &.active:after,
                            &:after {
                                font-size: 26px;
                                color: @tt-blue;
                            }
                        }

                        &.copy {
                            margin-right: 18px;
                        }

                        &.move {
                            margin-right: 25px;
                        }

                        .items {
                            .item {
                                .action:before {
                                    display: none;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__ml) {
    .loyalty-insider-info {
        .loyalty-insider-wrapper {
            .under {
                .left-info {
                    &.loyalty_insider_points_content_left {
                        padding-right: 15px;
                    }
                }
            }
        }
    }

    .loyalty-insider-info {
        .loyalty_insider_info {
            .loyalty_insider_points_content_point {
                .lib-font-size(52);
                letter-spacing: -1.5px;
                .currency-symbol,
                .price-decimal {
                    .lib-font-size(22);
                    top: -2px;
                    letter-spacing: -1px;
                }
            }
            .left-info {
                .loyalty_insider_points_sign {
                    .lib-font-size(22);
                    top: -16px;
                }
            }
        }
    }
}