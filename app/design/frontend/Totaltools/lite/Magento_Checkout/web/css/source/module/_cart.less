//
//  Common
//  _____________________________________________
@media-common: true;

.checkout-cart-button {
    .lib-button-outline();
    display: block;
    width: 100%;
}

& when (@media-common = true) {
    .checkout-cart-index {

        .block-requisition-list,
        #block-shipping,
        .page-title-wrapper,
        .cart-top-container {
            &:extend(.ui-hidden all);
        }

        .cart-top-wrap {
            .title {
                ._checkout-block-title;
            }

            .blocks {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-direction(row);
                .lib-vendor-prefix-flex-wrap;
                width: 100%;
                margin-bottom: 30px;

                > div {
                    width: 100%;
                    .lib-css(box-sizing, border-box, 1);
                }
            }
        }

        .form-cart {
            .block-title {
                .lib-clearfix;
                .lib-css(margin, @indent__m 0);
                .lib-vendor-prefix-display;
                .lib-vendor-box-align(center);
            }

            .title,
            .block-count {
                .lib-font-size(24);
                .lib-line-height(30);
                .lib-css(font-family, @font-family__base);
                .lib-css(font-weight, @font-weight__bold);
                .lib-css(margin, 0);
            }

            .block-count {
                color: @link__color;
                margin-left: auto;
                text-align: right;
            }
        }

        .cart.main.actions {
            .lib-css(margin, @indent__base 0);
            .lib-vendor-prefix-display;
            .lib-css(justify-content, flex-end);
        }

        .cart {
            .title {
                ._checkout-block-title;
                border-bottom: 1px solid @color-gray4;
                overflow: hidden;

                .heading {
                    float: left;
                }

                .count {
                    color: @color-blue;
                    float: right;
                }
            }

            .items {
                overflow: hidden;
                border: 0 none;
                margin: 0;
                padding: 0;
                max-height: 100%;

                .product {
                    .lib-clearfix();
                }

                .product-item {
                    padding: 20px 0;
                    margin: 0;
                    position: relative;

                    .product-labels-container {
                        display: inline-block;
                        width: 110px;
                        float: left;
                        clear: both;
                        margin-top: -25px;

                        .product-label-wrapper {
                            display: block;
                        }

                        .amasty-label-conatiner {
                            position: relative !important;
                            margin-top: 0 !important;
                        }
                    }

                    .product-item-photo {
                        width: 75px !important;
                        height: 75px !important;
                        display: inline-block;
                        float: left;

                        .product-image-container {
                            max-width: 100%;
                        }
                    }

                    .product-item-details {
                        margin-bottom: 5px;

                        .product-item-name {
                            color: @minicart-product__name-color;
                            font-weight: 700;
                            text-transform: uppercase;
                            margin: 0;
                            height: auto !important;

                            a {
                                .lib-css(color, @color-gray20);
                            }
                        }

                        .message.notice {
                            color: @color-white;
                            background-color: @color-red2;

                            > *:first-child:before {
                                color: @color-white;
                            }
                        }
                    }

                    .product-item-name-block {
                        margin-bottom: 5px;
                    }

                    .product-options {
                        max-width: 60%;
                    }

                    .product-price {
                        position: absolute;
                        top: 10px;
                        right: 0;
                        text-align: right;
                    }

                    .product-options {
                        .toggle {
                            cursor: pointer;
                            position: relative;
                            margin-bottom: @indent__s;
                            text-decoration: underline;
                            .lib-css(color, @link__color);
                            .lib-font-size(14);

                            .lib-icon-font(
                                @_icon-font-content: @tt-icon-angle-down,
                                @_icon-font-size: 16px,
                                @_icon-font-line-height: 16px,
                                @_icon-font-text-hide: false,
                                @_icon-font-position: after,
                                @_icon-font-margin: 0 0 0 4px,
                                @_icon-font-display: block
                            );

                            &:after {
                                position: static;
                                right: @indent__base;
                                top: 0;
                            }
                        }

                        &.active {
                            > .toggle {
                                .lib-icon-font-symbol(@_icon-font-content: @tt-icon-angle-up, @_icon-font-position: after);
                            }
                        }
                    }

                    .savings {
                        display: inline-block;
                        padding: 2px 5px 4px;
                        text-transform: uppercase;
                        .lib-css(background-color, @color-yellow);
                        .lib-heading-typography(14, 14, @color-gray20);
                        .lib-css(border-radius, 3px, 1);
                        .lib-clearfix();

                        .decimal-dot {
                            font-size: 20%;
                            line-height: inherit;
                            color: transparent;
                            vertical-align: super;
                        }
                    }

                    .savings-currency,
                    .savings-decimal {
                        vertical-align: super;
                        position: relative;
                        top: 2px;
                        .lib-font-size(10);
                    }

                    .cart-price {
                        white-space: nowrap;
                        .lib-heading-typography(30, 34, @color-red2);

                        .currency-symbol,
                        .price-decimal {
                            vertical-align: super;
                            position: relative;
                            top: 2px;
                            .lib-font-size(18);
                        }

                        .decimal-dot {
                            font-size: 20%;
                            line-height: inherit;
                            color: transparent;
                            vertical-align: super;
                        }
                    }

                    .subtotal {
                        .subtotal-currency,
                        .subtotal-decimal {
                            vertical-align: super;
                            position: relative;
                            top: 2px;
                            .lib-font-size(18);
                        }
                    }

                    .product-special {
                        &.product-free-shipping {
                            .product-price {
                                top: 6px;
                            }
                        }
                    }

                    .details-qty,
                    .options {
                        font-size: 14px;
                        color: @color-gray20;
                    }

                    .product-qty {
                        position: absolute;
                        top: 14px;
                        right: 160px;

                        .value {
                            max-width: 50px;
                            height: 40px;
                            .lib-line-height(40);
                            text-align: center;
                            padding: 0;
                        }
                    }

                    .product-remove {
                        position: absolute;
                        bottom: 6px;
                        right: 6px;
                        display: inline-block;
                        max-width: 90px;

                        .wishlist,
                        .action-towishlist {
                            display: none !important;
                        }

                        a {
                            display: inline-block;
                            background-color: #f7f7f7;
                            border: 2px solid #bfbfbf;
                            border-radius: 3px;
                            font-family: @font-family__base;
                            .lib-font-size(14);
                            font-weight: 700;
                            line-height: 1;
                            color: #999;
                            padding: 4px 10px 3px;
                            transition: all 0.2s ease-in-out;
                            cursor: pointer;
                            white-space: nowrap;
                            min-width: 81px;
                            .lib-css(box-sizing, border-box, 1);

                            .lib-icon-font(
                                @_icon-font-content: @minicart-icon_product__remove,
                                @_icon-font: @font-family__base,
                                @_icon-font-size: 21px,
                                @_icon-font-line-height: 21px,
                                @_icon-font-color: @color-red2,
                                @_icon-font-text-hide: false,
                                @_icon-font-position: before,
                                @_icon-font-display: inline-block,
                                @_icon-font-margin: 0 2px 0 0
                            );

                            &:before {
                                font-weight: 700;
                                position: relative;
                                top: -2px;
                            }

                            &:hover {
                                text-decoration: none;
                                background-color: @color-red2;
                                border-color: @color-red2;
                                color: @color-white;

                                &:before {
                                    color: @color-white;
                                }
                            }

                            &.action-edit {
                                display: none !important;
                            }
                        }
                    }
                }

                .product-stock-status {
                    .lib-clearfix();

                    p {
                        font-family: @font-family__base;
                        .lib-font-size(16);
                        font-weight: 700;
                        margin: 0;
                        position: relative;

                        .delivery-tooltip {
                            z-index: 999;
                            .lib-tooltip(top);
                            position: static;
                            float: left;

                            .tooltip-toggle {
                                .lib-icon-font(
                                    @_icon-font-content: @minicart-icon_availability__available,
                                    @_icon-font-margin: 0 3px 0 0,
                                    @_icon-font-vertical-align: top,
                                    @_icon-font-position: before,
                                    @_icon-font-color: inherit,
                                    @_icon-font-line-height: 18px,
                                    @_icon-font-text-hide: true
                                );

                                &:before {
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                }
                            }

                            .tooltip-content {
                                .lib-font-size(14);
                                font-weight: 400;
                            }
                        }

                        .delivery-message {
                            display: block;
                            padding-left: 25px;
                        }

                        &.available {
                            color: @minicart-delivery__available-color;
                        }

                        &.not-available {
                            color: @minicart-delivery__not-available-color;

                            .tooltip-toggle:before {
                                content: @minicart-icon_availability__not-available;
                            }
                        }

                        &.low-stock {
                            color: @minicart-delivery__low-stock-color;

                            .tooltip-toggle:before {
                                content: @minicart-icon_availability__low-stock;
                            }
                        }
                    }
                }
            }
        }
    }

    /****************************
    Price Guarantee Block
    ****************************/
    #price-guarantee-block {
        .lib-clearfix();

        .block-wrapper {
            #low-price-sticker {
                background: url(../images/new-payment-v2.png) top left no-repeat;
                background-size: contain;
                width: 100%;
                height: 0;
                padding-top: 38%;
            }
            .payment-step-container {
                #low-price-sticker {
                    background-image: url(../images/seals_top.png);
                    padding-top: 15%;
                }
            }

            .cart-step-container-rapidssl {
                #low-price-sticker {
                    background: url(../images/rapidssl.png) top left no-repeat;
                }
            }
        }
    }

    //  Summary block
    .cart-summary {
        &:extend(.abs-add-box-sizing all);
        padding: 0;

        > .title {
            display: block;
            .lib-font-size(24);
            .lib-line-height(30);
            .lib-css(margin, @indent__m 0);
            .lib-css(padding-bottom, @indent__m);
            .lib-css(border-bottom, 1px solid @color-gray4);
        }

        .block {
            &:extend(.abs-discount-block all);
            margin-bottom: @indent__s;
            border: 1px solid @color-gray4;

            > .content {
                display: none;
            }

            .title {
                position: relative;
                padding: @indent__m;
                margin: 0;
                width: 100%;
                color: @color-blue;
                border: 0 none;
                cursor: pointer;

                strong {
                    .lib-font-size(16) !important;
                    .lib-line-height(16);
                    text-decoration: none;
                }

                .lib-icon-font(
                    @tt-icon-down-dir,
                    @_icon-font-position: after,
                    @_icon-font-color: @link__color 
                );

                &:after {
                    position: absolute;
                    top: 50%;
                    right: 15px;
                    .lib-css(transform, translateY(-50%));
                    .lib-css(transition, all 0.2s ease, 1);
                }
            }

            &.active {
                .title:after {
                    .lib-css(transform, translateY(-50%) rotate(180deg), 1);
                }
            }

            .content {
                padding: 0 18px 18px;
            }

            .item-options {
                margin-left: 0;
            }

            .fieldset {
                margin: 0;

                .field {
                    margin: 0 0 @indent__s;

                    &.note {
                        font-size: @font-size__s;
                        margin-top: 20px;
                    }

                    .label {
                        display: none !important;
                    }
                }

                .methods {
                    .field {
                        > .label {
                            display: inline;
                        }
                    }
                }
            }

            .fieldset.estimate {
                > .legend,
                > .legend + br {
                    &:extend(.abs-no-display all);
                }
            }

            &.giftcard {
                #giftcard-balance-lookup {
                    margin-bottom: @indent__m;

                    p {
                        font-family: @font-family__base;
                        margin-bottom: @indent__s;

                        strong {
                            color: @color-blue;
                        }
                    }
                }
            }
        }

        .actions-toolbar {
            > .primary {
                button {
                    &:extend(.abs-revert-secondary-color all);
                }
            }
        }
        &:extend(.abs-adjustment-incl-excl-tax all);

        // Discount/Promo codes
        .block.discount {
            .fieldset.coupon {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap(wrap);
                .lib-css(justify-content, space-between, 1);

                .field {
                    width: 67%;
                }

                .actions-toolbar {
                    width: 30%;
                    margin-left: auto;
                    .lib-css(box-sizing, border-box, 1);

                    .primary {
                        display: block;
                        width: 100%;
                    }

                    .action.primary {
                        .checkout-cart-button();
                    }
                }
            }
        }

        // Quote email
        .block.quote {
            .actions-toolbar {
                .primary {}

                .action.primary {
                    .checkout-cart-button();
                }
            }
        }

        // Insider Points
        .block.points {
            .text-field {
                .lib-font-size(15);
            }

            .actions-toolbar {
                .action.primary {
                    .checkout-cart-button();
                }
            }
        }
    }

    //  Totals block
    .cart-totals {
        border-top: 1px solid @border-color__base;
        padding: @indent__m @indent__base;
        &:extend(.abs-sidebar-totals all);
        .lib-css(background, @sidebar__background-color);
        .lib-css(border, 2px solid #e5e5e5);
        .lib-css(border-radius, 5px);
        .lib-css(font-family, @font-family__base);
        margin-bottom: @indent__m;
        .table-wrapper {
            margin-bottom: 0;
            overflow: inherit;
        }
    }

    //  Products table
    .cart.table-wrapper {
        .items {
            thead + .item {
                border-top: @border-width__base solid @border-color__base;
            }

            > .item {
                border-bottom: @border-width__base solid @border-color__base;
                position: relative;

                .product-image-container {
                    .amasty-label-conatiner {
                        display: none !important;
                    }
                }
            }
        }

        .col {
            padding: 0;

            &.qty {
                .input-text {
                    margin-top: -5px;
                    &:extend(.abs-input-qty all);
                }

                .label {
                    &:extend(.abs-visually-hidden all);
                }
            }
        }

        .item {
            &-actions td {
                padding-bottom: @indent__s;
                text-align: center;
                white-space: normal;
            }

            .col {
                &.item {
                    display: block;
                    min-height: 75px;
                    padding: @indent__m 0 @indent__s 75px;
                    position: relative;
                }
            }
        }

        .actions-toolbar {

            &:extend(.abs-add-clearfix all);

            > .action {
                margin-bottom: @indent__s;
                margin-right: @indent__s;

                &:last-child {
                    margin-right: 0;
                }

                &.action-edit {
                    display: none !important;
                }

                &.action-delete {
                    display: inline-block;
                    font-weight: 700;
                    line-height: 1;
                    padding: 4px 10px 3px;
                    cursor: pointer;
                    white-space: nowrap;
                    min-width: 80px;
                    border: 2px solid @color-gray-light3;
                    .lib-font-size(14);
                    .lib-css(color, @color-gray-light6);
                    .lib-css(background-color, @color-gray97);
                    .lib-css(border-radius, 3px, 1);
                    .lib-css(transition, all 0.2s ease-in-out, 1);

                    .lib-icon-font(
                        @tt-icon-cross,
                        @_icon-font-size: 21px,
                        @_icon-font-line-height: 21px,
                        @_icon-font-color: @color-red2,
                        @_icon-font-text-hide: true,
                        @_icon-font-margin: 0 2px 0 0
                    );

                    &:before {
                        position: relative;
                        top: -2px;
                    }

                    &:hover {
                        text-decoration: none;
                        background-color: @color-red2;
                        border-color: @color-red2;
                        color: @color-white;

                        &:before {
                            color: @color-white;
                        }
                    }
                }
            }
        }

        .gift-registry-name-label {
            &:after {
                content: ':';
            }
        }

        //  Product options
        .item-options {
            font-size: @font-size__s;
            margin-bottom: @indent__s;
            &:extend(.abs-product-options-list all);
            &:extend(.abs-add-clearfix all);

            &[data-role="content"] {
                display: none;
            }
        }

        .product-item-name + .item-options {
            margin-top: @indent__s;
        }

        .product-image-wrapper {
            &:extend(.abs-reset-image-wrapper all);
        }

        .action.configure {
            display: inline-block;
            margin: 0 0 @indent__base;
        }
    }

    .cart-container {
        .form-cart {
            &:extend(.abs-shopping-cart-items all);
        }

        .checkout-methods-items {
            &:extend(.abs-reset-list all);
            list-style: none;
            padding: 0;
            margin: 0;
            margin-top: @indent__base;
            text-align: center;

            .action.primary,
            .action.continue {
                &:extend(.abs-button-l all);
                .lib-font-size(18);
                .lib-line-height(18);
                .lib-css(padding, @indent__m);
                .lib-css(border-radius, 3px, 1);
                width: 100%;

                &:hover {
                    text-decoration: none;
                }
            }

            .item {
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

    }

    //
    //  Cross sell
    //  ---------------------------------------------

    .block.crosssell {
        margin: 0px;
        padding: 0px;
        clear: both;

        .block-title.title {
            font-family: @font-family__base;
            text-align: center;
            text-transform: uppercase;
            font-size: 24px;
            padding-top: 40px;
        }

        .products-grid {
            .flickity-prev-next-button.previous {
                right: 38px;
                left: auto;
            }

            .flickity-prev-next-button.next {
                right: 0px;
            }

            .flickity-prev-next-button {
            /*.arrow {
                fill: #1a3e92;
              }*/
              top: -50px;
              border-radius: 3px;
//              border: 2px solid #1a3e92;
            }

            .flickity-page-dots {
                display: none;
            }
        }
    }

    .checkout-cart-index {
        .block.crosssell {
            padding: 20px 0 0;
            margin-bottom: 15px;
            clear: both;

            .block-title.title {
                text-align: left;
                font-family: inherit;
                text-transform: capitalize;
                font-size: inherit;
                padding-top: 0;
            }

            .slick-track {
                margin: 0;

                .product-item {
                    max-width: 100%;

                    .product-item-info {
                        .product-item-photo {
                            .product-image-container {
                                aspect-ratio: 200 / 200;
                            }
                        }
                    }
                }
            }
        }

        .homepage-block {
            .products-grid {
                .slick-arrow {
                    background: @color-black-75 !important;
                    transform: translate(0,-50%);
                    .lib-vendor-prefix-display;
                    .lib-css(align-items, center, 1);
                    .lib-css(justify-content, center, 1);

                    &.slick-prev {
                        &::before {
                            content: '\f103';
                        }
                    }

                    &.slick-next {
                        &::before {
                            content: '\f102';
                        }
                    }
                }
            }
        }
    }

    .recommended-products {
        h3 {
            text-transform: uppercase;
            text-align: center;
            font-size: 28px;
            font-family: @heading__font-family__base;
        }
    }

    .cart-bottom-container-wrapper {
        background: #f7f7f7;
        padding: 50px @layout-indent__width;
        width: @layout-column-main__width-1;
        .lib-css(box-sizing, border-box);

        .cart-bottom-container {
            max-width: @layout__max-width;
            margin: 0 auto;
            #lib-layout-columns();

            .cart-bottom-left-container {
                .lib-layout-column(2, 1, 50%);
                .lib-css(box-sizing, border-box);
                padding: 0 20px 0 0;
            }

            .cart-bottom-right-container {
                .lib-layout-column(2, 2, 50%);
                .lib-css(box-sizing, border-box);
                padding: 0 20px 0 0;
            }

            .block {
                .block-title {
                    h3 {
                        width: 100%;
                        text-transform: uppercase;
                        .lib-font-size(14px);
                        font-family: @font-family__base;
                        text-align: left;
                        padding: 10px 0;
                        border-top: 1px solid #dddddd;
                        border-bottom: 1px solid #dddddd;
                        margin: 0;
                    }
                }

                .block-content {
                    h5 {
                        text-transform: none;
                        font-weight: bold;
                        margin: 10px 0;
                    }
                }
            }
        }
    }

    .checkout-cart-index {
        .widget-product-tagline {
            text-align: left;
            padding-top: 10px;
            margin-top: 10px;
            border-top: 1px solid #cccccc;
            display: none;
            width: 100%;
            margin-top: 10px;
            float: left;

            img {
                margin: 2px 20px 2px 4px;
            }

            .zip-info {
                margin-left: 0;
                float: left;
            }
        }
    }

    // Empty Cart page CSS
    .checkout-cart-index {
        .page-title {
            margin: 30px 0 20px;
            font-weight: 700;
        }
    }

    .cart-empty-wrap {
        min-height: 657px;

        .page-title {
            padding: 30px 0 20px;
            margin: 0;
        }

        .cart-empty {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            justify-content: space-between;

            .column-left {
                width: 100%;

                p {
                    margin-bottom: 20px;
                }
            }

            .column-right {
                margin-left: auto;
                padding-bottom: 30px;
                max-width: 418px;
                width: 100%;
                aspect-ratio: 1/1.0742;

                img {
                    width: 100%;
                    aspect-ratio: 1/1.0742;
                    object-fit: cover;
                }
            }
        }
    }

    @media (max-width: 375px) {
        .cart-empty-wrap {
            min-height: 580px;
        }
    }

    // Cart page pagination CSS
    .cart-products-toolbar {
        margin-top: 20px;

        .pager {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            .lib-css(align-items, center, 1);

            .toolbar-amount {
                .lib-font-size(16);
                .lib-line-height(26);
                font-weight: 700;
                color: @secondary__color;
                margin-bottom: 0;
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {

    .cart-bottom-container-wrapper {
        .cart-bottom-container {
            .block {
                .block-title {
                    h3 {
                        border-bottom: none;
                    }
                }
            }
        }
    }


    .checkout-cart-index {
        .shipping-methods {
            margin-bottom: @indent__l;
        }

        .cart .items {
            .product-item {
                padding: 20px 0 15px;

                .product {
                    position: relative;
                    padding-bottom: 54px;
                }

                .product-item-details {
                    padding-left: 88px;
                    min-height: 60px;
                }

                .product-price {
                    top: auto;
                    bottom: -18px;
                }

                .product-qty {
                    top: auto;
                    right: auto;
                    bottom: -@indent__base;
                    left: 45%;
                    margin-left: -40px;
                }

                .product-stock-status {
                    padding: 25px 0 0;
                }

                .product-remove {
                    left: 0;
                    bottom: -@indent__base;
                }
            }
        }
    }

    .cart {
        &.table-wrapper {
            overflow: inherit;

            thead {
                .col {
                    &:not(.item) {
                        display: none;
                    }
                }
            }

            .col {

                &.qty,
                &.price,
                &.subtotal,
                &.msrp {
                    display: block;
                    float: left;
                    text-align: center;
                    white-space: nowrap;
                    width: 33%;

                    &:before {
                        content: attr(data-th) ':';
                        display: block;
                        font-weight: @font-weight__bold;
                        padding-bottom: @indent__s;
                    }
                }

                &.subtotal {
                    width: ~"calc(100% - 155px)";
                    padding-top: 25px !important;
                    padding-right: 0;
                    margin-bottom: 15px;

                    span {
                        float: left;
                        padding: 3px 0;
                    }

                    .actions-toolbar {
                        float: right;
                    }
                }

                &.msrp {
                    white-space: normal;
                }
            }

            .item {
                .col.item {
                    padding-bottom: 0;
                }

                .item-actions {
                    display: none;
                }
            }
        }

        &.main.actions {
            .action.update {
                width: 100%;
            }
        }
    }

    .cart-container {
        .lib-vendor-prefix-display;
        .lib-vendor-prefix-flex-wrap(wrap);

        .form-cart {
            &:extend(.abs-shopping-cart-items-mobile all);
        }

        .cart-left-container {
            .lib-vendor-prefix-order(1);
            width: 100%;

            &.crossell-wrapper {
                .lib-vendor-prefix-order(3);
            }
        }

        .cart-right-container {
            .lib-vendor-prefix-order(2);
            width: 100%;
        }
    }

    .block-requisition-list {
        padding: 0;
        display: block;
        display: none;

        .requisition-list-action {
            width: 100%;

            .action.toggle {
                .lib-font-size(14);
                .lib-css(font-weight, @font-weight__regular);
            }
        }

        .requisition-list-button {
            .lib-button-outline();
        }
    }

    // Cart page pagination CSS
    .cart-products-toolbar {
        .pager {
            .toolbar-amount {
                width: 100%;
            }
        }
    }

    .checkout-cart-index {
        .columns {
            .column {
                &.main {
                    padding-bottom: 0;
                }
            }
        }
    }
}

//
//  Tablet
//  _____________________________________________

.media-width(@extremum, @break, @extremum2, @break2) when (@extremum = 'min') and (@break = @screen__m) and (@extremum2 = 'max') and (@break2 = @screen__ml) {
    .checkout-cart-index {
        .cart-top-wrap .blocks {
            > div {
                width: 50%;
            }
        }

        .shipping-methods {
            padding-right: 20px;
        }
    }

    .checkout-cart-index {
        .cart-container {
            .cart-left-container {
                width: 100%;
                padding: 0;
            }

            .cart-right-container {
                width: 100%;
            }

            .cart.main.actions {
                button.update {
                    width: 100%;
                    margin-bottom: 20px;
                }
            }
        }
    }
}

//
//  Tablet and small desktops
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .cart-container {
        position: relative;

        &:extend(.abs-add-clearfix-desktop all);

        .form-cart {
            &:extend(.abs-shopping-cart-items-desktop all);
        }

        .widget {
            float: left;
        }

        .mobile{
            display: none;
        }
    }

    .cart-summary {
        float: right;
        position: static;
        width: 23%;

        .actions-toolbar {
            .column.main & {
                &:extend(.abs-reset-left-margin-desktop all);

                > .secondary {
                    float: none;
                }
            }
        }

        .block {
            .fieldset {
                .field {
                    .lib-form-field-type-revert(@_type: block);
                    margin: 0 0 @indent__s;
                }
            }
        }
    }

    .cart {
        &.table-wrapper {
            .items { // Google Chrome version 44.0.2403.107 m fix
                min-width: 100%;
                width: auto;
            }

            .item {
                .col {
                    &.item {
                        padding: 27px 8px @indent__s;
                    }
                }

                &-actions {
                    td {
                        text-align: right;
                    }
                }

                .product-stock-status {
                    margin: 25px 0 0;
                    padding-right: 100px;
                    min-height: 25px;
                }
            }

            .product {
                &-item-photo {
                    display: table-cell;
                    max-width: 100%;
                    padding-right: @indent__base;
                    position: static;
                    vertical-align: top;
                    width: 1%;
                }

                &-item-details {
                    display: inline-block;
                    white-space: normal;
                    max-width: 50%;
                }
            }

            .item-actions {
                .actions-toolbar {
                    text-align: right;
                    &:extend(.abs-reset-left-margin-desktop all);
                }
            }
        }
    }

    .checkout-cart-index{
        .page-title {
            margin: 30px 0 20px;
        }

        .cart-top-wrap .blocks {
            > div {
                width: 50%;
            }

            .block.shipping {
                padding-right: @indent__m;
            }
        }
    }

    .cart-container {
        .cart-left-container {
            .lib-layout-column(2, 1, 73%);
            padding-right: 30px;
            .lib-css(box-sizing, border-box);

            .form-cart {
                width: 100%;
            }
        }

        .cart-right-container {
            .cart-summary {
                width: 100%;
            }

            .lib-layout-column(2, 1, 27%);
            float: right;
        }
    }

    // Empty Cart page CSS
    .checkout-cart-index {
        .page-title {
            margin: 30px 0 20px;
        }
    }

    .cart-empty-wrap {
        min-height: 547px;

        .page-title {
            padding: 30px 0 20px;
            margin: 0;
        }

        .cart-empty {
            .column-left {
                width: auto;
            }

            .column-right {
                padding-bottom: 0;
            }
        }
    }
}

.toolbar-amount {
    margin-right: 1rem !important;
}

//
//  Desktop LG
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .checkout-cart-index {
        .cart-right-container{
            position: relative;
        }
    }
}
