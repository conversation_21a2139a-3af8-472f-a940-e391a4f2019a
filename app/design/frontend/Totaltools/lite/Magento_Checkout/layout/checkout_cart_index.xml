<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="checkout_cart_item_renderers"/>
    <body>
        <move element="checkout.cart.form.before" destination="checkout.cart.container" before="cart.summary"/>
        <move element="checkout.cart.form" destination="checkout.cart.container" after="cart.summary"/>
        <move element="checkout.cart.widget" destination="checkout.cart.container" after="checkout.cart.form"/>
        <move element="checkout.cart.totals.container" destination="cart.summary"/>
        <move element="checkout.cart.coupon" destination="cart.summary" before="-" after="checkout.cart.summary.title"/>
        <move element="checkout.cart.giftcardaccount" destination="cart.summary" after="checkout.cart.coupon"/>
        <move element="checkout.cart.methods.bottom" destination="cart.summary"/>

        <referenceBlock name="checkout.cart.item.renderers.default.actions.gift_options" remove="true" />
        <referenceBlock name="checkout.cart.item.renderers.simple.actions.gift_options" remove="true" />
        <referenceBlock name="checkout.cart.item.renderers.bundle.actions.gift_options" remove="true" />
        <referenceBlock name="checkout.cart.item.renderers.grouped.actions.gift_options" remove="true" />
        <referenceBlock name="checkout.cart.item.renderers.configurable.actions.gift_options" remove="true" />
        <referenceBlock name="checkout.cart.item.renderers.gift-card.actions.gift_options" remove="true" />

        <referenceBlock name="checkout.cart.item.renderers.simple.actions.move_to_wishlist" remove="true"></referenceBlock>
        <referenceBlock name="checkout.cart.order.actions.gift_options" remove="true"></referenceBlock>
        <referenceBlock name="checkout.cart.methods.multishipping" remove="true"></referenceBlock>
        <referenceContainer name="nav.bar" remove="true" />

        <container name="checkout.cart.totals.holder"></container>

        <move element="checkout.cart.totals.msrp" destination="checkout.cart.totals.holder" after="-"></move>
        <move element="checkout.cart.totals" destination="checkout.cart.totals.holder" after="-"></move>
        <move element="checkout.cart.totals.holder" destination="cart.summary" before="checkout.cart.methods.bottom" after="checkout.cart.points"></move>

        <referenceBlock name="checkout.cart.container">
            <container name="checkout.cart.top.container" before="checkout.cart.left.container" htmlTag="div" htmlClass="cart-top-container">
                <block class="Magento\Checkout\Block\Cart" name="checkout.cart.methods.bottom.mobile" before="-" template="cart/methods.phtml">
                    <container name="checkout.cart.methods.mobile" as="methods" label="Payment Methods After Checkout Button">
                        <block class="Magento\Checkout\Block\Onepage\Link" name="checkout.cart.methods.onepage.mobile" template="onepage/link.phtml" />
                        <block class="Magento\Checkout\Block\QuoteShortcutButtons" name="checkout.cart.shortcut.buttons.mobile" />
                    </container>
                </block>
            </container>
            <container name="checkout.cart.left.container" htmlTag="div" htmlClass="cart-left-container" />
            <container name="checkout.cart.right.container" htmlTag="div" htmlClass="cart-right-container" />
        </referenceBlock>
        <referenceContainer name="page.wrapper">
            <container name="checkout.cart.bottom" htmlTag="section" htmlClass="checkout-faqs" >
                <container name="checkout.cart.bottom-main" htmlTag="div" htmlClass="checkout-faqs-wrapper page-main">
                    <block class="Magento\Framework\View\Element\Template" name="need-help-block" template="Magento_Checkout::need-help.phtml" after="-" />
                </container>
            </container>
        </referenceContainer>

        <move element="checkout.cart.form.before" destination="checkout.cart.left.container" before="-"></move>
        <move element="checkout.cart.form" destination="checkout.cart.left.container" before="-"></move>
        <move element="cart.summary" destination="checkout.cart.right.container" before="-"></move>
        <!-- <move element="checkout.cart.crosssell" destination="checkout.cart.left.container.crossell" after="-"></move> -->

        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template" name="magento-loader-image" template="Magento_Checkout::preload.phtml" />
            <referenceBlock name="checkout.cart.summary.title">
                <arguments>
                    <argument translate="true" name="text" xsi:type="string">Order Summary</argument>
                </arguments>
            </referenceBlock>
        </referenceContainer>
        <referenceContainer name="header.container">
            <referenceBlock name="store.locator" remove="true" />
        </referenceContainer>
        <referenceBlock name="checkout.cart.shipping">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="block-summary" xsi:type="array">
                            <item name="config" xsi:type="array">
                                <item name="componentDisabled" xsi:type="boolean">true</item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>

        <referenceContainer name="checkout.cart.totals.container">
            <container name="payment.widgets.container" htmlTag="div" htmlClass="product-payment-widgets" before="checkout.cart.methods">
                <container name="payment.widget.ctn" htmlClass="payment-widget-ctn" htmlTag="div">
                    <block name="product.payment.block" template="Magento_Catalog::product/payment-widget.phtml">
                        <arguments>
                            <argument name="page_type" xsi:type="string">cart</argument>
                        </arguments>
                        <block class="Totaltools\PaypalPayinFour\Block\Config" name="payinfour.product" template="Totaltools_PaypalPayinFour::payinfour_product.phtml">
                            <arguments>
                                <argument name="page_type" xsi:type="string">cart</argument>
                                <argument name="ctn-class" xsi:type="string">paypal-ctn</argument>
                                <argument name="sort-order" xsi:type="string">10</argument>
                            </arguments>
                        </block>
                        <block class="Magento\Framework\View\Element\Template"
                               name="zip.widget.block" as="zip.widget.block" template="Zip_ZipPayment::advert/pdp_widget.phtml">
                            <arguments>
                                <argument name="page_type" xsi:type="string">cart</argument>
                                <argument name="ctn-class" xsi:type="string">zip-ctn</argument>
                                <argument name="sort-order" xsi:type="string">20</argument>
                            </arguments>
                        </block>
                        <block class="Humm\HummPaymentGateway\Block\Advert\Widget" name="humm.custom.widget" template="Humm_HummPaymentGateway::advert/humm-widget.phtml">
                            <arguments>
                                <argument name="page_type" xsi:type="string">cart</argument>
                                <argument name="ctn-class" xsi:type="string">humm-ctn</argument>
                                <argument name="sort-order" xsi:type="string">40</argument>
                            </arguments>
                        </block>
                    </block>
                </container>
            </container>
        </referenceContainer>

        <referenceBlock name="afterpay.cart.cta">
            <arguments>
                <argument name="page_type" xsi:type="string">cart</argument>
                <argument name="ctn-class" xsi:type="string">afterpay-ctn</argument>
                <argument name="sort-order" xsi:type="string">30</argument>
            </arguments>
        </referenceBlock>
        <move element="afterpay.cart.cta" destination="product.payment.block" />
        <move element="ambanners.below_total" destination="checkout.cart.totals.container" before="payment.widgets.container"/>
    </body>
</page>
