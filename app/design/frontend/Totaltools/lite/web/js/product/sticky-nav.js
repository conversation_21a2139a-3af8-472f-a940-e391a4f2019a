define([
    'jquery',
    'jquery/ui'
], function($) {
    'use strict';

    return function(config) {
        $(document).ready(function() {
            // Handle active state for navigation items
            $(".product-top-sticky-bar li a").on('click', function(e) {
                e.preventDefault();
                $(".product-top-sticky-bar li").removeClass("active");
                $(this).parent().addClass("active");
            });

            // Handle first tab click and accordion behavior
            $(".product-top-sticky-bar li:first-child a").on('click', function(e) {
                e.preventDefault();
                var $firstTitle = $("#detailed-accordion .product.data.items .item.title:first-child");

                if(!$($firstTitle).hasClass("active")) {
                    $($firstTitle).click();
                }
            });

            // Existing scroll animation code
            $('.product-top-sticky-bar a').on('click', function(ev) {
                ev.preventDefault();

                var anchor = $(this).attr('href');

                $('html, body').animate({
                    scrollTop: $(anchor).offset().top - 120
                }, 300);
            });
        });
    };
});