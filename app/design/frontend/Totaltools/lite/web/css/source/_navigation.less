@media-common: true;

& when (@media-common = true) {
    .page-header {
        background-color: @primary__color;
        border: 0 !important;
        margin: 0 !important;

        .header {
            &.content {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap(wrap);
                .lib-css(align-items, center, 1);
                margin: 0 auto;
                max-width: 1282px;
                padding: 0 20px;
            }

            .nav-toggle {
                width: 30px;

                span {
                    display: none;
                }

                &::before {
                    content: '\e816';
                    color: @color-white;
                    font-size: 28px;
                    line-height: inherit;
                    font-family: @font-tt-icons;
                    vertical-align: middle;
                    display: inline-block;
                    font-weight: 400;
                }
            }

            .block-search {
                z-index: 30;
                position: relative;

                .block-title {
                    display: none;
                }

                .block-content {
                    position: relative;
                    z-index: 1;

                    .label {
                        display: none;
                    }
                }

                .control {
                    position: static;
                    border: 0;
                    z-index: 0;
                    
                    .search-autocomplete-dropdown {
                        .input-text { 
                            border: 1px solid @color-blue;
                            background-color: @color-white;
                            color: @color-gray20;
                            height: 44px;
                            line-height: 42px;
                            padding: 0 35px 0 15px;
                            .lib-css(border-radius, 5px, 1);
                            font-size: 16px;
                            .lib-css(font-weight, @font-weight__regular);
                            width: 100%;
                            margin: 0;
                            position: relative;
                            left: 0;
                            z-index: 5;
                            box-shadow: unset !important;

                            &::placeholder {
                                color: @color-gray40;
                            }
                        }
                    }

                    #search {
                        background-color: @color-white;
                        color: @color-gray20;
                        border: 1px solid @color-white;
                        height: 44px;
                        line-height: 42px;
                        padding: 0 35px 0 15px;
                        .lib-css(border-radius, 5px, 1);
                        font-size: 16px;
                        .lib-css(font-weight, @font-weight__regular);
                        width: 100%;
                        margin: 0;
                        position: relative;
                        left: 0;
                        z-index: 5;
                        box-shadow: unset !important;

                        &::placeholder {
                            color: @color-gray40;
                        }

                        &:focus {
                            border-color: @color-blue;
                        }
                    }

                    .nested {
                        display: none;
                    }

                    .search-autocomplete-container {
                        font-family: @font-family__base;
                        position: relative;

                        .search-autocomplete-dropdown {
                            .search-autocomplete-wrapper {
                                .col-left {
                                    .search-autocomplete-categories {
                                        border: 0;
                                        &.suggested-keywords {
 
                                            p {
                                                font-size: 12px;
                                                line-height: 20px;
                                                color: @color-gray-light6;
                                                margin-bottom: 7px;
                                            }

                                            h5 {
                                                color: @color-blue2;
                                                font-family: @font-family-name__base;
                                                .lib-css(font-weight, @font-weight__bold);
                                                line-height: 20px;
                                                font-size: 18px;
                                                letter-spacing: 0.5px;
                                                text-transform: uppercase;
                                                margin: 0;
                                                padding: 0 0 3px;
                                                border-bottom: 1px solid @color-gray90;
                                                width: 82%;    
                                                max-width: ~"calc(100% - 65px)";
                                            }

                                            .categories-list {
                                                padding-top: 3px;

                                                .categories-list-item {
                                                    margin: 0;
                                                    border: 0;
                                                    padding: 2.5px 0;

                                                    a {
                                                        padding: 0;
                                                        justify-content: space-between;
                                                        transition: 0.6s all ease;

                                                        p {
                                                            font-size: 14px;
                                                            line-height: 20px;
                                                            color: @color-gray20;
                                                            margin-bottom: 0;
                                                        }

                                                        .product-count {
                                                            line-height: 1;    
                                                            position: relative;
                                                            padding-right: 21px;

                                                            span {
                                                                font-size: 12px;
                                                                line-height: 20px;
                                                                color: @color-gray-light6;
                                                                padding-left: 4px;
                                                                
                                                                &:first-child {
                                                                    padding-left: 0;
                                                                }
                                                            }

                                                            &::after {
                                                                content: "";
                                                                width: 14px;
                                                                height: 14px;
                                                                background: url(../images/link-icon.png) center center no-repeat;
                                                                background-size: contain;
                                                                margin-left: 7px;    
                                                                right: 0;
                                                                position: absolute;
                                                                top: 50%;
                                                                transform: translateY(-50%);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    .recently-viewed-list {
                                        p {
                                            font-size: 12px;
                                            line-height: 20px;
                                            color: @color-gray-light6;
                                            margin-bottom: 7px;
                                        }

                                        .searched {
                                            height: auto;
                                            border: 0;
                                            .lib-vendor-prefix-display;
                                            .lib-vendor-prefix-flex-wrap(wrap);
                                            .lib-css(align-items, center, 1);
                                            .lib-css(justify-content, space-between, 1);

                                            h5 {
                                                color: @color-blue2;
                                                font-family: @font-family-name__base;
                                                .lib-css(font-weight, @font-weight__bold);
                                                line-height: 28px;
                                                font-size: 18px;
                                                letter-spacing: 1px;
                                                text-transform: uppercase;
                                                margin: 0;
                                                padding: 0;
                                                border-bottom: 1px solid @color-gray10;
                                                width: 82%;
                                                max-width: ~"calc(100% - 65px)";
                                            }

                                            .clear-all {
                                                position: static;

                                                a {
                                                    .lib-css(font-weight, @font-weight__regular);
                                                    line-height: 1.25;
                                                    font-size: 14px;
                                                    color: @secondary__color;
                                                    display: inline-block;
                                                }
                                            }
                                        }

                                        .search-autocomplete-categories {
                                            border: 0;

                                           
                                            .categories-list {
                                                padding-top: 3px;

                                                .categories-list-item {
                                                    margin: 0;
                                                    border: 0;
                                                    padding: 2.5px 0;

                                                    a {
                                                        padding: 0;
                                                        justify-content: space-between;

                                                        p {
                                                            font-size: 14px;
                                                            line-height: 20px;
                                                            color: @color-gray20;
                                                            margin-bottom: 0;
                                                        }

                                                        .product-count {
                                                            line-height: 1;

                                                            span {
                                                                font-size: 12px;
                                                                line-height: 1.5;
                                                                color: @color-gray-light2;
                                                                padding-left: 4px;
                                                                
                                                                &:first-child {
                                                                    padding-left: 0;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    .trending-search {
                                        padding-top: 15px;

                                        h5 {
                                            color: @color-blue2;
                                            font-family: @font-family-name__base;
                                            .lib-css(font-weight, @font-weight__bold);
                                            line-height: 20px;
                                            font-size: 18px;
                                            letter-spacing: 1px;
                                            text-transform: uppercase;
                                            margin: 0;
                                            padding: 0 0 3px;
                                            border-bottom: 1px solid @color-gray10;
                                            text-align: left;
                                            width: 82%;
                                            max-width: ~'calc(100% - 65px)';
                                        }

                                        .categories-list {
                                            padding: 0;
                                            margin: 0;
                                            padding-top: 3px;

                                            li {
                                                padding: 2.5px 0;
                                                margin: 0;
                                                list-style: none;

                                                button {
                                                    color: @color-gray20;
                                                    display: block;
                                                    text-transform: capitalize;
                                                    font-size: 14px;
                                                    line-height: 20px;
                                                    cursor: pointer;
                                                    background: transparent;
                                                    border: 0;
                                                    outline: unset !important;
                                                    width: 100%;
                                                    text-align: left;
                                                    padding: 0;
                                                    .lib-css(font-weight, 400);

                                                    &:hover {
                                                        text-decoration: none;
                                                        .lib-css(font-weight, @font-weight__bold);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                .col-right {
                                    display: none;

                                    .search-autocomplete-products {
                                        .search-autocomplete-product {
                                            a {
                                                .info {
                                                    .shipping-label {
                                                        .lib-font-size(14);
                                                        .lib-css(font-weight, @font-weight__bold);
                                                        text-align: center;
                                                        color: @secondary__color;
                                                        text-transform: uppercase;
                                                        white-space: nowrap;
                                                        display: table;
                                                        border: 1px solid @color-gray90;
                                                        border-radius: 3px;
                                                        box-shadow: 0px 4px 4px @color-black-25;
                                                        min-height: unset;

                                                        &::before {
                                                            transform: scaleX(-1);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    .new-search-autocomplete {
                        input {
                            background-color: @color-white;
                            color: @color-gray20;
                            border: 1px solid @color-white;
                            height: 44px;
                            line-height: 42px;
                            padding: 0 35px 0 15px;
                            .lib-css(border-radius, 5px, 1);
                            font-size: 16px;
                            .lib-css(font-weight, @font-weight__regular);
                            width: 100%;
                            margin: 0;
                            position: relative;
                            left: 0;
                            z-index: 5;
                        }

                        .search-autocomplete-dropdown {
                            position: absolute !important;
                            z-index: 10;
                            top: 100%;
                            left: 0;
                            right: auto;
                            bottom: auto;
                            width: 100%;
                            max-height: unset;
                            min-height: unset;
                            box-shadow: 0 3px 3px 3px @color-black-10;
                            overflow-y: unset;
                            padding: 0;                        

                            .search-autocomplete-wrapper { 
                                &.search-result {
                                    width: 100% !important;
                                    position: static;
                                    transform: unset;
                                }

                                .col-left {
                                    width: 100%;
                                    flex: 0 0 100%;
                                    padding: 0;

                                    .search-autocomplete-categories {
                                        .categories-list-item {
                                            padding: 0 !important;
                                            margin: 0;
                                            border-bottom: 1px dotted @color-gray80 !important;

                                            &:last-child {
                                                border-bottom: 0
                                            }
                                            
                                            .category-container {
                                                .lib-vendor-prefix-display;
                                                .lib-vendor-prefix-flex-wrap(wrap);
                                                .lib-css(align-items, center, 1);
                                                .lib-css(justify-content, space-between, 1);
                                                padding: 8.5px 15px !important; 

                                                p {
                                                    margin: 0;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .action {
                    position: absolute;
                    right: 10px;
                    top: 6px;
                    display: inline-block;
                    background-image: none;
                    background: 0 0;
                    border: 0;
                    box-shadow: none;
                    line-height: inherit;
                    margin: 0;
                    text-decoration: none;
                    text-shadow: none;
                    .lib-css(font-weight, @font-weight__regular);
                    padding: 5px 0;
                    z-index: 5;
                    .lib-font-size(0);

                    &::before {
                        font-size: 19px;
                        line-height: 22px;
                        color: @primary__color;
                        content: '' !important;
                        .lib-css(font-family, @font-tt-icons);
                        margin: 0;
                        vertical-align: top;
                        display: inline-block;
                        .lib-css(font-weight, @font-weight__regular);
                        overflow: hidden;
                        text-align: center;
                        width: 22px;
                        height: 20px;
                    }
                }
            }

            .store-locator {
                list-style: none;

                a {
                    font-size: 20px;
                    line-height: 1.25;
                    font-family: @font-family__base;
                    color: @color-white;
                    .lib-css(font-weight, @font-weight__bold);
                    white-space: nowrap;
                    text-decoration: none;
                    .lib-vendor-prefix-display;
                    .lib-css(align-items, center, 1);
                    cursor: pointer;

                    &::before {
                        font-size: inherit;
                        line-height: inherit;
                        color: @color-white;
                        content: '\e815';
                        .lib-css(font-family, @font-tt-icons);
                        margin: -3px 2px 0 0;
                    }
                }
            }

            .link-login {
                list-style: none;

                .my-account {
                    position: relative;

                    &:hover {
                        .list-items {
                            opacity: 1;
                            .lib-css(transform, scale(1) translateX(-50%), 1);
                        }

                    }

                    &>a {
                        font-size: 20px;
                        line-height: 1.25;
                        font-family: @font-family__base;
                        color: @color-white;
                        .lib-css(font-weight, @font-weight__bold);
                        white-space: nowrap;
                        text-decoration: none;
                        .lib-vendor-prefix-display;
                        .lib-css(align-items, center, 1);
                        cursor: pointer;

                        &::before {
                            font-size: inherit;
                            line-height: inherit;
                            color: @color-white;
                            content: '\E84E';
                            .lib-css(font-family, @font-tt-icons);
                            margin: -1px 4px 0 0;
                        }
                    }

                    .list-items {
                        list-style: none;
                        position: absolute;
                        top: 100%;
                        left: 50%;
                        margin: 14px 0 0 0;
                        background-color: @color-white;
                        border: 1px solid @color-gray10;
                        display: block;
                        padding: 15px;
                        z-index: 21;
                        opacity: 0;
                        .lib-css(transform, scale(0) translateX(-50%), 1);
                        .lib-css(transition, all .2s ease-in, 1);
                        .lib-css(box-shadow, 0 0 3px 0 @color-gray10, 1);
                        .lib-css(border-radius, 3px, 1);

                        &::before {
                            border-bottom: 14px solid @color-white;
                            border-left: 12px solid transparent;
                            border-right: 12px solid transparent;
                            content: '';
                            height: 0;
                            left: 50%;
                            margin-left: -13px;
                            position: absolute;
                            top: -14px;
                            width: 0;
                            z-index: 1;
                        }

                        li {
                            text-transform: capitalize;
                            margin-bottom: 6px;
                            white-space: nowrap;

                            &.customer-info {
                                color: @color-blue;
                                border-bottom: 1px solid @color-gray80;
                                padding-bottom: 8px;
                                white-space: nowrap;

                                span {
                                    display: inline-block;
                                    .lib-heading-typography(
                                        @_font-size: 16,
                                        @_line-height: 20,
                                    );
                                }
                            }

                            &:first-child {
                                margin-bottom: 10px;
                            }

                            a {
                                padding-left: 0;
                                margin: 0;
                                font-family: @font-family__base;
                                color: @color-gray20;
                                display: inline-block;
                                .lib-css(font-weight, @font-weight__bold);
                                text-decoration: none;
                                .lib-css(transition, all .2s ease, 1);

                                &:hover {
                                    text-decoration: underline;
                                    color: @secondary__color;
                                }
                            }

                            .link-btn {
                                display: block;
                                font-size: 1.6rem;
                                line-height: 2.2rem;
                                text-align: center;
                                padding: 8px 20px;
                                text-decoration: none;
                                font-family: @font-family__base;
                                .lib-css(font-weight, @font-weight__bold);
                                .lib-css(border-radius, 5px, 1);
                                cursor: pointer;

                                &.primary {
                                    background-color: @secondary__color;
                                    color: @color-white;
                                }

                                &.button-outline {
                                    background-color: @color-white;
                                    color: @secondary__color;
                                    border: 2px solid @secondary__color;
                                }
                            }
                        }
                    }
                }
            }

            .minicart-wrapper {
                position: relative;
                z-index: 20;

                .action {
                    &.showcart {
                        .lib-heading-typography (
                            @_font-size: 20,
                            @_line-height: 25,
                            @_color: @color-white,
                        );
                        white-space: nowrap;
                        text-decoration: none;
                        .lib-vendor-prefix-display;
                        .lib-css(align-items, center, 1);
                        cursor: pointer;

                        &::before {
                            font-size: 21px;
                            line-height: inherit;
                            color: @color-white  !important;
                            content: '\e811';
                            .lib-css(font-family, @font-tt-icons);
                        }

                        .counter {
                            &.qty {
                                background: @color-red5;
                                color: @color-white;
                                height: 20px;
                                line-height: 20px;
                                font-size: 15px;
                                .lib-css(border-radius, 7px, 1);
                                display: inline-block;
                                min-width: 14px;
                                overflow: hidden;
                                padding: 1px 5px;
                                text-align: center;
                                white-space: normal;
                                .lib-css(font-weight, @font-weight__semibold);
                                position: absolute;
                                top: -16px;
                                right: -7px;
                                min-width: 24px;
                                margin: 0;

                                .counter-label {
                                    display: none;
                                }

                                &.empty {
                                    display: none;
                                }
                            }
                        }
                    }
                }

                &.active {
                    .block-minicart {
                        display: block;
                    }
                }
            }
        }
    }

    .nav-bar {
        background-color: @secondary__color;

        .nav-bar-container {
            width: 100%;
            margin: 0 auto;
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);
            .lib-css(align-items, center, 1);

            .nav-bar-link {
                color: @color-white;
                display: inline-block;
            }
        }
    }

    .nav-bar-redemption {
        &::before {
            font-size: 18px;
            line-height: inherit;
            content: '\E853';
            .lib-css(font-family, @font-tt-icons);
            margin: -5px 5px 0 0;
            vertical-align: middle;
            display: inline-block;
            .lib-css(font-weight, @font-weight__regular);
            text-align: center;
        }
    }

    .nav-bar-gift-card {
        &::before {
            font-size: 18px;
            line-height: inherit;
            content: '\E820';
            .lib-css(font-family, @font-tt-icons);
            margin: -5px 5px 0 0;
            vertical-align: middle;
            display: inline-block;
            .lib-css(font-weight, @font-weight__regular);
            text-align: center;
        }
    }

    .nav-bar-bonus {
        &::before {
            font-size: 18px;
            line-height: inherit;
            content: '\E854';
            .lib-css(font-family, @font-tt-icons);
            margin: -5px 5px 0 0;
            vertical-align: middle;
            display: inline-block;
            .lib-css(font-weight, @font-weight__regular);
            text-align: center;
        }
    }

    .nav-bar-clearance {
        &::before {
            font-size: 18px;
            line-height: inherit;
            content: '\e826';
            .lib-css(font-family, @font-tt-icons);
            margin: -5px 5px 0 0;
            vertical-align: middle;
            display: inline-block;
            .lib-css(font-weight, @font-weight__regular);
            text-align: center;
        }
    }

    .store-locator-top {
        margin-left: auto;
        background-color: @color-black-15;
        .lib-css(align-self, stretch, 1);

        .store-locator-wrapper {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);
            font-family: @font-family__base;
            color: @color-white;
            padding: 8px 15px 6px;
            position: relative;

            .store-selector {
                .store-selector-wrapper {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, center, 1);

                    .store-icon {
                        fill: @primary__color;
                        margin-right: 7px;

                        svg {
                            width: 100%;
                        }
                    }

                    .store-change {
                        .lib-css(font-weight, @font-weight__heavy);
                        text-transform: uppercase;
                        font-size: 16px;
                        cursor: pointer;
                    }

                    .store-selected {
                        &.active {
                            .store-details {
                                .store-dropdown-options {
                                    display: block;
                                }
                            }
                        }

                        .store-name {
                            white-space: nowrap;
                            display: block;
                            cursor: pointer;
                            .lib-heading-typography (
                                @_font-size: 14,
                                @_line-height: 14,
                                @_color: @color-white,
                            )
                        }

                        .store-details {
                            text-align: left;
                            padding-right: 15px;
                            font-family: @font-family-name__base;


                            .store-dropdown {
                                margin-top: 0;

                                .store-status {
                                    &::before {
                                        .lib-line-height(20);
                                    }
                                }
                            }

                            .store-status-text {
                                font-size: 14px;
                                .lib-line-height(20);
                                .lib-css(font-weight, @font-weight__bold);
                                font-family: @font-family__base;
                                color: @color-white;
                                cursor: pointer;
                                text-transform: capitalize;
                            }

                            .store-dropdown-options {
                                position: absolute;
                                top: 100%;
                                right: 0;
                                z-index: 18;
                                width: 100%;
                                padding: 20px;
                                text-align: left;
                                display: none;
                                background-color: @color-white;
                                border: 1px solid @color-gray4;
                                color: @color-gray2;
                                font-family: @font-family__base;

                                &::before {
                                    content: '';
                                    height: 0;
                                    width: 0;
                                    position: absolute;
                                    pointer-events: none;
                                    bottom: 100%;
                                    right: 7%;
                                    border: solid transparent;
                                    border-bottom-color: @color-gray4;
                                    border-width: 9px;
                                    margin-left: -9px;
                                }

                                h3 {
                                    margin: 0;
                                    text-transform: capitalize;
                                    .lib-heading-typography(
                                        @_font-size: 18,
                                        @_line-height: 26,
                                        @_color: @secondary__color
                                    );
                                }

                                .store-address {
                                    margin-bottom: 15px;

                                    span {
                                        font-size: 14px;
                                        line-height: 1.5;
                                        font-family: @font-family-name__base;
                                        .lib-css(font-weight, @font-weight__regular);
                                        color: @color-gray2;
                                    }
                                }

                                .store-directions {
                                    display: none;
                                }

                                .store-hours {
                                    border-bottom: 1px solid @color-gray4;
                                    margin-bottom: 15px;

                                    .store-timings {
                                        margin: 0;
                                        padding: 0;
                                        list-style: none;

                                        li {
                                            .lib-vendor-prefix-display;
                                            .lib-vendor-prefix-flex-wrap(wrap);
                                            margin-bottom: 3px;

                                            span {
                                                font-size: 14px;
                                                line-height: 1.5;
                                                font-family: @font-family-name__base;
                                                .lib-css(font-weight, @font-weight__regular);
                                                color: @color-gray2;

                                                &:first-child {
                                                    width: 40%;
                                                }
                                            }
                                        }
                                    }

                                    &.store-holiday {
                                        .time-label {
                                            padding-left: 0;
                                            padding-right: 0;
                                            font-size: 14px;
                                        }
                                    }

                                    &.store-spcecial-day {
                                        .table {
                                            margin-bottom: 20px;

                                            td {
                                                padding: 8px;
                                                line-height: 1.42857143;
                                                vertical-align: top;

                                                &.event-start {
                                                    padding-left: 0;
                                                }

                                                &.event-info {
                                                    padding-right: 0;
                                                }
                                            }

                                            .event-start-wrap {
                                                border: 1px solid @color-gray4;
                                                display: inline-block;
                                                text-align: center;
                                                text-transform: uppercase;
                                                width: 44px;

                                                .month {
                                                    display: block;
                                                    background-color: @color-gray4;
                                                    color: @color-gray20;
                                                    font-size: 12px;
                                                    line-height: 1.42857143;
                                                    padding: 0 8px;
                                                    text-align: center;
                                                    text-transform: uppercase;
                                                }

                                                .event-start-day {
                                                    display: block;
                                                    font-size: 18px;
                                                    line-height: 1.42857143;
                                                    text-align: center;
                                                    text-transform: uppercase;
                                                    color: @color-gray2;
                                                }
                                            }

                                            .event-info {

                                                .event-label,
                                                .event-date {
                                                    font-size: 14px;
                                                    font-family: @font-family-name__base;
                                                    .lib-css(font-weight, @font-weight__regular);
                                                    line-height: 1.42857143;
                                                    color: @color-gray2;
                                                }

                                                .event-time {
                                                    display: inline-block;
                                                    float: right;
                                                    padding: 2px 4px 1px;
                                                    font-size: 12px;
                                                    line-height: 1;
                                                    border: 1px solid @color-green;
                                                    .lib-css(border-radius, 2px, 1);
                                                    color: @color-green;
                                                }
                                            }
                                        }
                                    }
                                }

                                .store-actions {
                                    .btn {
                                        margin: 0 0 10px;
                                        padding: 8px 15px;
                                        line-height: 1.5;
                                        text-align: center;
                                        white-space: nowrap;


                                        &:last-child {
                                            margin-bottom: 0;
                                        }
                                    }

                                    .btn-details {
                                        display: block;
                                        width: 100%;
                                        color: @color-white;
                                        background-color: @primary__color;
                                        border: 2px solid @color-red5;
                                        .lib-css(font-weight, @font-weight__bold);
                                        font-family: @font-family-name__base;
                                        .lib-font-size(15);
                                        .lib-css(border-radius, 3px, 1);
                                        .lib-css(transition, all .2s ease, 1);
                                    }

                                    .btn-change {
                                        display: block;
                                        width: 100%;
                                        color: @secondary__color;
                                        background-color: @color-white;
                                        border: 2px solid @secondary__color;
                                        .lib-css(font-weight, @font-weight__bold);
                                        .lib-font-size(15);
                                        font-family: @font-family-name__base;
                                        .lib-css(border-radius, 3px, 1);
                                        .lib-css(transition, all .2s ease, 1);

                                        &:hover {
                                            background-color: @color-blue2;
                                            color: @color-white;
                                        }
                                    }
                                }
                            }
                        }

                    }
                }
            }
        }
    }

    .page-top-banner {
        &>div {
            max-width: 1282px;
            padding: 0 20px;
            margin: 0 auto;
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);
            .lib-css(align-items, center, 1);
        }

        .home-top-items {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(nowrap);
            .lib-css(align-items, center, 1);
            list-style: none;
            border-bottom: 1px solid @color-gray90;
            padding: 0;
            margin: 0;

            li {
                .lib-css(color, @secondary__color);
                .lib-line-height(60);
                margin: 0;
                text-transform: uppercase;
                white-space: nowrap;
                text-align: right;

                &:first-child {
                    text-align: left;
                }

                &.low-price {
                    a {
                        &::before {
                            width: 40px;
                            height: 40px;
                            background-image: url(../images/assurance_lpg.gif);
                            margin: 2px 3px 3px 0;
                        }
                    }
                }

                &.online-returns {
                    a {
                        &::before {
                            width: 36px;
                            height: 36px;
                            background-image: url(../images/assurance_returns.gif);
                            margin: 4px 5px 3px 0;
                        }
                    }
                }

                &.delivery-free {
                    a {
                        &::before {
                            width: 52px;
                            height: 52px;
                            background-image: url(../images/assurance_delivery.gif);
                            margin: 4px 5px 3px 0;
                        }
                    }
                }

                &.insider-rewards {
                    a {
                        &::before {
                            width: 36px;
                            height: 36px;
                            background-image: url(../images/assurance_insiders.gif);
                            margin: 0 5px 2px 0;
                        }
                    }
                }

                &.trade-reward {
                    a {
                        &::before {
                            width: 40px;
                            height: 40px;
                            background-image: url(../images/trade_rewards.gif);
                            margin: -7px 4px 0px 0;
                        }
                    }
                }

                a {
                    white-space: nowrap;
                    display: inline-block;
                    text-decoration: none;
                    .lib-heading-typography(15, 15, @secondary__color);

                    &::before {
                        content: '';
                        background-size: contain;
                        background-position: center;
                        background-repeat: no-repeat;
                        vertical-align: middle;
                        display: inline-block;
                    }
                }
            }
        }
    }

    .full-left-side,
    .sidebar-main {
        .apply-filter-btn {
            display: none;
        }

        &.active {
            .apply-filter-btn {
                position: fixed;
                bottom: 30px;
                z-index: 999;
                right: 10px;
                text-transform: capitalize;
                background-color: @color-blue;
                transition: background-color .3s,opacity .5s,visibility .5s;
                color: @color-white;
                display: block !important;
                .lib-css(border-radius, 3px, 1);
                padding: 7px 15px;
                .lib-font-size(16);
                .lib-line-height(24);
                border: 1px solid @color-gray-darken2;
            }
        }
    }

    @media screen and (max-width: 374px) {
        .page-header {
            .header {
                .logo {
                    max-width: 150px !important;
                }

                .link-login {
                    margin-left: 12px !important;
                }

                .minicart-wrapper {
                    margin-left: 12px !important;

                }
            }
        }
    }

    .page-print,
    .sales-order-print {
        .page-main {
            .action.nav-toggle {
                display: none;
            }
        }
    }

    .search-type-full {
        .page-header {
            .header { 
                .block-search {
                    .control { 
                        .new-search-autocomplete {
                            .search-autocomplete-dropdown {
                                position: fixed !important;
                                left: 0;
                                top: 0;
                                right: 0;
                                bottom: 0;
                                width: 100vw;
                                overflow: auto;
                                z-index: 50;
                                min-height: -webkit-fill-available;
                                padding: 25px 15px 15px;
                                background-color: @color-white;
                                box-shadow: 0 3px 3px 3px @color-black-10;

                                &.open {
                                    z-index: 30;
                                }
                            }

                            .search-autocomplete-wrapper {
                                width: 100% !important;
                                .lib-css(box-sizing, border-box, 1);
                                position: relative;

                                .col-left {
                                    padding: 0 10px;

                                    .search-autocomplete-categories {
                                        .categories-list-item {
                                            padding: 2.5px 0 !important;
                                            border-bottom: 0 !important;

                                            .category-container {
                                                padding: 0 !important;
                                            }
                                        }
                                    } 
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .page-header {
        .header {
            &.content {
                padding: 12px 15px 8px;
            }

            .nav-toggle {
                position: static;
                order: 1;
            }

            .logo {
                margin: 0;
                max-width: 210px;
                order: 2;

                img {
                    width: 100%;
                }
            }

            .block-search {
                order: 6;
                width: 100%;
                margin-top: 10px;
                z-index: 20 !important;

                .search-layout {
                    display: none;
                }

                &.active {
                    z-index: 150 !important;
                }

                .control {
                    margin: 0;
                    padding: 0;
                }

                .block-content {
                    .minisearch {
                        position: relative;
                    }

                    .actions {
                        display: block;
                        bottom: auto;
                        top: 0;
                        right: 0;

                        .action {
                            &.search {
                                top: 6px;
                                padding: 7px 0;
                            }
                        }
                    }
                }
            }

            .store-locator {
                order: 3;
                margin-left: auto;

                a {
                    width: 100%;
                    margin: 0;

                    &::before {
                        font-size: 23px;
                        margin-right: 1px;
                    }

                    span {
                        display: none;
                    }
                }
            }

            .link-login {
                order: 4;
                margin-left: 15px;

                .my-account {
                    &>a {
                        width: 100%;
                        margin: 0;


                        &::before {
                            font-size: 23px;
                            margin-right: 0;
                        }

                        span {
                            display: none;
                        }
                    }

                    .list-items {
                        width: 150px;

                        &#customer-info {
                            padding-left: 10px;
                            padding-right: 10px;
                        }

                        li {
                            a {
                                font-size: 15px;
                                line-height: 30px;
                            }
                        }
                    }
                }

            }

            .minicart-wrapper {
                order: 5;
                margin: 0;
                margin-left: 15px;
                z-index: 21;

                .action {
                    &.showcart {
                        width: 100%;
                        margin-left: 0;

                        &::before {
                            margin: -1px 0 0 0;
                            font-size: 24px;
                        }

                        .text {
                            display: none;
                        }

                        .counter {
                            &.qty {
                                min-width: fit-content;
                                height: auto;
                                font-size: 12px;
                                line-height: 17px;
                                font-weight: 500;
                                left: auto;
                                padding: 0 5px;
                                top: -14px;
                            }
                        }
                    }
                }

                .block-minicart {
                    position: absolute;
                    top: 100%;
                    .lib-css(box-shadow, 0 3px 3px @color-black-15, 1);
                    padding: 40px 20px 25px;
                    right: -15px;
                    width: 320px;
                    border: 1px solid @color-gray10;
                    margin-top: 15px;
                    .lib-css(border-radius, 3px, 1);

                    &:before {
                        content: '';
                        display: block;
                        height: 0;
                        position: absolute;
                        width: 0;
                        border: 14px solid;
                        border-color: transparent transparent @color-white;
                        z-index: 2;
                        top: -28px;
                        left: auto;
                        right: 13px;
                    }

                    &::after {
                        display: none;
                    }
                }
            }
        }

        &.affixed {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1001;
            .lib-css(padding, @indent__s 0);
            .lib-css(box-shadow, 0 0 10px rgba(0, 0, 0, 0.25), 1);

            .store-locator,
            .link-login {
                visibility: hidden;
                opacity: 0;
            }

            .block-search {
                display: none;
            }

            .header {
                padding-top: 2px;
                padding-bottom: 2px;
            }

            .minisearch .actions .action.search {
                top: 0;
            }
        }
    }

    .nav-bar {
        .shop-by-menu {
            [data-content-type=html] {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap(wrap);
            }      
        }

        [data-content-type=html] {
            width: 100%;
            padding: 0 5px;
            background-color: @primary__color;
            white-space: nowrap;
            overflow-x: auto;

            .nav-bar-link {
                font-size: 16px;
                line-height: 1.25;
                padding: 2px 10px 10px;
                text-transform: capitalize;
                .lib-css(font-weight, @font-weight__bold);

                &::before {
                    color: @color-white;
                }
            }
        }

        .shop-by-menu {
            width: 100%;
            
            [data-content-type=html] {
                overflow-x: unset;
                background: transparent;
                padding: 0;
            }
        }
    }

    .page-top-banner {
        
        & > div {
            padding: 0 15px;
        }

        .home-top-items {
            width: calc(100% + 5px);
            .lib-css(align-items, flex-start, 1);
            .lib-css(justify-content, space-between, 1);
            margin: 0 -2.5px;

            li {
                width: 100%;

                a {
                    .lib-font-size(10);
                    .lib-line-height(12);
                    white-space: initial;
                    .lib-vendor-prefix-display;
                    .lib-css(align-items, center, 1);
                    .lib-css(justify-content, center, 1);
                    .lib-css(flex-direction, column, 1);
                    text-align: center;
                    padding: 2px 2.5px 4px;
                }

                &.low-price,
                &.online-returns,
                &.delivery-free,
                &.insider-rewards,
                &.trade-reward {
                    a {
                        &::before {
                            width: 36px;
                            height: 36px;
                            margin: 0 0 3px;
                        }
                    }
                } 
            }
        }
    }

    .store-locator-top {
        min-height: 47px;

        .store-locator-wrapper {
            .store-selector {
                .store-selector-wrapper {
                    .store-icon {
                        max-width: 20px;
                    }

                    .store-selected {
                        width: ~"calc(100% - 27px)";
                        .lib-vendor-prefix-display;
                        .lib-vendor-prefix-flex-wrap(wrap);
                        .lib-css(align-items, center, 1);

                        .store-details { 
                            padding-left: 8px;
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    .page-header {
        .header {
            .logo {
                max-width: 172px;
                margin-right: auto;
            }

            .store-locator {
                margin-left: auto;
            }

            .minicart-wrapper {
                .action {
                    &.showcart {
                        svg {
                            width: 24px;
                            height: 24px;
                        }
                    }
                }

                .ui-dialog {
                    width: 100%;
                    height: 100%;
                    position: fixed;
                    left: 0;
                    top: 0;
                    z-index: 30;
                }

                .block-minicart {
                    max-width: 350px;
                    position: fixed;
                    top: 0;
                    height: 100%;
                    margin-top: 0;
                    border-radius: 0;
                    right: 0;
                    border: 0;
                    transition: .1s;
                    padding-top: 30px;
                    overflow: unset;
                    padding-right: 0;
                    padding-bottom: 0;

                    #minicart-content-wrapper {
                        overflow-y: auto;
                        height: 100%;
                        padding-bottom: 50px;
                        padding-right: 20px;
                    }

                    &::before {
                        display: none;
                    }

                    .block-content {
                        .minicart-items-wrapper {
                            border: 0;
                            margin: 0;
                            border-left: 0;
                            border-right: 0;
                            width: 100%;
                        }
                    }

                    .clear-cart-btn {
                        position: fixed;
                        left: auto;
                        bottom: 0;
                        padding-bottom: 15px !important;
                        max-width: 320px;
                        right: 0;
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .page-header {
        .header {
            &.content {
                padding: 12.5px 20px;
            }

            .nav-toggle {
                position: static;
            }

            .logo {
                margin: 0;
                width: 210px;
                margin-right: auto;
                position: relative;
                top: -2px;

                img {
                    width: 100%;
                }
            }

            .block-search {
                padding-right: 0;
                padding-left: 20px;
                max-width: 42.7%;
                width: 100%;
                flex: 1;
                margin-right: auto;

                &.active {
                    .search-layout { 
                        display: block;
                    }
                }

                .search-layout { 
                    content: "";
                    background: @color-black-75;
                    left: 0;
                    top: 0;
                    height: 100vh;
                    width: 100vw;
                    position: fixed;
                    transition: opacity 0.1s ease-in-out;
                    display: none;
                }

                .control {
                    .search-autocomplete-container {
                        width: 100%;
                        top: 0;
                    }
                }
            }

            .store-locator {
                min-width: 24px;
                margin-left: auto;

                a {
                    span {
                        display: none;
                    }

                    svg {
                        margin-right: 2px;
                    }
                }
            }

            .link-login {
                min-width: 24px;
                margin-left: 12px;

                .my-account {
                    &>a {
                        span {
                            display: none;
                        }
                    }

                    .list-items {
                        width: 180px;

                        li {
                            a {
                                .lib-font-size(16);
                                .lib-line-height(26);
                            }
                        }
                    }
                }
            }

            .minicart-wrapper {
                min-width: 24px;
                margin-left: 12px;

                .action {
                    &.showcart {
                        width: 100%;

                        &::before {
                            margin: -1px 0 0 0;
                        }

                        .text {
                            display: none;
                        }
                    }
                }

                .block-minicart {
                    z-index: 20;
                    right: -15px;
                    width: 390px;
                    padding-bottom: 0;
                    .lib-css(border-radius, 3px, 1);

                    &:before {
                        content: '';
                        display: block;
                        height: 0;
                        position: absolute;
                        width: 0;
                        border: 14px solid;
                        border-color: transparent transparent @color-white;
                        z-index: 2;
                        top: -28px;
                        left: auto;
                        right: 12px;
                    }

                    &::after {
                        content: '';
                        display: block;
                        height: 0;
                        position: absolute;
                        width: 0;
                        border: 15px solid;
                        border-color: transparent transparent @color-gray-light2;
                        z-index: 1;
                        top: -31px;
                        left: auto;
                        right: 11px;
                    }
                }
            }
        }
    }

    .nav-bar {
        [data-content-type=html] {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);
            .lib-css(align-self, stretch, 1);

            .nav-bar-link {
                line-height: 1.334;
                padding: 12px 10px;
                text-transform: uppercase;
                .lib-heading-typography(
                    @_font-size: 14
                );
                &::before {
                    color: @primary__color;
                }

                &:hover {
                    text-decoration: none;
                    background-color: @color-black-40;
                }
            }
        }
    }

    .store-locator-top {

        .store-locator-wrapper {
            border-left: 1px solid @color-white-45;
            border-right: 1px solid @color-white-45;

            .store-selector {
                .store-selector-wrapper {

                    .store-icon {
                        max-width: 30px;
                    }
                }
            }
        }
    }

    .page-top-banner {
        display: block;

        .home-top-items {
            width: 100%;
            overflow-x: auto;
            gap: 20px;
        }
    }

    .search-type-full { 
        .page-header {
            .header { 
                .block-search {
                    .control { 
                        .new-search-autocomplete {
                            .search-autocomplete-wrapper {
                                &.search-result {
                                    width: ~"calc(42.8% - 25px)" !important;
                                    left: ~"calc(50% + 8.9vw)";
                                    transform: translateX(-50%);
                                    position: relative;
                                }

                                .col-left { 
                                    padding: 0;
                                }
                            }

                            .search-autocomplete-dropdown {
                                padding: 14px 15px 15px;
                                position: fixed !important;
                                top: 0;
                                left: 0;
                                bottom: auto;
                                width: 100vw;
                                max-height: 100vh;
                                overflow-y: auto;
                                min-height: 50vh;
                                box-shadow: 0 10px 25px @color-black-75;
                            }
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .page-header {
        .header {
            .nav-toggle {
                display: none;
            }

            .logo {
                width: 300px;
                margin-right: 0;
            }

            .block-search {
                max-width: 37.6%;
                padding-right: 20px;
                margin-right: 0;
                padding-left: 50px;

                .control {
                    .search-autocomplete-container {
                        .search-autocomplete-dropdown {
                            .search-autocomplete-wrapper {
                                .col-left {
                                    .search-autocomplete-categories {
                                        &.suggested-keywords {
                                            h5 {
                                                width: 100%;
                                                max-width: 100%;
                                                padding-bottom: 9px;
                                            }

                                            .categories-list {
                                                .categories-list-item {
                                                    padding: 4px 0;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .store-locator {
                margin-left: auto;

                a {
                    span {
                        display: block;
                    }
                }
            }

            .link-login {
                margin-left: auto;

                .my-account {
                    &>a {
                        span {
                            display: block;
                        }
                    }

                    .list-items {
                        li {
                            a {
                                letter-spacing: 0;
                            }
                        }
                    }
                }
            }

            .minicart-wrapper {
                margin: 0;
                margin-left: auto;

                .action {
                    &.showcart {

                        &::before {
                            margin: -1px 5px 0 0;
                        }

                        .text {
                            display: block;
                        }
                    }
                }

                .block-minicart {
                    &:before {
                        right: 63px;
                    }

                    &::after {
                        right: 62px;
                    }
                }
            }
        }
    }

    .nav-bar {
        .nav-bar-container {
            max-width: 1282px;
            padding: 0 20px;

            [data-content-type=html] {
                margin-left: 0;
            }
        }
    }

    .store-locator-top {
        min-width: 300px;
    }

    .page-top-banner {
        .home-top-items {
            .lib-vendor-prefix-flex-wrap(nowrap);
            .lib-css(justify-content, space-between, 1);
            gap: 5px;
            overflow: unset;

            li {
                overflow: hidden;
                width: auto;

                a {
                    .lib-font-size(14);
                    .lib-line-height(14); 
                    .lib-css(letter-spacing, -0.5px);
                }
            }
        }
    }

    .search-type-full { 
        .page-header {
            .header { 
                .block-search {
                    .control { 
                        .new-search-autocomplete {
                            .search-autocomplete-wrapper {
                                &.search-result {
                                    display: flex;
                                    width: ~"calc(74vw - 174px)" !important;
                                    left: 356px !important;
                                    transform: unset;

                                    .col-left {
                                        width: 50%;
                                        flex: unset;
                                    }

                                    .col-right {
                                        width: 50%;
                                        margin-top: -44px;
                                    }
                                }
                            }

                            .search-autocomplete-dropdown {
                                padding-top: 23px;
                            }
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__ml) {
    .page-header {
        .header {
            .block-search {
                max-width: 41%;
            }
        }
    }

    .page-top-banner {
        .home-top-items {
            gap: 15px;

            li {
                width: auto;

                a {
                    .lib-font-size(16);
                    .lib-line-height(16); 
                }
            }
        }
    }

    .search-type-full { 
        .page-header {
            .header { 
                .block-search {
                    .control { 
                        .new-search-autocomplete {
                            .search-autocomplete-wrapper {
                                &.search-result {
                                    width: 878.44px !important;
                                    left: ~"calc(50% + 159px)" !important;
                                    transform: translateX(-50%);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
