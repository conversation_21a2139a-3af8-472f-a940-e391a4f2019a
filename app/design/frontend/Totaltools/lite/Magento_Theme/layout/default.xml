<!--
/**
 * @category    Totaltools
 * @package     Totaltools/lite
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   © 2023 Total Tools Pty Ltd. <https://totaltools.com.au>
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="head.additional">
            <block name="fallback.fonts" class="Magento\Framework\View\Element\Template"
                template="Magento_Theme::html/fallback-fonts.phtml" before="-" />
        </referenceBlock>
        <referenceBlock name="logo">
            <arguments>
                <argument name="logo_file" xsi:type="string">images/logo.svg</argument>
                <argument name="logo_img_width" xsi:type="number">300</argument>
                <argument name="logo_img_height" xsi:type="number">60</argument>
            </arguments>
        </referenceBlock>
        <move element="register-link" destination="top.links" after="authorization-link" />
        <move element="minicart" destination="header-wrapper" after="-" />
        <referenceBlock name="header.panel.wrapper" remove="true" />
        <referenceContainer name="header-wrapper">
            <block class="Magento\Customer\Block\Account\AuthorizationLink" after="topSearch"
                name="link-login" template="Magento_Theme::html/top-links.phtml" />
        </referenceContainer>
        <!-- Page Top -->
        <referenceContainer name="page.top">
            <referenceBlock name="navigation.sections" remove="true" />
            <container name="nav.bar"
                htmlTag="div" htmlClass="nav-bar" before="-">
                <container name="nav.bar.container" htmlTag="div" htmlClass="nav-bar-container">
                    <block class="Totaltools\Megamenu\Block\ShortcodeMenu"
                        name="catalog.topnav.custom.ttl">
                        <arguments>
                            <argument name="menuid" xsi:type="helper"
                                helper="Totaltools\Megamenu\Helper\Data::getPrimaryMenuId" />
                        </arguments>
                    </block>
                    <block class="Magento\Cms\Block\Block" name="nav.bar.static">
                        <arguments>
                            <argument name="block_id" xsi:type="string">navbar_static</argument>
                        </arguments>
                    </block>
                </container>
            </container>
        </referenceContainer>
        <referenceContainer name="page.top">
            <container name="page_top_banner" as="page_top_banner" label="Page Top Banner"
                htmlTag="div" htmlClass="page-top-banner" before="breadcrumbs">
                <block class="Magento\Cms\Block\Block" name="home_top_items">
                    <arguments>
                        <argument name="block_id" xsi:type="string">home_top_items</argument>
                    </arguments>
                </block>
            </container>
        </referenceContainer>
        <referenceContainer name="content">
            <container name="top.content" label="Widgets - Top Content" before="-" />
            <container
                name="home_message" as="home_message" htmlTag="div" htmlClass="home-message"
                after="top.content" />
        </referenceContainer>
        <referenceContainer name="footer-container">
            <container name="footer_top" as="footer_top" label="Footer Top" htmlTag="div"
                htmlClass="footer-top" before="-">
                <container name="footer_top_inner" as="footer_top_inner" label="Page Footer Inner"
                    htmlTag="div" htmlClass="footer content"></container>
            </container>

            <container
                name="footer_middle" as="footer_middle" label="Footer Middle" htmlTag="div"
                htmlClass="footer-middle" after="footer_top">
                <container name="footer_middle_inner" as="footer_top_inner"
                    label="Page Footer Inner" htmlTag="div" htmlClass="footer content">
                    <container name="footer_middle_left" as="footer_middle_left" htmlTag="div"
                        htmlClass="footer_middle_left">
                        <block class="Magento\Framework\View\Element\Template" name="home_bottom_franchise"  template="Magento_Theme::footer-block-left.phtml"  />
                    </container>

                    <container name="footer_middle_right" as="footer_middle_right" htmlTag="div"
                        htmlClass="footer_middle_right">
                        <block class="Magento\Framework\View\Element\Template" name="home_bottom_slogant"  template="Magento_Theme::footer-block-right.phtml"  />
                    </container>
                </container>
            </container>

            <container
                name="footer_bottom" as="footer_bottom" label="Footer Bottom" htmlTag="div"
                htmlClass="footer-bottom" after="footer_middle" />

            <container name="footer_copyright"
                as="footer_copyright" label="Footer Copyright" htmlTag="div"
                htmlClass="footer-copyright" after="-">
                <container name="footer_copyright_inner" as="footer_copyright_inner"
                    label="Page Copyright Inner" htmlTag="div" htmlClass="footer content">
                    <container name="footer_copyright_right" as="footer_copyright_right"
                        htmlTag="div" htmlClass="payments-wrapper" after="-">
                        <block class="Magento\Framework\View\Element\Template" name="footer_payments"  template="Magento_Theme::footer-block-payments.phtml"  />
                    </container>
                </container>
            </container>

        </referenceContainer>
        <referenceContainer name="footer">
            <referenceBlock name="store_switcher" remove="true" />
            <referenceBlock
                name="footer_links" remove="true" />

            <container name="footer_customer_care"
                as="footer_customer_care" htmlTag="div" htmlClass="footer_customer_care" before="-">
                <block class="Magento\Cms\Block\Block" name="footer_nav_left">
                    <arguments>
                        <argument name="block_id" xsi:type="string">footer_nav_left</argument>
                    </arguments>
                </block>
            </container>
            <container
                name="footer_links_general" as="footer_links_general" htmlTag="div"
                htmlClass="footer_links_general" after="footer_customer_care">
                <block class="Magento\Cms\Block\Block" name="footer_nav_mid">
                    <arguments>
                        <argument name="block_id" xsi:type="string">footer_nav_mid</argument>
                    </arguments>
                </block>
            </container>
            <container
                name="footer_links_right" as="footer_links_right" htmlTag="div"
                htmlClass="footer_links_right" after="footer_links_general">
                <block class="Magento\Cms\Block\Block" name="footer_nav_right">
                    <arguments>
                        <argument name="block_id" xsi:type="string">footer_nav_right</argument>
                    </arguments>
                </block>
            </container>
            <container
                name="footer_join_community" as="footer_join_community" htmlTag="div"
                htmlClass="footer_join_community" after="footer_links_right">
                <block class="Magento\Cms\Block\Block" name="footer_nav_socials">
                    <arguments>
                        <argument name="block_id" xsi:type="string">footer_nav_socials</argument>
                    </arguments>
                </block>
            </container>
            <referenceBlock
                name="report.bugs" remove="true" />
        </referenceContainer>
        <referenceBlock name="customer_account_navigation">
            <referenceBlock name="customer-account-navigation-account-edit-link" remove="true" />
        </referenceBlock>
        <referenceBlock name="form.subscribe" remove="true" />
        <move element="footer" destination="footer_bottom" />
        <move element="copyright" destination="footer_copyright_inner" before="-" />
        <referenceContainer name="before.body.end">
             <block class="Magento\Framework\View\Element\Template" name="back.to.top" template="Magento_Theme::html/backtotop.phtml"></block>
        </referenceContainer>
    </body>
</page>