& when (@media-common = true) {
    .breadcrumbs {
        margin-top: 5px;
        margin-bottom: 10px;

        .items {
            margin: 0;
            padding: 0;
            list-style: none;
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            .lib-css(align-items, center, 1);

            .item {
                margin: 0;
                display: inline-block;
                .lib-font-size(12);
                color: @color-gray64;
                font-family: @font-family-name__base;

                &:not(:last-child) {
                    &:after {
                        content: '';
                        display: inline-block;
                        padding-left: 4px;
                        padding-right: 4px;
                        font-size: inherit;
                        line-height: inherit;
                        color: @secondary__color;
                        content: '\e802';
                        font-family: @font-tt-icons;
                        vertical-align: middle;
                        display: inline-block;
                        .lib-css(font-weight, @font-weight__regular);
                    }
                }

                a {
                    .lib-font-size(12);
                    font-weight: 500;
                    font-family: @font-family-name__base;
                    color: @secondary__color;
                    text-decoration: none;
                }

                strong {
                    .lib-font-size(12);
                    font-weight: 500;
                    font-family: @font-family-name__base;
                    color: @color-gray40;
                }
            }
        }
    }

    .columns {
        .lib-vendor-prefix-display;
        .lib-vendor-prefix-flex-wrap;
        .lib-vendor-box-align(flex-start);

        .catalog-category-view & {
            position: relative;
        }

        .sidebar-additional {
            display: none;
        }

        .column {
            &.main {
                width: 100%;

                .catalog-topnav {
                    display: none;
                }

                .category-view {
                    margin-bottom: 15px;

                    .page-title-wrapper {
                        display: block;
                        padding: 0 0 10px;

                        &:last-child {
                            padding-bottom: 0px;
                        }

                        .page-title {
                            color: @color-gray20;
                            font-family: @font-family-name__base;
                            .lib-css(font-weight, @font-weight__bold);
                            .lib-font-size(28);
                            .lib-line-height(42);
                            .lib-css(letter-spacing, -0.25px);
                            text-transform: uppercase;
                            margin: 0;
                        }
                    }

                    .category-short-description {
                        margin-bottom: 20px;

                        p {
                            color: @color-gray20;
                            font-family: @font-family-name__base;
                            .lib-font-size(16);
                            .lib-line-height(20);
                            margin: 0;

                            a {
                                color: @secondary__color;

                                &:hover {
                                    text-decoration: underline;
                                }
                            }
                        }
                    }
                }

                .view-all-products-container {
                    margin-top: 40px;

                    .view-all-products {
                        background: @color-blue;
                        border: 2px solid @color-blue8;
                        color: @color-white;
                        cursor: pointer;
                        display: inline-block;
                        font-weight: 700;
                        margin: 0;
                        padding: 9.25px 10px;
                        font-size: 14px;
                        line-height: 1.25;
                        border-radius: 3px;
                        padding: 10px;
                        text-align: center;
                        &:hover {
                            background: @color-blue8;
                            border-color: @color-blue5;
                            text-decoration: unset;
                        }
                    }
                }

                .category_brands {
                    color: @color-gray20;
                    font-family: @font-family-name__base;
                    .lib-css(font-weight, @font-weight__bold);
                    .lib-font-size(17.5);
                    .lib-line-height(28);
                    text-transform: uppercase;
                    margin: 0 0 20px;
                }

                .subcat {
                    &.wrapper {
                        margin: 0 0 40px;

                        .subcategories {
                            list-style: none;
                            margin: 0;
                            padding: 0;
                            position: relative;
                            top: 1px;
                            left: 1px;
                            .lib-vendor-prefix-display;
                            .lib-vendor-prefix-flex-wrap(wrap);

                            .subcategory {
                                margin: -1px 0 0 -1px;
                                padding: 0;
                                border: 1px solid @color-gray4;
                                text-align: center;

                                .subcategory-link {
                                    display: block;
                                    width: 100%;
                                    height: 100%;
                                    padding: 10px;
                                    color: @secondary__color;
                                    border: 1px solid transparent;
                                    .lib-css(transition, border-color .2s ease, 1);

                                    &:hover {
                                        text-decoration: none;
                                        border-color: @secondary__color;
                                    }

                                    .subcategory-image {
                                        height: 135px;
                                        margin-bottom: 15px;

                                        img {
                                            margin: 0 auto;
                                            max-height: 135px;
                                        }
                                    }

                                    .subcategory-title {
                                        font-family: @font-family-name__base;
                                        .lib-font-size(18);
                                        .lib-line-height(20);
                                        .lib-css(font-weight, @font-weight__bold);
                                        .lib-css(letter-spacing, -0.25px);
                                        .lib-css(transition, all .2s ease, 1);
                                    }

                                    .subcategory-count {
                                        .lib-font-size(14);
                                        color: @color-gray40;
                                    }
                                }
                            }
                        }
                    }
                }

                .category-longtail-keywords {
                    margin-top: 20px;

                    ul {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 10px;
                        margin: 0;
                        padding: 0;

                        li {
                            list-style: none;
                            margin: 0;

                            a {
                                font-size: 16px;
                                line-height: 1.25;
                                display: inline-block;
                                border: 1px solid @color-blue;
                                border-radius: 7px;
                                padding: 10px 12px 9px;
                                cursor: pointer;

                                &:hover {
                                    background: @color-blue;
                                    color: @color-white;
                                    text-decoration: unset;
                                }
                            }
                        }
                    }
                }
                
                .category-view-description {
                    margin: 30px 0 0;

                    .category-description {
                        p {
                            color: @color-gray20;
                            font-family: @font-family-name__base;
                            .lib-font-size(16);
                            .lib-line-height(20);
                            margin: 0 0 20px;

                            &:last-child {
                                margin-bottom: 0;
                            }

                            a {
                                color: @secondary__color;

                                &:hover {
                                    text-decoration: underline;
                                }
                            }
                        }
                    }
                }

                #amasty-shopby-product-list,
                #instant-search {
                    .toolbar-products {
                        margin-bottom: 25px;
                        padding: 10px;
                        background: @color-white-fog;
                        text-align: center;
                        min-height: 58px;
                        .modes {
                            display: flex;
                            gap: 5px;
                            margin-right: 20px;
                            float: left;

                            .modes-label {
                                display: none;
                            }

                            .modes-mode {
                                border: 0;
                                .lib-css(font-weight, @font-weight__regular);
                                line-height: 1;
                                padding: 5px;
                                margin: 0 1px;
                                background: @color-gray92;
                                text-align: center;
                                display: inline-flex;
                                align-items: center;
                                justify-content: center;
                                text-decoration: none;
                                width: 36px;
                                height: 36px;

                                span {
                                    display: none;
                                }

                                &.active {
                                    background: @color-blue;
                                    &::before {
                                        color: @color-white !important;
                                    }
                                }

                                &.mode-grid {
                                    border-radius: 3px 0 0 3px;
                                    .lib-css(border-radius, 3px 0 0 3px, 1);

                                    &::before {
                                        font-size: 20px;
                                        line-height: inherit;
                                        color: @color-gray1;
                                        content: '\e81a';
                                        font-family: @font-tt-icons;
                                        vertical-align: middle;
                                        display: inline-block;
                                        .lib-css(font-weight, @font-weight__regular);
                                        text-align: center;
                                    }
                                }

                                &.mode-list {
                                    border-radius: 0 3px 3px 0;
                                    .lib-css(border-radius, 0 3px 3px 0, 1);

                                    &::before {
                                        font-size: 21px;
                                        line-height: inherit;
                                        color: @color-gray1;
                                        content: '\e81b';
                                        font-family: @font-tt-icons;
                                        vertical-align: middle;
                                        display: inline-block;
                                        .lib-css(font-weight, @font-weight__regular);
                                        text-align: center;
                                    }
                                }
                            }
                        }

                        .toolbar-amount {
                            display: inline-block;
                            margin: 0 16px 0 0;
                            padding: 6px 0 0;
                            vertical-align: middle;
                            .lib-font-size(18);
                            .lib-line-height(26);
                            float: left;

                            .toolbar-number {
                                font-family: @font-family-name__base;
                                color: @secondary__color;
                                .lib-css(font-weight, @font-weight__bold);
                            }
                        }

                        .toolbar-sorter {
                            margin-top: 5px;
                            float: right;

                            .sorter-label {
                                .lib-font-size(14);
                                color: @color-gray20;
                                font-family: @font-family-name__base;
                            }

                            .sorter-options {
                                margin: 0 0 0 7px;
                                width: auto;
                                height: 32px;
                                .lib-font-size(14);
                                .lib-line-height(30);
                                font-family: @font-family-name__base;
                                padding: 0 40px 0 15px;
                                .lib-css(border-radius, 3px, 1);

                            }

                            .sorter-action {
                                vertical-align: top;
                                display: inline-block;
                                text-decoration: none;

                                &::before {
                                    font-size: 14px;
                                    line-height: 32px;
                                    color: @color-light-blue;
                                    content: '\e81d';
                                    font-family: @font-tt-icons;
                                    margin: 0 5px;
                                    vertical-align: middle;
                                    display: inline-block;
                                    font-weight: 400;
                                    text-align: center;
                                }

                                span {
                                    display: none;
                                }
                            }
                        }
                    }

                    .products {
                        &.wrapper {
                            position: relative;
                            margin: 30px 0 0;
                        }

                        &~.toolbar-products {
                            background: none;
                            .lib-vendor-prefix-display;
                            .lib-vendor-prefix-flex-wrap(wrap);
                            .lib-css(align-items, center, 1);
                            .lib-vendor-prefix-flex-direction(row-reverse);
                            padding-left: 0;
                            padding-right: 0;
                            margin: 30px 0 0;

                            .limiter {
                                margin-top: 0 !important;
                            }
                        }
                    }

                    .products-grid {
                        .products {
                            &.items {
                                width: 100%;
                                padding: 0;
                                margin: 0;
                                list-style: none;
                                .lib-vendor-prefix-display;
                                .lib-vendor-prefix-flex-wrap(wrap);
                            }
                        }
                    }
                }
            }
        }
    }

    .products-grid {
        .product-items {
            list-style: none;
            margin: 0;
            padding: 0;
            width: 100%;
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);
        }

        .product-item {
            margin-bottom: 15px;

            .product-item-info {
                width: 100%;
                max-width: 100%;
                text-align: center;
                border: 2px solid transparent;
                padding: 10px;
                .lib-css(border-radius, 3px, 1);
                position: relative;
                z-index: 1 !important;

                &:hover {
                    border-color: @secondary__color;
                }

                .product-item-photo {
                    display: block;
                    margin: 0 auto 20px;
                    max-width: 200px;

                    .product-image-container {
                        position: relative;
                        display: inline-block;
                        max-width: 100%;
                        width: 100% !important;

                        .product-image-wrapper {
                            height: 0;
                            display: block;
                            position: relative;
                            z-index: 1;
                            overflow: hidden;
                            padding-bottom: 100% !important;
                            text-align: left;
                        }

                        .product-image-photo {
                            display: block;
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            margin: auto 4%;
                            max-width: 92%;
                            width: 100%;
                            aspect-ratio: 1/1;
                            object-fit: contain;
                            padding: 0 1px;
                            box-sizing: border-box;
                            transition: all .3s linear;
                        }

                        .bss-bt-quickview {
                            display: none;
                        }
                    }
                }

                .product-reviews-summary {
                    position: absolute;
                    top: auto;
                    left: 50%;
                    z-index: 10;
                    margin: 0 auto;
                    .lib-css(transform, translate(-50%, -20px), 1);
                }

                .product-item-details {
                    position: relative;
                    .lib-css(margin-top, @indent__m);

                    .product-item-name {
                        display: block;
                        margin: 0;
                        word-wrap: break-word;
                        font-family: @font-family-name__base;
                        .lib-font-size(14);
                        .lib-line-height(14);
                        color: @color-gray20;
                        .lib-css(font-weight, @font-weight__bold);
                        min-height: unset !important;
                        height: 56px !important;
                        overflow: hidden;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 4;

                        a {
                            color: @color-gray20;
                        }
                    }

                    .sku {
                        display: none;
                    }

                    .product-item-description {
                        display: none;
                    }
                }

                .price-box {
                    margin: 0;
                    text-align: center;
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, center, 1);
                    .lib-vendor-prefix-flex-direction(column);
                    .lib-css(justify-content, center, 1);
                    white-space: nowrap;
                    min-height: 104.5px !important;
                    word-wrap: unset !important;

                    .price-container {
                        display: inline-block;

                        .price-label {
                            display: none;
                        }

                        .price {
                            .lib-css(order, 2, 1);
                            .lib-heading-typography (
                                @_font-size: 42,
                                @_color: @primary__color,
                                @_letter-spacing: -2px,
                            );

                            .currency-symbol,
                            .price-decimal {
                                top: 1px;
                                .lib-font-size(21);
                                vertical-align: super;
                                position: relative;
                            }
                        }
                    }

                    .price-to {
                        .price-container {
                            .price-label {
                                display: none;
                            }
                        }
                    }

                    .old-price {
                        order: 3;

                        .price-label {
                            color: @color-gray11;
                            .lib-font-size(15);
                            .lib-line-height(15);
                            .lib-css(font-weight, 500);
                            display: inline-block;
                        }

                        .price {
                            color: @color-gray11;
                            .lib-font-size(15);
                            .lib-line-height(15);
                            letter-spacing: -1px;
                            text-decoration: line-through;
                            font-family: @font-family-name__base !important;
                            .lib-css(font-weight, @font-weight__semibold) !important;

                            .currency-symbol,
                            .price-decimal {
                                .lib-font-size(15);
                                .lib-line-height(15);
                                vertical-align: baseline;
                                position: static;
                            }
                        }
                    }

                    .special-price {
                        order: 2;

                        .price-label {
                            display: none;
                        }
                    }
                }

                .product-item-actions {
                    display: block;
                    margin: 10px 0 0;

                    .actions-primary {
                        display: block;
                        width: 100%;
                    }

                    .tocart,
                    .todetails {
                        background-color: @primary__color;
                        border: 2px solid @color-red;
                        color: @color-white;
                        width: 100%;
                        padding: 7px 8px 5px;
                        white-space: nowrap !important;
                        .lib-css(border-radius, 0, 1);
                        font-family: @font-family-name__base;
                        .lib-font-size(16);
                        .lib-line-height(24);
                        min-width: 118px;
                        .lib-css(font-weight, @font-weight__bold);

                        &:hover {
                            background-color: @color-red3;
                        }

                        &::after {
                            font-size: inherit;
                            line-height: inherit;
                            color: inherit;
                            .lib-css(font-family, @font-tt-icons);
                            margin: -5px 0 0 3px;
                            vertical-align: middle;
                            display: inline-block;
                            .lib-css(font-weight, @font-weight__regular);
                        }
                    }

                    .tocart {
                        &::after {
                            content: '\e811';
                        }
                    }

                    .todetails {
                        &::after {
                            content: '\E82A';
                        }
                    }

                    &>a,
                    .actions-secondary {
                        display: none;
                    }
                }

                .right-info {
                    display: none;
                }
            }
        }

        .slick-slide {
            .product-item .product-item-info {
                &:hover {
                    border-color: transparent;
                }
            }
        }
    }

    .products-list {
        .product-items {
            list-style: none;
            margin: 0;
            padding: 0;
            width: 100%;
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);

            .product-item {
                width: 100%;
                margin-bottom: 15px;
                min-height: unset !important;

                .product-item-info {
                    width: 100%;
                    max-width: 100%;
                    text-align: center;
                    border: 2px solid transparent;
                    padding: 10px;
                    .lib-css(border-radius, 3px, 1);
                    position: relative;
                    z-index: 1 !important;
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, flex-start, 1);
    
                    &:hover {
                        border-color: @secondary__color;
                    }

                    .product-item-photo {
                        display: block;
                        width: 100%;
                        font-size: 0;
    
                        .product-image-container {
                            position: relative;
                            display: inline-block;
                            max-width: 100%;
                            width: 100% !important;
                            height: auto;
                            aspect-ratio: 1 / 1;
    
                            .product-image-wrapper {
                                height: 0;
                                display: block;
                                position: relative;
                                z-index: 1;
                                overflow: hidden;
                                padding-bottom: 100% !important;
                                width: 100%;
                            }
    
                            .product-image-photo {
                                display: block;
                                position: absolute;
                                top: 0;
                                bottom: 0;
                                left: 0;
                                right: 0;
                                margin: auto;
                                max-height: 100%;
                                max-width: 100%;
                                object-fit: contain;
                                box-sizing: border-box;
                            }

                            .product-hover-image {
                                margin: 0;
                                max-width: 100%;
                            }
    
                            .bss-bt-quickview {
                                display: none;
                            }
                        }
                    }

                    .product-item-details {
                        text-align: left;

                        .product-item-name {    
                            margin: 0;
                            word-wrap: break-word;
                            font-family: @font-family-name__base;
                            font-size: 14px;
                            line-height: 1.25;
                            color: @color-gray20;
                            font-weight: 700;
                            margin-bottom: 12px;
                            display: block;
                            height: auto !important;
                            
                            a {
                                color: @color-gray20;
                            }
                        }
                    }

                    .price-box {
                        margin: 0;
                        display: flex;
                        flex-wrap: wrap;
                        .lib-css(flex-direction, column, 1);
                        .lib-css(align-items, flex-start, 1);
                        height: auto !important;

                        .special-price {
                            order: 2;
                            .price-label {
                                display: none;
                            }
                        }

                        .price-container {
                            display: inline-block;

                            .price {
                                .lib-heading-typography (
                                    @_font-size: 42,
                                    @_color: @primary__color,
                                    @_letter-spacing: -2px,
                                );

                                .currency-symbol,
                                .price-decimal {
                                    top: 1px;
                                    .lib-font-size(21);
                                    vertical-align: super;
                                    position: relative;
                                }

                                .decimal-dot {
                                    font-size: 20%;
                                    line-height: inherit;
                                    color: transparent;
                                    vertical-align: super;
                                }
                            }
                        }

                        .old-price { 
                            order: 3;

                            .price-label {
                                color: gray;
                                .lib-font-size(15);
                                .lib-line-height(15);
                                font-weight: 500;
                                display: inline-block;
                            }

                            .price {
                                color: gray;
                                .lib-font-size(15);
                                .lib-line-height(15);
                                letter-spacing: -1px;
                                text-decoration: line-through;
                                font-family: @font-family-name__base !important;
                                font-weight: 600 !important;

                                .currency-symbol,
                                .decimal-dot,
                                .price-decimal {
                                    .lib-font-size(15);
                                    .lib-line-height(15);
                                    vertical-align: baseline;
                                    position: static;
                                }
                            }
                        }

                        .you-save-statement {
                            order: 1;
                        }
                    }

                    .product-item-actions {
                        .tocart,
                        .todetails {
                            background-color: @primary__color;
                            border: 2px solid @color-red;
                            color: @color-white;
                            width: 100%;
                            padding: 7px 8px 5px;
                            white-space: nowrap !important;
                            .lib-css(border-radius, 0, 1);
                            font-family: @font-family-name__base;
                            .lib-font-size(16);
                            .lib-line-height(24);
                            min-width: 118px;
                            .lib-css(font-weight, @font-weight__bold);

                            &:hover {
                                background-color: @color-red3;
                            }

                            &::after {
                                font-size: inherit;
                                line-height: inherit;
                                color: inherit;
                                .lib-css(font-family, @font-tt-icons);
                                margin: -5px 0 0 3px;
                                vertical-align: middle;
                                display: inline-block;
                                .lib-css(font-weight, @font-weight__regular);
                            }
                        }

                        .tocart {
                            &::after {
                                content: '\e811';
                            }
                        }

                        .todetails {
                            &::after {
                                content: '\E82A';
                            }
                        }

                        .actions-secondary {
                            display: none;
                        }
                    }
                }
            }
        }
    }

    .price-box {
        .special-price,
        .normal-price,
        .price-container {
            .price {
                .decimal-dot {
                    font-size: 20%;
                    line-height: inherit;
                    color: transparent;
                    vertical-align: super;
                }
            }
        }

        .you-save-statement {
            .decimal-dot {
                font-size: 20%;
                line-height: inherit;
                color: transparent;
                vertical-align: super;
            }
        }

        .old-price {
            .price {
                .decimal-dot {
                    font-size: inherit;
                    line-height: inherit;
                    color: inherit;
                    vertical-align: unset;
                }
            }
        }
    }

    .block.related,
    .block.upsell {
        padding-left: 0;
        padding-right: 0;

        .products-related,
        .products-upsell {
            .slick-slider {
                display: block;
                width: 100% !important;
            }

            .slick-track {
                margin: 0;
            }

            .product-item {
                width: 50%;
            }
        }
    }

    /**
    * Shipping label
    */
    .shipping-label {
        .lib-font-size(14);
        .lib-css(font-weight, @font-weight__semibold);
        text-align: center;
        color: @secondary__color;
        text-transform: uppercase;
        white-space: nowrap;
        text-decoration: none;
        max-width: 155px;
        margin: 5px auto 0;
        padding: 3px 5px 2.5px;
        min-height: 30px;
        border: 1px solid @color-gray90;
        .lib-css(border-radius, 0, 1);
        .lib-css(justify-content, center, 1);
        .lib-css(box-shadow, 0px 4px 4px @color-black-25, 1);
        .lib-icon-font(
            @tt-icon-truck,
            @_icon-font-size: 18,
            @_icon-font-display: flex
        );
        .lib-vendor-prefix-display;
        .lib-vendor-box-align(center);

        &::before {
            margin-right: 4px;
            .lib-css(transform, scaleX(-1), 1);
        }

        span {
            padding-top: 2px;
        }
    }

    /**
    * Sticky bar for product features
    */
    .product-top-sticky-bar {
        margin: 0;
        list-style: none;
        padding: 4px 16px;
        width: 100%;
        box-sizing: border-box;
        position: fixed;
        left: 0;
        top: -60px;
        background: @color-gray97;
        z-index: 19;
        display: none;
        border: 1px solid @color-gray90;
        column-gap: 4px;
        overflow-x: auto;

        li {
            margin: 0;
            width: 100%;

            a {
                .lib-font-size(13);
                .lib-line-height(38);
                .lib-css(font-weight, @font-weight__bold);
                border: 1px solid @color-gray90;
                border-radius: 4px;
                padding: 0px 16px;
                text-align: center;
                text-decoration: unset !important;
                text-transform: uppercase;
                color: @color-blue;
                background: @color-white;
                white-space: nowrap;
                display: block;
            }

            &.active {
                a {
                    color: @color-white;  
                    background: @color-blue; 
                }
            }
        }
    }

    .page-header {
        &.affixed {
            &~.top-container {
                .product-top-sticky-bar {
                    .lib-vendor-prefix-display;
                    top: 70px;
                }
            }
        }
    }

    .product-item {
        .price-box {

            .you-save-statement,
            .savings-label {
                .lib-vendor-prefix-order(1);
                .lib-vendor-prefix-display();
                .lib-vendor-prefix-flex-wrap(wrap);
                .lib-css(justify-content, center, 1);
                .lib-css(border-radius, 3px, 1);
                .lib-css(background-color, @color-yellow2);
                .lib-heading-typography (
                    @_font-size: 18,
                    @_line-height: 18,
                    @_letter-spacing: -1px,
                );
                text-transform: uppercase;
                white-space: nowrap;
                padding: 5px 6px 4px;
                margin: 0 0 5px;
                position: relative;
                z-index: 1;

                .wrap,
                .savings-label-wrap {
                    .lib-vendor-prefix-display();
                    .lib-vendor-prefix-flex-wrap(nowrap);
                    .lib-vendor-box-align(flex-start);

                    .you-save-decimal,
                    .savings-label-currency,
                    .savings-label-decimal,
                    .currency-symbol,
                    .price-decimal {
                        .lib-font-size(12);
                        line-height: 1;
                        vertical-align: super;
                        position: relative;
                        top: 1px;
                    }

                    .currency-symbol,
                    .savings-label-currency {
                        margin-left: 3px;
                        top: 2px;
                    }

                    .you-save-decimal,
                    .price-decimal,
                    .savings-label-decimal {
                        margin-left: 0.5px;
                    }
                }
            }
        }
    }

    .product-reviews-summary {
        .lib-font-size(0);

        .rating-summary {
            position: relative;
            display: inline-block;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            left: 0;
            .lib-font-size(0);

            .label {
                display: none;
            }

            .rating-result {
                width: 87px;
                position: relative;
                margin: 0;

                &::before {
                    color: @color-yellow;
                    font-family: @font-tt-icons;
                    .lib-font-size(16);
                    height: 16px;
                    .lib-css(letter-spacing, 3px, 1);
                    .lib-line-height(16);
                    content: '\e809''\e809''\e809''\e809''\e809';
                }

                &>span {
                    display: block;
                    overflow: hidden;

                    &::before {
                        color: @color-yellow;
                        font-family: @font-tt-icons;
                        .lib-font-size(16);
                        height: 16px;
                        .lib-css(letter-spacing, 3px, 1);
                        .lib-line-height(16);
                        content: '\e808''\e808''\e808''\e808''\e808';
                    }
                }
            }
        }

        .reviews-actions {
            display: none;
            
            .action {
                border-radius: 0;
                .lib-font-size(15);
                margin-right: 5px;
                padding-right: 7px;

                &.view {
                    border-right: 1px solid @secondary__color;
                }
            }
        }
    }

    .toolbar-products,
    #instant-search-results,
    .cart-products-toolbar {
        .pages {
            .lib-font-size(0);
            display: block;

            .label {
                display: none;
            }

            .items {
                .lib-font-size(0);
                .lib-css(letter-spacing, -1px);
                line-height: 0;
                white-space: nowrap;
                margin: 0;
                padding: 0;
                list-style: none none;
                display: inline-block;
                .lib-css(font-weight, @font-weight__regular);

                .item {
                    margin: 0 5px 0 0;
                    float: left;
                    display: inline-block;

                    &.current {
                        a {
                            border-color: @color-gray80;
                            color: @color-gray20;
                            .lib-css(font-weight, @font-weight__regular);
                            cursor: default;

                            &.page {
                                border-color: @color-gray80;
                                color: @color-gray20;
                                .lib-css(font-weight, @font-weight__bold);
                            }
                        }
                    }

                    &:last-child {
                        margin: 0;
                    }

                    .page {
                        border: 2px solid @color-gray80;
                        .lib-font-size(14);
                        .lib-line-height(32);
                        color: @color-gray20;
                        display: inline-block;
                        .lib-css(font-weight, @font-weight__bold);
                        font-family: @font-family-name__base;
                        .lib-css(border-radius, 2px, 1);
                        padding: 3px 16.5px;
                    }

                    a {
                        &.page {
                            border: 2px solid @secondary__color;
                            color: @secondary__color;
                            display: inline-block;
                            padding: 3px 16.5px;
                            text-decoration: none;
                        }
                    }

                    .action {
                        border: 2px solid @secondary__color;
                        color: @secondary__color;
                        min-width: 42px;
                        .lib-css(border-radius, 2px, 1);
                        text-align: center;
                        position: relative;
                        .lib-css(font-weight, @font-weight__bold);
                        font-family: @font-family-name__base;
                        .lib-font-size(14);
                        .lib-line-height(32);
                        display: inline-block;
                        padding: 3px 16.5px;
                        text-decoration: none;

                        span {
                            display: none;
                        }

                        &.previous {
                            left: 0;
                            padding: 0;
                
                            .lib-icon-font(
                                @_icon-font-content: @tt-icon-angle-left,
                                @_icon-font-size: 26px,
                                @_icon-font-line-height: 38px
                            );

                            &::before {
                                padding: 0 1px;
                            }
                        }
                
                        &.next {
                            right: 0;
                            margin-right: 0;
                            padding: 0;
                
                            .lib-icon-font(
                                @_icon-font-content: @tt-icon-angle-right,
                                @_icon-font-size: 26px,
                                @_icon-font-line-height: 38px
                            );

                            &::before {
                                padding: 0 1px;
                            }
                        }
                    }
                }
            }
        }
    }

    .limiter {
        margin: 5px 0 0 5px;
        .lib-font-size(14);

        .label,
        .limiter-text {
            color: @color-gray20;
            font-family: @font-family-name__base;
            .lib-css(font-weight, @font-weight__regular);
            .lib-font-size(14);
        }

        .control {
            display: inline-block;
            width: auto;

            .limiter-options {
                width: auto;
                height: 32px;
                .lib-font-size(14);
                .lib-line-height(32);
                margin: 0 0 0 7px;
                font-family: @font-family-name__base;
                border: 1px solid @color-gray80;
                .lib-css(border-radius, 3px, 1);
                padding: 0 40px 0 15px;
                .lib-css(font-weight, @font-weight__regular);
                color: @color-gray20;
                appearance: none;
                background: url(../images/arrow-down.png) no-repeat right center;
                vertical-align: baseline;
            }
        }
    }

    // Product Detail Page Style
    .product-info-wrap {
        position: relative;

        .page-title-wrapper {
            .page-title {
                .lib-heading-typography (
                    @_color: @color-gray20,
                    @_letter-spacing: -1px,
                );
                .lib-css(text-transform, uppercase);
            }
        }

        .product-info-stock-sku {
            margin-bottom: 10px;
        }

        .product-reviews-summary {
            .lib-line-height(32);
            margin-bottom: 10px;

            &.empty {
                .reviews-actions {
                    padding-left: 0;
                }
            }

            .reviews-actions {
                display: inline-flex;
                .lib-line-height(16);
                padding: 2px 0 0 5px;

                a {
                    text-decoration: underline;
                    color: @color-light-blue2;
                    margin-right: 5px;
                    display: inline-block;
                    padding-right: 7px;
                    .lib-font-size(14);
                    .lib-line-height(14);

                    &.view {
                        display: inline-block;
                        border-right: 1px solid @color-light-blue2;
                        margin-right: 6px;
                        padding-right: 7px;
                        border-radius: 0;
                    }
                }
            }
        }
    }

    .product-info-main {
        .lib-vendor-prefix-display;
        .lib-vendor-prefix-flex-wrap;

        .product-column-left {
            width: 100%;

            .left-column-wrapper {
                width: 100%;

                .product-attribute-icons {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    list-style: none;
                    margin: 0;
                    padding: 0;

                    li {
                        a {
                            display: block;
                        }
                    }
                }
            }
        }

        .product-column-media {
            .product {
                &.media {
                    width: 100%;

                    .fotorama__video-close {
                        z-index: 15;
                    }
                }
            }

            .action-skip-wrapper {
                display: none;
            }
        }

        .product-column-addto {
            &>.product-info-price {
                position: relative;
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                .lib-css(align-items, center, 1);

                .product-info-price-additional {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap;
                    .lib-vendor-prefix-flex-direction(column);
                    .lib-css(align-items, flex-start, 1);
                    margin-left: auto;

                    &>div {
                        margin: 3px 0;
                    }

                    .low-price-guarantee {
                        img {
                            max-width: 122px;
                        }

                        .block-info {
                            display: none;
                        }
                    }

                    .product {
                        .attribute {
                            max-width: 122px;
                        }
                    }
                }

                .pricematch-wrapper {
                    width: 100%;
                    display: block;

                    .pricematch-modal {
                        border: 2px solid @primary__color;
                        padding: 2px 9px;
                        display: inline-block;
                        margin-bottom: 20px;
                        text-decoration: unset;
                        display: inline-flex;
                        align-items: center;
                        column-gap: 2px;

                        span {
                            text-transform: uppercase;
                            .lib-css(font-weight, @font-weight__bold);
                            .lib-font-size(15);
                            .lib-line-height(16);
                            color: @color-gray18;
                            .lib-css(letter-spacing, 0.5px);
                        }
                    }
                }
            }

            .product-payment-widgets {
                .payment-widget-ctn {
                    .section-title {
                        .lib-font-size(16);
                        .lib-line-height(24);
                        .lib-css(font-weight, @font-weight__bold);
                        padding: 5px 0 0;
                        border-bottom: 1px solid @color-gray20;
                        margin-bottom: 10px;
                        cursor: pointer;
                        position: relative;
                        width: 100%;
                        display: inline-block;
                        text-decoration: none;

                        p {
                            margin-bottom: 5px;

                            .click-to-see {
                                display: block;
                            }
                        }
                    }

                    .product-widget-contents {
                        .lib-vendor-prefix-display;
                        .lib-vendor-prefix-flex-wrap;
                        .lib-css(transition, all .3s ease-in-out, 1);
                        padding-bottom: 20px;
                        // opacity: 0;
                        transform: scale(1.06);

                        .payment {
                            position: relative;
                            cursor: pointer;
                            width: 25%;
                            padding: 5px 2px;
                            text-align: center;
                            .lib-css(transition, max-height 1s, 1);
                            animation: ease-in-out 1s;
                            max-height: 200px !important;

                            &.paypal-ctn {
                                img {
                                    width: 70px;
                                    margin: 10px auto 7px;
                                }
                            }

                            &.zip-ctn {
                                img {
                                    width: 75px;
                                    margin: 0 auto 2px;
                                }
                            }

                            &.afterpay-ctn {
                                img {
                                    width: 70px;
                                    margin: 0 auto 1px;
                                }
                            }

                            &.humm-ctn {
                                img {
                                    width: 70px;
                                    margin: 11px auto;
                                }
                            }
                        }

                        .payment-widget-iframe,
                        #zip-product-widget,
                        .zip-widget__wrapper,
                        .afterpay-container,
                        .humm-price-info-widget {
                            opacity: 0;
                            position: absolute;
                            top: 0;
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .payment-detail,
                    .payinfour-message {
                        .lib-font-size(13);
                        .lib-line-height(20);
                        display: block;
                    }
                }
            }
        }

        .product-details {
            .product-addto-links {
                display: none;
            }
        }

        .price-box {
            .lib-vendor-prefix-display;
            .lib-css(flex-direction, column, 1);

            &.price-configured_price {
                display: none;
            }

            .price-container {
                margin-bottom: 5px;
            }

            .price-as-configured {
                margin-top: 20px
            }

            .you-save-statement,
            .savings-label {
                .lib-vendor-prefix-order(1);
                .lib-vendor-prefix-display();
                .lib-vendor-prefix-flex-wrap(wrap);
                .lib-css(justify-content, center, 1);
                .lib-css(border-radius, 3px, 1);
                .lib-css(background-color, @color-yellow2);
                .lib-css(padding, @indent__xs @indent__s);
                .lib-css(margin-bottom, @indent__s);
                .lib-heading-typography (
                    @_font-size: 18,
                    @_line-height: 18,
                );
                text-transform: uppercase;
                white-space: nowrap;
                position: relative;
                z-index: 1;

                .wrap,
                .savings-label-wrap {
                    .lib-vendor-prefix-display();
                    .lib-vendor-prefix-flex-wrap(nowrap);
                    .lib-vendor-box-align(flex-start);

                    .you-save-decimal,
                    .savings-label-currency,
                    .savings-label-decimal,
                    .currency-symbol,
                    .price-decimal {
                        .lib-font-size(12);
                        line-height: 1;
                        vertical-align: super;
                        position: relative;
                        top: 1px;
                    }

                    .currency-symbol,
                    .savings-label-currency {
                        margin-left: 3px;
                        top: 2px;
                    }

                    .you-save-decimal,
                    .price-decimal,
                    .savings-label-decimal {
                        margin-left: 0.5px;
                    }
                }
            }

            .special-price {
                order: 2;

                .price-label {
                    display: none;
                }
            }

            .old-price {
                order: 3;
                margin: 5px 0 5px 4px;

                .price-wrapper {
                    text-decoration: line-through;
                }

                .price-label {
                    color: @color-gray11;
                    .lib-font-size(15) !important;
                    .lib-line-height(15) !important;
                    font-weight: 500;
                    display: inline-block;
                }

                .price-wrapper .price,
                .currency-symbol,
                .price-decimal {
                    color: @color-gray11;
                    .lib-font-size(15) !important;
                    .lib-line-height(15) !important;
                    .lib-css(font-family, @font-family__base);
                    .lib-css(font-weight, @font-weight__bold);
                    text-decoration: line-through;
                    letter-spacing: normal;
                    vertical-align: unset !important;

                    body.no-arial & {
                        .lib-css(font-family, @font-family__base);
                        .lib-css(font-weight, @font-weight__bold);
                    }
                }

                // .price-decimal {
                //     &::before {
                //         content: '.';
                //     }
                // }
            }

            .price-wrapper {
                .price {
                    .lib-heading-typography(
                        @_font-size: 52,
                        @_line-height: 52,
                        @_color: @primary__color,
                        @_letter-spacing: -1px,
                    );

                    .currency-symbol,
                    .price-decimal {
                        vertical-align: super;
                        position: relative;
                    }
                }
            }

            &.price-tier_price {
                .price-container {
                    margin-bottom: 0;
                    
                    .price-wrapper {
                        .price {
                            .lib-font-size(18);
                            .lib-line-height(18);

                            .currency-symbol,
                            .price-decimal {
                                .lib-font-size(12);
                                .lib-line-height(12);
                                top: 3px;
                            }
                        }
                    }
                }
            }
        }

        .product-addto-container {
            width: 100%;
            padding: 14px 18px 14px 20px;
            background: @color-gray97;
            border: 2px solid @color-gray90;
            .lib-css(border-radius, 5px, 1);

            .block {
                &.related {
                    .amlabel-position-wrapper {
                        display: none !important;
                    }

                    .block-title {
                        margin-bottom: 10px;

                        strong {
                            .lib-heading-typography (
                                @_font-size: 14,
                                @_line-height: 20,
                                @_color: @color-gray20,
                            );
                            .lib-css(text-transform, uppercase);
                        }

                        &.r1_t7_v2 {
                            &.free-delivery-title {
                                padding-bottom: 10px;
                                margin-bottom: 0;

                                strong {
                                    .lib-font-size(14);
                                    .lib-line-height(19);
                                    text-transform: initial;
                                    color: @secondary__color;

                                    span {
                                        color: @color-green6;
                                    }
                                }
                            }
                        }
                    }

                    .block-content {
                        .block-actions {
                            display: none;
                        }

                        .products-related {
                            margin: 0 0 6px;
                            display: block;
                            width: 100%;

                            .product-items {
                                list-style: none;
                                margin: 0;
                                padding: 0;

                                .product-item {
                                    width: 100%;
                                    height: 100%;
                                    padding: 0;
                                    margin-bottom: 5px;

                                    .product-item-info {
                                        border: 0 none;
                                        margin: 0;
                                        padding: 0;
                                        z-index: 1 !important;

                                        .product-item-detail {
                                            .lib-vendor-prefix-display;
                                            .lib-vendor-prefix-flex-wrap;
                                            .lib-vendor-box-align(flex-start);
                                            padding: 0;
                                            margin: 0;
                                            text-align: left;

                                            .product-item-photo {
                                                width: 70px;
                                                height: 70px;
                                                background-color: @color-white;
                                                border: 2px solid @color-gray90;
                                                margin: 0;
                                                padding: 5px 5px 5px 0;
                                                max-width: unset;
                                            }

                                            .product-item-name {
                                                text-align: left;
                                                margin: 0;
                                                padding: 0 10px;
                                                width: ~"calc(100% - 70px)";

                                                .product-item-link {
                                                    .lib-font-size(14);
                                                    .lib-line-height(17);
                                                    color: @secondary__color;
                                                    .lib-css(font-weight, @font-weight__bold);
                                                    display: block;

                                                    &:hover {
                                                        text-decoration: underline;
                                                    }
                                                }

                                                .product-item-price {
                                                    .lib-vendor-prefix-display;
                                                    .lib-vendor-prefix-flex-wrap(wrap);
                                                    .lib-vendor-box-align(flex-end);
                                                    .lib-css(margin-top, 10px);

                                                    .related-product-item-actions {
                                                        margin: 0 0 0 auto;
                                                    }
                                                }

                                                .price-box {
                                                    min-height: initial !important;
                                                    .lib-vendor-box-align(flex-start);

                                                    .price {
                                                        .lib-css(letter-spacing, -1px);
                                                        color: @primary__color;

                                                        .price-decimal,
                                                        .currency-symbol {
                                                            vertical-align: super;
                                                            position: relative;
                                                        }
                                                    }

                                                    .old-price {
                                                        display: none;
                                                    }
                                                }

                                                .you-save-statement {
                                                    max-width: 120px;
                                                }
                                            }

                                            .tocart {
                                                .lib-heading-typography ();
                                                .lib-css(border-radius, 5px, 1);
                                                padding: 2px 20px;
                                                margin-bottom: 5px;
                                                min-width: 92px;
                                                width: auto;

                                                &:after {
                                                    display: none;
                                                }
                                            }

                                            .field {
                                                &.choice {
                                                    margin-top: 2px;
                                                    margin-left: auto;
                                                    width: 24px;
                                                    height: 24px;
                                                    position: relative;

                                                    input {
                                                        position: absolute;
                                                        left: 0;
                                                        top: 0;
                                                        width: 100%;
                                                        height: 100%;
                                                        z-index: 1;
                                                        opacity: 0;
                                                        margin: 0;
                                                    }

                                                    label {
                                                        &::before {
                                                            content: '';
                                                            position: absolute;
                                                            left: 0;
                                                            top: 0;
                                                            width: 22px;
                                                            height: 22px;
                                                            background: @color-white;
                                                            border: 1px solid @color-gray-middle2;
                                                            .lib-css(border-radius, 3px, 1);
                                                        }

                                                        span {
                                                            display: none;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            #load-more-product-link {
                                margin-bottom: 10px;
                                color: @secondary__color;
                                .lib-css(font-weight, @font-weight__bold);
                                .lib-font-size(16);
                                .lib-line-height(20);
                                margin-left: auto;
                                width: 90px;
                                text-align: right;
                            }
                        }
                    }
                }
            }

            .product-add-form {
                .box-tocart {
                    &.update {
                        .quantity-widget {
                            flex-wrap: wrap;
                            max-width: 110px;

                            .handle.increase {
                                order: 3;
                            }

                            input[type=number]  {
                                order: 2;
                            }

                            .handle.decrease {
                                order: 1;
                            }

                            div.mage-error {
                                order: 4;
                            }
                        }
                    }
                }
            }
        }

        .reward-text {
            border: 1px solid @primary__color;
            margin-bottom: 14px;
            padding: 8px 10px 6px 58px;
            background: url(../images/assurance_insiders.gif) no-repeat 8px center @color-white;
            .lib-css(border-radius, 2px, 1);
            font-weight: 600;
            background-size: 42px;
            .lib-font-size(15);
            .lib-line-height(20);

            .earn-reward-point {
                display: block;
                color: @color-green3;
                .lib-line-height(16);
                .lib-css(margin-bottom, 4px);
            }

            .redeem-today {
                color: @color-gray20;

                a {
                    color: @secondary__color;
                    text-decoration: underline;
                }
            }
        }

        .product-column-right {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            width: 100%;

            .product {
                border-bottom: 1px solid @color-gray90;

                .block-image {
                    img {
                        max-width: 100%;
                    }
                }

                .block-info {
                    .low-price-info {
                        padding: 8px 0 0;
                        display: inline-block;
                        text-decoration: none;
                        color: @secondary__color;
                        .lib-font-size(14);
                        .lib-line-height(16);
                        .lib-icon-font(@_icon-font-content: @tt-icon-info,
                            @_icon-font-margin: 0 3px 0 1px);

                        &::before {
                            font-size: inherit;
                            line-height: inherit;
                            color: inherit;
                            content: '\ea0c';
                            font-family: @font-tt-icons;
                            margin: 0 3px 0 0;
                            vertical-align: top;
                            display: inline-block;
                            font-weight: 400;
                            overflow: hidden;
                            speak: none;
                            text-align: center;
                        }

                        span {
                            text-decoration: underline;
                        }
                    }
                }

                &.showmore {
                    margin: 0 !important;
                    padding: 20px 0 20px 20px !important;
                }

                &.overview {
                    h2 {
                        .lib-heading-typography(20, 24, @color-light-blue3);
                        text-transform: uppercase;
                        margin: 0 0 10px;
                    }

                    p {
                        margin-bottom: 10px;
                    }

                    .block-info {
                        position: absolute;
                        bottom: 0;
                        display: block;
                        width: 100%;
                        background: -moz-linear-gradient(top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .58) 36%, #fff 62%);
                        background: -webkit-linear-gradient(top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .58) 36%, #fff 62%);
                        background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .58) 36%, #fff 62%);
                        padding-top: 50px;
                        padding-bottom: 10px;

                        a {
                            padding: 8px 0 0;
                            display: inline-block;
                            text-decoration: none;

                            &::before {
                                font-size: inherit;
                                line-height: inherit;
                                color: inherit;
                                content: '\ea0c';
                                font-family: @font-tt-icons;
                                margin: 0 3px 0 0;
                                vertical-align: top;
                                display: inline-block;
                                font-weight: 400;
                                overflow: hidden;
                                speak: none;
                                text-align: center;
                            }
                        }
                    }
                }
            }

            .popup__delivery-returns,
            .popup__low-price-guarantee {
                display: none;
            }
        }

        .product-add-form {
            margin: 0 0 @indent__base;

            .product-options-wrapper {
                .configurable {
                    margin: 18px 25px;
                }

                .fieldset {
                    .field {
                        .label {
                            .lib-font-size(16);
                            .lib-line-height(16);
                            margin: 0 0 5px;
                            .lib-css(font-weight, @font-weight__regular);
                            display: inline-block;
                        }
                    }
                }
            }
        }

        #fulfilment-boxes-component {
            margin-top: 15px;
            min-height: 280px;

            .selected-suburb-title,
            .selected-store-title {
                .lib-heading-typography(
                    @_font-size: 17,
                    @_line-height: 24,
                    @_color: @secondary__color
                );
                .lib-vendor-prefix-display;
                align-items: baseline;
                text-decoration: none;
                margin: 0;
            }

            .fulfillment-box-content {
                border: 1px solid @color-gray90;
                .lib-css(border-radius, 4px, 1);
                padding: 15px 18px;

                &.estimate-delivery {
                    .fulfillment-box-header {
                        .lib-vendor-prefix-display;
                        .lib-vendor-prefix-flex-wrap(wrap);
                        .lib-css(align-items, center, 1);
                        .lib-css(justify-content, space-between, 1);
                        margin-bottom: 15px;

                        .express-delivery-block {
                            font-family: @font-family-name__base;
                            .lib-font-size(14);
                            .lib-line-height(20);
                            color: @color-gray40;
                            font-weight: @font-weight__bold;
                            margin: 0;
                            width: 100%;
                        }

                        .quick-view-title {
                            display: none;
                        }
                    }

                    .get-delivery-cost {
                        background: @color-blue;
                        border: 2px solid @color-blue8;
                        color: @color-white;
                        cursor: pointer;
                        display: inline-block;
                        font-weight: 700;
                        margin: 0;
                        padding: 9.25px 10px;
                        font-size: 14px;
                        line-height: 1.25;
                        border-radius: 3px;
                        width: 100%;
                        text-align: center;
                        margin-top: 10px;

                        &:hover {
                            background: @color-blue8;
                            border-color: @color-blue5;
                            text-decoration: unset;
                        }
                    }

                    .selected-suburb {
                        border: 0;
                        padding: 0;

                        .fulfillment-box-header {
                            .conv-test-applied {
                                display: block;
                            }
                        }

                        .delivery-messages {
                            margin-top: 12px;
                        }

                        .selected-suburb-wrap {
                            .delivery-messages {
                                .availability {

                                    .available-delivery,
                                    .express-message {
                                        position: relative;
                                        margin-top: 3px;
                                        display: none;
                                        padding-left: 25px;
                                        font-weight: 500;
                                        display: block;

                                        &::before {
                                            font-size: inherit;
                                            line-height: inherit;
                                            color: inherit;
                                            content: '\e80d';
                                            font-family: @font-tt-icons;
                                            margin: -2px 4px 0 0;
                                            vertical-align: top;
                                            display: inline-block;
                                            font-weight: 400;
                                            position: absolute;
                                            left: 0;
                                            top: 0;
                                        }
                                    }

                                    .fast-dispatch {
                                        display: none;

                                        &.standard-message {
                                            display: block;
                                        }
                                    }

                                    .product-message {
                                        display: none;
                                    }
                                }
                            }
                        }
                    }

                    .selected-suburb-wrap {
                        .delivery-messages {
                            .availability {

                                .available-delivery,
                                .express-message {
                                    display: block;
                                }
                            }
                        }
                    }

                    .selected-store {
                        border-bottom: 1px solid @color-gray90;
                        padding-bottom: 15px;
                        margin-bottom: 18px;

                        .click-collect-stock {
                            .lib-font-size(14);
                            .lib-line-height(20);
                            font-family: @font-family-name__base;
                            color: @color-gray40;
                            font-weight: @font-weight__bold;
                            margin: 0;
                            width: 100%;
                            margin-bottom: 15px;
                        }

                        .quick-view-title {
                            display: none;
                        }

                        .selected-store-location {
                            margin-top: 12px;
                        }
                    }

                    .selected-suburb-wrap {
                        position: relative;

                        .attribute {
                            position: absolute;
                            top: 0;
                            right: 0;

                            .conv-test-applied {
                                display: block;
                                width: 120px;
                                height: auto;
                            }
                        }

                        @media screen and (min-width: @screen__l) {
                            .attribute {
                                position: static;
                                margin-bottom: 5px;
                            }
                        }

                        @media screen and (min-width: @screen__ml) {
                            .attribute {
                                position: absolute;
                                margin-bottom: 0;
                            }
                        }

                        @media screen and (max-width: @screen__xs) {
                            .attribute {
                                position: static;
                                margin-bottom: 5px;
                            }
                        }
                    }
                }
            }

            .selected-suburb {
                padding-top: 5px;

                .delivery-messages {
                    .availability {
                        .fast-dispatch {
                            color: @color-gray20;
                            font-weight: 500;
                            margin: 7px 0 0 !important;
                            display: block;

                            &::before {
                                content: '';
                                display: block;
                                width: 20px;
                                height: 20px;
                                float: left;
                                margin: 0 6px 0 0;
                                background: url(../images/vector.svg) no-repeat;
                                background-position: center;
                            }

                            &.conv-test-applied {
                                display: block;
                            }
                        }

                        &.low-stock {
                            .stock-status {
                                color: @primary__color;
                            }
                        }
                    }
                }
            }

            .selected-store {
                border-bottom: 1px solid @color-gray90;
                padding-bottom: 15px;

                .availability {
                    &.low-stock {
                        .stock-status {
                            color: @primary__color;
                        }
                    }
                }
            }

            .store-form {
                max-width: 100%;
                position: relative;

                .store-zipcode {
                    position: relative;
                    overflow-x: hidden;
                    background: @color-white url(../images/loader-1.gif) no-repeat 200% 200%;
                    display: block;
                    text-decoration: none;
                    margin-bottom: 2px;

                    input {
                        border-color: @color-gray9;
                        .lib-css(border-radius, 3px, 1);
                        padding-left: 32px !important;
                        .lib-font-size(14);
                        .lib-line-height(32);
                        height: 44px;
                        padding: 5px 15px;
                        background: transparent;
                        border: 1px solid @color-gray80;
                        width: 100%;

                        &:focus {
                            box-shadow: unset;
                        }
                    }

                    &::after {
                        right: auto;
                        top: 12px;
                        left: 12px;
                        color: @color-gray20;
                        .lib-font-size(16);
                        line-height: inherit;
                        content: '\e90c';
                        font-family: @font-tt-icons;
                        vertical-align: middle;
                        display: inline-block;
                        .lib-css(font-weight, @font-weight__regular);
                        position: absolute;
                    }

                    .reset-feild {
                        position: absolute;
                        right: 5px;
                        top: 50%;
                        .lib-css(transform, translateY(-50%), 1);
                        color: @color-black;
                        .lib-css(opacity, 0.75, 1);
                        background: transparent;
                        border: 0;
                        padding: 0 5px;
                        .lib-font-size(24);
                        .lib-line-height(24);
                        text-align: center;

                        &:hover,
                        &:active {
                            .lib-css(opacity, 1, 1);
                        }
                    }
                }

                .store-options {
                    background-color: @color-white;
                    position: absolute;
                    z-index: 100;
                    top: 100%;
                    width: 100%;

                    .store-list {
                        list-style: none;
                        padding: 0;
                        margin: 0;
                        background-color: @color-white;
                        max-height: 250px;
                        overflow-y: auto;
                        border: 1px solid @color-gray80;
                        .lib-css(border-radius, 0 0 3px 3px, 1);

                        .item-suburb {
                            border: 1px solid @color-gray80;
                            border-width: 0 1px;
                            margin: 0;

                            a {
                                display: block;
                                text-align: left;
                                padding: 12px;
                                background-color: @color-white;
                                .lib-font-size(18);
                                color: @color-gray20;
                                cursor: pointer;
                                text-transform: capitalize;

                                &:hover {
                                    text-decoration: none;
                                    background-color: @color-gray96;
                                }
                            }
                        }

                        .item-store {
                            padding: 18px 20px;
                            border-bottom: 1px solid @color-gray90;
                            text-align: left;
                            color: @color-gray20;
                            .lib-font-size(16);
                            .lib-line-height(24);
                            margin: 0;

                            .store-name {
                                color: @color-gray20;
                                .lib-css(font-weight, @font-weight__bold);
                                .lib-font-size(20);
                                display: inline-block;
                                text-decoration: none;

                                &::before {
                                    font-size: inherit;
                                    line-height: inherit;
                                    color: @secondary__color;
                                    content: '\f276';
                                    font-family: @font-tt-icons;
                                    margin: -2px 5px 0 0;
                                    vertical-align: top;
                                    display: inline-block;
                                    .lib-css(font-weight, @font-weight__regular);
                                    text-align: center;
                                }
                            }

                            .store {
                                .delivery-type {
                                    .lib-css(font-weight, @font-weight__semibold);
                                    padding: 2px 0 5px;

                                    .delivery-message {
                                        &.available {
                                            color: @color-green3;
                                        }

                                        &.low-stock {
                                            color: @color-red2;
                                        }
                                    }

                                    .info-warning {
                                        font-size: 14px;
                                        line-height: 20px;
                                        font-weight: 600;
                                        color: @color-orange3;
            
                                        & ~ .info-tc {
                                            font-size: 10px;
                                            line-height: 20px;
                                            color: @color-blue;
                                        }
                                    }
                                }

                                .button {
                                    background: @secondary__color;
                                    border: 2px solid @color-blue5;
                                    color: @color-white;
                                    cursor: pointer;
                                    display: inline-block;
                                    .lib-css(font-weight, @font-weight__bold);
                                    padding: 9px 18px;
                                    .lib-font-size(17);
                                    .lib-line-height(18);
                                    margin: 6px 0 0;
                                    .lib-css(border-radius, 3px, 1);
                                }
                            }

                            .store-phone {
                                &::before {
                                    font-size: inherit;
                                    line-height: inherit;
                                    color: inherit;
                                    content: '\f095';
                                    font-family: @font-tt-icons;
                                    margin: -1px 2px 0 0;
                                    vertical-align: top;
                                    display: inline-block;
                                    .lib-css(font-weight, @font-weight__regular);
                                    text-align: center;
                                    .lib-css(transform, rotate(25deg), 1);
                                }
                            }
                        }
                    }
                }
            }

            .click-and-collect-messages,
            .delivery-messages {
                list-style: none;
                position: relative;
                margin: 0;
                padding: 0;

                .availability {
                    margin: 2px 0;

                    &.low-stock {
                        color: @color-orange3;
                        text-decoration: none;
                        display: block;

                        .info-warning {
                            color: @color-orange3;

                            & ~ .info-tc {
                                font-size: 10px;
                                line-height: 20px;
                                color: @color-blue;
                            }
                        }

                        .info-error {
                            color: @color-red2;

                            & ~ .info-tc {
                                font-size: 10px;
                                line-height: 20px;
                                color: @color-blue;
                            }
                        }
                    }

                    &.not-available {
                        color: @color-red2;

                        .info-warning {
                            color: @color-orange3;

                            & ~ .info-tc {
                                font-size: 10px;
                                line-height: 20px;
                                color: @color-blue;
                            }
                        }

                        .info-error {
                            color: @color-red2;

                            & ~ .info-tc {
                                font-size: 10px;
                                line-height: 20px;
                                color: @color-blue;
                            }
                        }
                    }

                    &.available {
                        color: @color-green3;
                        display: inline-block;
                        text-decoration: none;
                        display: block;
                    }

                    .stock-status {
                        display: block;
                        font-size: 14px;
                        line-height: 20px;
                        font-weight: 600;
                        padding-left: 30px;
                    }
                }
            }

            .selected-store-location {
                margin-bottom: 0;
                display: block;
                text-decoration: none;

                a {
                    cursor: pointer;
                    text-transform: capitalize;
                    .lib-css(font-weight, @font-weight__bold);
                    text-decoration: underline;
                    color: @secondary__color;
                    position: relative;

                    &::before {
                        line-height: inherit;
                        color: @secondary__color;
                        content: '\e904';
                        .lib-font-size(20);
                        line-height: 1;
                        font-family: @font-tt-icons;
                        margin: 0 10px 0 0;
                        display: inline-block;
                        .lib-css(font-weight, @font-weight__regular);
                    }
                }
            }

            .selected-store-address {
                display: block;
                margin-bottom: 10px;
                padding-left: 30px;
                .lib-font-size(14);

                a {
                    cursor: pointer;
                    color: @secondary__color;
                }
            }

            .delivery-methods-wrapper {
                .standard,
                .express, 
                .ondemand {
                    display: flex !important;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    margin-top: 12px;
                    position: relative;
                    padding-left: 30px;

                    & > span {
                        .lib-font-size(14);
                        .lib-line-height(20);
                        font-weight: 600;
                        color: @color-gray40;

                        &:first-child {
                            .lib-font-size(16);
                            .lib-line-height(20);
                            font-weight: 700;
                            color: @color-green3;
                        }

                        &.free {
                            .lib-font-size(16);
                            .lib-line-height(20);
                            font-weight: 700;
                            color: @color-green3;
                            text-transform: uppercase;
                        }
                    }

                    p {
                        .lib-font-size(14);
                        .lib-line-height(20);
                        font-weight: 600;
                        color: @color-gray40;
                        margin: 0;
                        width: 100%;

                        small {
                            .lib-font-size(10);
                            .lib-line-height(20);
                            color: @color-blue;
                        }

                        .info-warning {
                            .lib-font-size(14);
                            .lib-line-height(20);
                            font-weight: 600;
                            color: @color-orange3;
                        }

                        .info-success {
                            .lib-font-size(14);
                            .lib-line-height(20);
                            font-weight: 600;
                            color: @color-green3;
                        }

                        .info-error {
                            .lib-font-size(14);
                            .lib-line-height(20);
                            font-weight: 600;
                            color: @primary__color;
                        }
                    }

                    .free-shipping-note {
                        .lib-font-size(14);
                        .lib-line-height(20);
                        font-weight: 600;
                        color: @color-blue;
                    }
                }

                .standard {
                    &::before {
                        .lib-font-size(20);
                        .lib-line-height(20);
                        color: @secondary__color;
                        content: '\f0d1';
                        font-family: @font-tt-icons;
                        margin: -1px 0 0;
                        vertical-align: top;
                        display: inline-block;
                        .lib-css(font-weight, @font-weight__regular);
                        position: absolute;
                        left: 0;
                        top: 0;
                    }
                }

                .express {
                    .lib-icon-font(
                        @_icon-font-content: @tt-icon-delivery,
                        @_icon-font-size: 11px,
                        @_icon-font-color: @color-red2,
                        @_icon-font-display: block,
                        @_icon-font-margin: 0,
                        @_icon-font-vertical-align: middle,
                        @_icon-font-position: before
                    );

                    &::before {
                        .lib-line-height(20);
                        position: absolute;
                        left: -3px;
                        top: 0;
                    }

                    &::after {
						content: '';
						display: block;
						width: 8px;
						height: 8px;
						background: transparent url('../images/aus_post.svg') center center no-repeat;
						background-size: contain;
						position: absolute;
						top: -3px;
                        left: 18px;
					}
                }
                
                .ondemand {
                    &::before {
                        content: '';
						display: block;
						width: 20px;
						height: 20px;
						background: transparent url('../images/uber.png') center center no-repeat;
						background-size: contain;
                        position: absolute;
                        left: 0;
                        top: -1px;
                    }
                }
            }
        }

        .product-addto-links {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);
            .lib-css(align-items, center, 1);
            .lib-css(justify-content, space-around, 1);

            & > p {
                font-size: 0;
                margin: 0;
            }

            a {
                .lib-font-size(15);
                line-height: 1;
                color: @secondary__color;
                padding: 0;
                text-align: center;
                white-space: nowrap;
                display: flex;
                align-items: center;
                text-decoration: none;
                gap: 5px;

                img {
                    width: 20px;
                    height: 20px;
                    object-fit: contain;
                }
            }

            .wishlist {
                &.split {
                    &.button {
                        padding: 0;
                        margin: 0;

                        .action {
                            &.split {
                                border: 0;
                                .lib-font-size(15);
                                .lib-line-height(20);
                                font-weight: 400;
                                color: @secondary__color;
                                padding: 0;
                                text-align: center;
                                white-space: nowrap;
                                display: inline-flex;
                                text-decoration: none;
                                background: transparent;
                                background: transparent;
                                width: 100%;
                                justify-content: center;
                                gap: 5px;

                                &::before {
                                    content: '';
                                    background: url(../images/wishlistIcon.png) no-repeat center center;
                                    background-size: contain;
                                    width: 20px;
                                    height: 20px;
                                    display: inline-block;
                                }
                            
                            }

                            &.toggle {
                                display: none;
                            }
                        }
                    }
                }
            }

            .block-requisition-list {
                order: 5;
                text-align: left;
                text-align: center;
                display: inline-block;
                line-height: 1;
                padding-top: 12px;
                margin-top: 12px;
                width: 100%;
                border-top: 1px solid @color-gray90;

                .requisition-list-action {
                    .lib-css(border-radius, 3px, 1);
                    .lib-font-size(16);
                    .lib-line-height(24);
                    display: inline-block;
                    position: relative;
                    vertical-align: text-bottom;
                    width: auto;

                    .requisition-list-button {
                        color: @secondary__color;
                        text-transform: capitalize;
                        .lib-font-size(14);
                        padding: 4px;
                        border: 0;
                        background: transparent;
                        .lib-css(font-weight, @font-weight__regular);
                        display: flex;
                        align-items: center;
                        padding: 0;

                        img {
                            margin-right: 5px;
                        }

                        &::before {
                            content: "";
                            background: url(../images/addToStandardOrder.png) no-repeat center center;
                            background-size: contain;
                            width: 20px;
                            height: 20px;
                            display: inline-block;
                            margin-right: 5px;
                        }

                        &::after {
                            font-size: 14px;
                            line-height: 1.2;
                            color: @secondary__color;
                            content: '\e818';
                            font-family: @font-tt-icons;
                            margin: 0 0 0 10px;
                            vertical-align: top;
                        }

                        &.active {
                            &::after {
                                content: '\e819';
                            }
                        }
                    }

                    .items {
                        right: 0;

                        &::before {
                            left: auto;
                            right: 10px;
                        }

                        &::after {
                            left: auto;
                            right: 9px;
                        }
                    }
                }
            }
        }
    }

    .product {
        &.info {
            &.detailed {

                .product {
                    &.data {
                        &.items {
                            margin: 0;
                            padding: 0;

                            .item {
                                &.title {
                                    border-top: 1px solid @color-gray80;

                                    a {
                                        .lib-font-size(20);
                                        .lib-line-height(40);
                                        .lib-css(font-weight, @font-weight__bold);
                                        color: @secondary__color;
                                        padding: 8.5px 0;
                                        display: block;
                                        text-decoration: unset;
                                        position: relative;

                                        &::after {
                                            content: "";
                                            width: 0;
                                            height: 0;
                                            border-left: 4px solid transparent;
                                            border-right: 4px solid transparent;
                                            border-top: 7px solid @color-blue2;
                                            position: absolute;
                                            right: 12px;
                                            top: 50%;
                                            transform: translateY(-50%);
                                        }
                                    }

                                    &.active {
                                        a {
                                            &::after {
                                                transform: translateY(-50%) rotate(180deg);
                                            }
                                        }
                                    }
                                }
                            }

                            .item {
                                &.content {
                                    width: 100%;

                                    &>div {
                                        .lib-vendor-prefix-display;
                                        .lib-vendor-prefix-flex-wrap;
                                        .lib-css(align-items, flex-start, 1);
                                        padding: 10px 0 20px;
                                    }

                                    h2 {
                                        .lib-font-size(17);
                                        .lib-line-height(28);
                                        .lib-css(font-weight, @font-weight__bold);
                                        color: @color-gray20;
                                        margin: 20px 0;
                                        width: 100%;
                                        
                                        &:first-child {
                                            margin-top: 0;
                                        }

                                        &:last-child {
                                            margin-bottom: 0;
                                        }
                                    }

                                    h3 {
                                        .lib-font-size(18);
                                        .lib-line-height(30);
                                        .lib-css(font-weight, @font-weight__bold);
                                        color: @color-gray20;
                                        margin: 14px 0 12px;
                                        
                                        &:first-child {
                                            margin-top: 0;
                                        }

                                        &:last-child {
                                            margin-bottom: 0;
                                        }
                                    }

                                    p {
                                        .lib-font-size(16);
                                        .lib-line-height(20);
                                        color: @color-gray20;
                                        margin-bottom: 20px;

                                        &:last-child {
                                            margin-bottom: 0;
                                        }
                                    }

                                    ul {
                                        margin-bottom: 15px;

                                        li {
                                            .lib-font-size(16);
                                            .lib-line-height(20);
                                            color: @color-gray20;
                                            margin-bottom: 10px;

                                            &:last-child {
                                                margin-bottom: 0;
                                            }
                                        }

                                        &:last-child {
                                            margin-bottom: 0;
                                        }
                                    }

                                    .description {
                                        ul {
                                            padding-left: 15px;

                                            li {
                                                .lib-css(font-weight, @font-weight__regular);
                                                .lib-font-size(16);
                                                .lib-line-height(20);
                                                color: @color-gray20;
                                                margin-bottom: 10px;
                                            }
                                        }
                                    }

                                    .product-specification,
                                    .additional-attributes,
                                    .video-item {
                                        width: 100%;
                                    }
                                }
                            }
                        }
                    }
                }

                .product-specification {
                    .additional-attributes {

                        .table-caption {
                            display: none
                        }

                        tr {
                            th {
                                .lib-font-size(16);
                                .lib-line-height(20);
                                color: @color-gray20;
                                .lib-css(font-weight, @font-weight__regular);
                                padding: 8px 30px 5px 15px;
                                width: 32%;
                            }

                            td {
                                .lib-font-size(16);
                                .lib-line-height(20);
                                color: @color-gray20;
                                .lib-css(font-weight, @font-weight__regular);
                                padding: 8px 30px 5px 0;
                            }

                            &:nth-child(odd) {
                                background: @color-gray97;
                            }
                        }
                    }
                }
            }
        }
    }

    .catalog-product-view {
        .loading-mask {
            background-color: @color-white-50;
            z-index: 1000;

            .loader {

                >img {
                    z-index: 1001;
                }
            }
        }

        .box-tocart {
            .fieldset {
                width: 100%;
                .lib-vendor-prefix-display;
                .lib-css(flex-direction, row, 1);
                .lib-css(align-items, center, 1);
                .lib-css(justify-content, space-between, 1);

                .field {
                    &.qty {
                        margin: 0;

                        .label {
                            display: none;
                        }

                        input[type="number"] {
                            max-width: 60px;
                            max-height: 40px;
                            .lib-line-height(40);
                            text-align: center;
                        }
                    }
                }

                .actions {
                    .action {
                        &.tocart {
                            padding: 8px 12px;
                            .lib-font-size(22);
                            .lib-line-height(24);
                            color: @color-white;
                            text-shadow: 0 0 0;
                            display: inline-block;
                            text-decoration: none;
                            width: 100%;
                            cursor: pointer;
                            border: 2px solid @color-red;
                            .lib-css(border-radius, 3px, 1);
                            background-color: @primary__color;
                            .lib-css(font-weight, @font-weight__bold);
                            min-height: 49px;
                            .lib-icon-font(@_icon-font-content: @tt-icon-cart,
                                @_icon-font-margin: 0 3px 5px 0);

                            &:hover {
                                background-color: @color-red3;
                                border-color: @primary__color;
                            }
                        }
                    }
                }
            }
        }

        .gallery-placeholder {
            .loading-mask {
                display: none;
            }
        }

        .fotorama__stage {
            margin-bottom: 25px;
        }

        .fotorama__nav-wrap {
            width: 100%;

            .fotorama__thumb__arr--left {
                background: @color-white;
                width: 60px;
                left: 0;
                text-align: left;

                &.fotorama__arr--disabled {
                    opacity: 1;

                    .fotorama__thumb--icon {
                        opacity: .35;
                    }
                }

                .fotorama__thumb--icon {
                    transform: translateY(-50%);
                    position: relative;
                    width: auto;
                    padding: 0;
                    width: 42px;
                    height: 42px;
                    top: 50%;
                    left: 0;
                    background: unset;

                    &:hover {
                        &::before {
                            border-color: @secondary__color;
                            color: @secondary__color;
                        }
                    }

                    &::before {
                        content: '\e801';
                        width: 42px;
                        height: 42px;
                        border: 2px solid @color-gray80;
                        .lib-css(border-radius, 3px, 1);
                        position: absolute;
                        font-family: @font-tt-icons;
                        right: 0;
                        top: 50%;
                        .lib-font-size(36);
                        .lib-line-height(36);
                        transform: translateY(-50%);
                        box-sizing: border-box;
                        text-align: center;
                        color: @color-gray80;
                    }
                }
            }

            .fotorama__thumb__arr--right {
                background: @color-white;
                left: auto;
                width: 60px;
                right: 0;
                text-align: right;

                &.fotorama__arr--disabled {
                    opacity: 1;

                    .fotorama__thumb--icon {
                        opacity: .35;
                    }
                }

                .fotorama__thumb--icon {
                    transform: translateY(-50%);
                    position: relative;
                    width: auto;
                    padding: 0;
                    width: 42px;
                    height: 42px;
                    top: 50%;
                    left: auto;
                    right: 0;
                    margin-left: auto;
                    background: unset;

                    &:hover {
                        &::before {
                            border-color: @secondary__color;
                            color: @secondary__color;
                        }
                    }

                    &::before {
                        content: '\e802';
                        width: 42px;
                        height: 42px;
                        border: 2px solid @color-gray80;
                        .lib-css(border-radius, 3px, 1);
                        position: absolute;
                        font-family: @font-tt-icons;
                        right: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        box-sizing: border-box;
                        .lib-font-size(36);
                        .lib-line-height(36);
                        text-align: center;
                        color: @color-gray80;
                    }
                }
            }

            .fotorama__thumb {
                background-color: @color-gray92;
                height: 100%;
                overflow: hidden;
                position: relative;
                width: 100%;
            }
        }

        .fotorama__thumb-border {
            border: 1px solid @color-gray80  !important;
        }

        .fotorama__nav--dots {
            padding: 0;
            top: -20px;
        }

        .fotorama__dot {
            width: 8px;
            height: 8px;
            .lib-css(border-radius, 50%, 1);
        }
    }

    .loading-mask {
        bottom: 0;
        left: 0;
        margin: auto;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 100;
        background: @color-white-50;
        display: flex;
        align-items: center;
        justify-content: center;

        .loader {
            background: url(../images/loader-1.gif) no-repeat center center;
            background-size: contain;
            width: 64px;
            height: 64px; 

            > img {
                bottom: 0;
                left: 0;
                margin: auto;
                position: fixed;
                right: 0;
                top: 0;
                z-index: 101;
                display: none;
            }

            > p {
                display: none;
            }
        }
    }

    .product-addto-bar {
        background-color: @color-gray97;
        border: 2px solid @color-gray90;
        position: fixed;
        z-index: 19;
        left: 0;
        display: block;
        width: 100%;
        height: 90px;
        overflow: hidden;
        .lib-css(transition, top .2s ease, 1);

        &.affixed {
            top: 0;
            z-index: 110;
        }

        .product-addto-bar-container {
            max-width: 1272px;
            height: 100%;
            padding: 0 15px;
            margin: 0 auto;
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);
            .lib-css(align-items, center, 1);
            .lib-css(justify-content, space-between, 1);
        }
    }

    .product-info-stock-sku {
        margin-bottom: 10px;
        .lib-vendor-prefix-display;
        .lib-vendor-prefix-flex-wrap;
        .lib-css(align-items, center, 1);

        .attribute.sku {
            border-right: 1px solid @color-gray40;
            padding-right: 4px;
            margin-right: 4px;
        }

        .attribute,
        .type,
        .value {
            .lib-css(font-weight, @font-weight__regular);
            display: inline-block;
            .lib-font-size(12);
            .lib-line-height(12);
            font-family: @font-family-name__base;
            color: @color-gray40;
        }

        .value {
            &:nth-child(3) {
                display: none;
            }
        }

        .sku {
            padding-right: 4px;

            strong:after {
                content: '#';
            }
        }

        .stock {
            display: none;
        }
    }

    .product-reviews-wrapper {
        .review-heading {
            color: @color-gray20;
            .lib-css(font-weight, @font-weight__bold);
            margin: 0 0 20px;
            width: 100%;
        }

        #product-review-container {
            .review-summary-heading {
                .lib-font-size(17);
                text-transform: uppercase;
                .lib-css(font-weight, @font-weight__bold);
            }

            .review-summary-subheading {
                .lib-font-size(18);
                margin-bottom: 5px;
            }

            .review-list {
                &>.block-title {
                    display: none;
                }

                .block-content {
                    .review-items {
                        margin: 0;
                        padding: 0;
                        list-style: none;

                        .review-item {
                            padding: 25px 0;
                            border-bottom: 1px solid @color-gray94;

                            &:last-child {
                                border-bottom: 0;
                            }

                            .review-ratings {
                                .rating-summary {
                                    .rating-result {
                                        &:before {
                                            .lib-rating-icons-content(@_icon-content: '\e809');
                                            color: @color-yellow;
                                            .lib-css(letter-spacing, 3px);
                                        }

                                        span {
                                            &:before {
                                                .lib-rating-icons-content(@_icon-content: '\e808');
                                                color: @color-yellow;
                                                .lib-css(letter-spacing, 3px);
                                            }
                                        }
                                    }
                                }
                            }

                            .review-title {
                                .lib-css(font-weight, @font-weight__bold);
                                .lib-font-size(18);
                                .lib-line-height(30);
                                color: @color-gray20;
                                margin: 0
                            }

                            .review-content {
                                .lib-font-size(16);
                                color: @color-gray20;
                                margin-bottom: 10px;
                            }

                            .review-details {
                                .lib-font-size(16);
                                color: @color-gray20;

                                .review-author,
                                .review-date {
                                    display: inline-block;
                                    .lib-font-size(16);
                                }
                            }
                        }
                    }
                }
            }

            .review-toolbar {
                border: 0;
            }
        }

        .review-add {
            background: @color-gray97;
            border: 2px solid @color-gray90;

            .block-title {
                .lib-font-size(17);
                .lib-css(font-weight, @font-weight__bold);
                text-transform: uppercase;
                margin-bottom: 10px;
                display: block;
            }

            .block-content {

                .fieldset {
                    border: 0;
                    padding: 0;
                    margin: 0 0 10px;

                    .review-field-ratings {
                        border: 0;
                        padding: 0;
                        margin-top: 10px;
                        margin-bottom: 30px;

                        #product-review-table {
                            margin-bottom: 0;

                            .review-field-rating {
                                .label {
                                    .lib-css(font-weight, @font-weight__bold);
                                    .lib-font-size(16);
                                    .lib-line-height(24);
                                    display: block;
                                    width: 100%;
                                    padding: 6px 10px 10px 0;
                                    margin: 0 0 4px;

                                    &::before {
                                        content: 'Your Rating';
                                    }

                                    &::after {
                                        display: none;
                                    }

                                    span {
                                        display: none;
                                    }
                                }

                                .review-control-vote {
                                    display: block;
                                    padding: 0;
                                    margin: 0;
                                    width: 100%;
                                    height: 24px;
                                    .lib-rating-vote(@_icon-content: '\e808', @_icon-font-size: 24px);

                                    &:before {
                                        .lib-rating-icons-content(@_icon-content: '\e809');
                                        color: @color-yellow;
                                        .lib-css(letter-spacing, 3px);
                                    }

                                    label {
                                        &:before {
                                            color: @color-yellow;
                                            .lib-css(letter-spacing, 3px);
                                            border: 0;
                                            background: transparent;
                                        }

                                        &:after {
                                            display: none;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    &>.field {
                        margin: 0 0 10px;
                    }
                }

                .label {
                    display: block;
                    .lib-font-size(16);
                    line-height: 1.5;
                    margin-bottom: 4px;

                    &:after {
                        content: '*';
                        color: @color-red6;
                        margin-left: 2px;
                    }
                }

                input,
                textarea {
                    line-height: 1.5;
                }

                #review_field {
                    min-height: 106px;
                }

                .review-form-actions {
                    margin-left: 0;

                    .submit {
                        .lib-css(font-weight, @font-weight__bold);
                        .lib-font-size(16);
                        .lib-line-height(40);
                        background: @color-white;
                        border: 2px solid @color-blue5;
                        color: @secondary__color;
                        cursor: pointer;
                        padding: 2px 30px;
                        .lib-css(border-radius, 3px, 1);

                        &:hover {
                            background: @color-blue7;
                            border-color: @color-blue5;
                            color: @color-white;
                            text-decoration: none;
                        }
                    }
                }
            }
        }
    }

    .review-ratings {
        display: table;
        max-width: 100%;
        margin-bottom: 0;
        float: unset;
    }

    .rating-summary {
        display: table-row;

        .rating-label {
            display: none;
        }

        .rating-result {
            width: 87px;
            display: inline-block;
            position: relative;
            vertical-align: middle;
            margin-left: 0;

            &::before {
                color: @color-yellow;
                font-family: @font-tt-icons;
                .lib-font-size(16);
                height: 16px;
                .lib-css(letter-spacing, 3px, 1);
                .lib-line-height(16);
                content: '\e809''\e809''\e809''\e809''\e809';
            }

            &>span {
                &::before {
                    color: @color-yellow;
                    font-family: @font-tt-icons;
                    .lib-font-size(16);
                    height: 16px;
                    .lib-css(letter-spacing, 3px, 1);
                    .lib-line-height(16);
                    content: '\e808''\e808''\e808''\e808''\e808';
                }
            }
        }
    }

    .customer-review-rating {
        .rating-summary {
            .rating-label {
                display: inline;
            }
        }
    }

    .block-viewed-products-grid {
        .block-title {
            .lib-font-size(28);
            .lib-line-height(42);
            .lib-css(color, @color-gray20);
            .lib-css(font-weight, @font-weight__bold);
            margin: 0 0 20px;
            width: 100%;
        }

        .price-to {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-direction(column);

            .price-container {
                .lib-vendor-prefix-order(2);
            }

            .savings-label {
                .lib-vendor-prefix-order(1);
            }
        }
    }

    .review-toolbar {
        border-top: 1px solid @color-gray-middle4;
        margin: 0 0 20px;
        text-align: center;

        .pages {
            display: block;
            text-align: center;
            border-bottom: 1px solid @color-gray-middle4;
            padding: 20px 0;

            .label {
                display: none;
            }

            .items {
                .lib-font-size(0);
                .lib-css(letter-spacing, -1px, 1);
                line-height: 0;
                white-space: nowrap;
                margin: 0;
                padding: 0;
                list-style: none;
                display: inline-block;
                .lib-css(font-weight, @font-weight__regular);

                .item {
                    .lib-font-size(14);
                    .lib-line-height(32);
                    margin: 0 5px 0 0;
                    display: inline-block;
                    .lib-css(font-weight, @font-weight__bold);

                    strong {
                        border: 2px solid @color-gray80;
                        .lib-font-size(14);
                        .lib-line-height(32);
                        color: @color-gray20;
                        display: inline-block;
                        font-weight: 400;
                        padding: 3px 15px;
                        .lib-css(border-radius, 2px, 1);
                        min-width: 42px;

                        .label {
                            display: none;
                        }
                    }

                    a {
                        border: 2px solid @secondary__color;
                        color: @secondary__color;
                        display: inline-block;
                        padding: 3px 15px;
                        text-decoration: none;
                        .lib-css(border-radius, 2px, 1);
                        min-width: 42px;

                        .label {
                            display: none;
                        }

                        &.action {
                            span {
                                display: none;
                            }

                            &.previous {
                                &:before {
                                    .lib-font-size(28);
                                    .lib-line-height(32);
                                    color: @secondary__color;
                                    content: '\e801';
                                    font-family: @font-tt-icons;
                                    vertical-align: top;
                                    .lib-css(font-weight, @font-weight__regular);
                                }
                            }

                            &.next {
                                &:before {
                                    .lib-font-size(28);
                                    .lib-line-height(32);
                                    color: @secondary__color;
                                    content: '\e802';
                                    font-family: @font-tt-icons;
                                    vertical-align: top;
                                    .lib-css(font-weight, @font-weight__regular);
                                }
                            }
                        }
                    }
                }
            }
        }

        .limiter {
            display: none;
        }
    }

    .catalog-product-view {
        .product-info-main {
            margin-bottom: 30px;
        }

        .product-upsell-container {
            margin-bottom: 30px;
        }

        .homepage-block {
            padding-top: 0;

            &.block.related, 
            &.block.upsell,
            &.emarsys-recommended {
                margin-bottom: 30px;
            }

            .products-grid {
                .product-item {
                    margin-bottom: 0;
                }
            }
        }

        .product-info-bottom {
            margin-bottom: 30px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .product {
            &.info { 
                &.detailed {    
                    border-bottom: 1px solid @color-gray80;
                    margin-bottom: 30px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }

        .product-reviews-wrapper {
            margin-bottom: 30px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    // Product-giftcard page
    .page-product-giftcard {
        .giftcard-amount {
            margin: 10px 0 20px;
            text-align: left;

            .field {
                &.open-amount {
                    padding: 0;
                }
            }
        }

        .fieldset {
            &>.field {
                &>.label {
                    .lib-font-size(16);
                    font-weight: 400;
                    line-height: inherit;
                    margin: 0 0 5px;
                    display: inline-block;
                }

                .giftcard-message-max-length {
                    height: 82px;
                }
            }

            &.giftcard {
                margin: 0 0 25px;

                .legend,
                &>br {
                    display: none;
                }
            }
        }

        #tab-label-deliveryreturn,
        #deliveryreturn,
        .product-addto-links {
            display: none;
        }

        .product-info-main {

            .product-payment-widgets,
            .zip-container {
                display: none;
            }
        }
    }

    #payinfour-category-widget {
        .lib-css(margin-top, @indent__s);
        .lib-css(justify-content, center, 1);

        span {
            max-width: 100%;
        }
    }

    .sidebar-main {
        .block {
            &.filter {
                .action-close {
                    display: none;
                }
            }
        }
    }

    // Instant Search page
    .instant-search-page {
        .page-main {
            min-height: ~"calc(100vh - 229px)";
        }
        .columns {
            .column {
                &.main {
                    width: 100% !important;
                    padding-left: 0 !important;

                    #instant-search {
                        .products-grid, 
                        .products-list {
                            .products.items {
                                &>div {
                                    display: block;
                                    width: 100%;
                                    &>div {
                                        &>div{
                                            .lib-vendor-prefix-display;
                                            .lib-vendor-prefix-flex-wrap;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Buy page 
    .buy-keyword-index {
        .columns {
            .column {
                &.main {
                    #instant-search {
                        .products-grid {
                            .products.items {
                                &>div {
                                    display: block;
                                    width: 100%;
                                    &>div {
                                        &>div{
                                            .lib-vendor-prefix-display;
                                            .lib-vendor-prefix-flex-wrap;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @media (max-width:400px) {
        .shipping-label {
            .lib-css(letter-spacing, -0.6px);
        }

        .instant-search-page {
            .page-main {
                min-height: ~"calc(100vh - 322.13px)";
            }
    
            .page-footer {
                min-height: 1521.5px;
            }
        }
    }

    @media (max-width:375px) {
        .columns {
            .column {
                &.main {
                    #amasty-shopby-product-list,
                    #instant-search {
                        .products-grid {
                            .products {
                                &.items {
                                    .product-item {
                                        width: 100%;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .block.related,
        .block.upsell {
            .products-related,
            .products-upsell {
                .product-item {
                    width: 100%;
                }
            }
        }
    }

    @media (max-width:374px) {
        .shipping-label {
            .lib-css(letter-spacing, 0px);
        }

        .page-header {
            &.affixed {
                &~.top-container {
                    .product-top-sticky-bar {
                        top: 58px !important;
                    }
                }
            }
        }
    }


    @media (max-width:359px) {
        .catalog-product-view {
            .box-tocart {
                .fieldset {
                    .field {
                        &.qty {
                            max-width: 70px;
                        }
                    }
                }
            }
        }
    }


    // Compare Products page CSS
    .catalog-product_compare-index {
        .page-title-wrapper {
            .page-title {
                .lib-css(font-weight, @font-weight__bold);
            }
        }

        .action {
            &.print {
                border: 2px solid @color-blue2;
                .lib-css(border-radius, 4px, 1);
                position: relative;
                top: -55px;
                display: inline-block;
                float: right;
                margin: 0 0 20px;
                padding: 10px 14px;
                text-decoration: none !important;
                text-transform: uppercase;
                color: @secondary__color;

                span {
                    display: inline-block;
                    .lib-font-size(17);
                    .lib-css(font-weight, @font-weight__bold);
                }

                &:hover {
                    border: 2px solid @color-blue2;
                    background: @color-blue2;
                    color: @color-white;
                }
            }
        }
    }

    .table-wrapper.comparison {
        max-width: 100%;
        text-align: start;
        overflow-x: auto;
        width: 100%;
        overflow-y: hidden;
    }

    .table-comparison {
        table-layout: fixed;

        .table-caption,
        thead {
            display: none;
        }

        .cell.label.remove,
        .cell.label.product {
            span {
                &:extend(.abs-visually-hidden all);
            }
        }

        tbody {
            tr {
                &:first-child {
                    th {
                        &:first-child {
                            span {
                                display: none;
                            }
                        }
                    }
                }
            }
        }

        tfoot {
            &>tr {
                th,
                td {
                    border-top: 1px solid @color-gray80;
                    padding-top: 20px;
                }
            }
        }

        ul {
            padding-left: 8px;
        }

        .cell {
            width: 160px;
            padding: 15px;
            .lib-css(box-sizing, content-box, 1);

            th {
                font-weight: 700;
            }

            .attibute.value {
                width: 100%;
                overflow: hidden;

                h2 {
                    margin: 20px 0;
                    .lib-font-size(17);
                    .lib-line-height(28);
                    .lib-css(font-weight, @font-weight__bold);
                }
            }

            &.product.info,
            &.product.label {
                border-bottom: 1px solid #d1d1d1;
            }
            &.label {
                .attribute.label {
                    display: block;
                    width: 100%;
                    word-wrap: break-word;
                }
            }
            &.attribute {
                .lib-font-size(13);
                img {
                    max-width: 100%;
                    height: auto;
                }
            }
        }

        .product-item-photo {
            display: block;
            margin: 0 auto 15px;

            .product-image-container {
                width: 100% !important;
                max-width: 300px;
                min-width: 140px;
                display: block;
                margin: 0 auto;
            }

            .product-image-wrapper {
                display: block;
                position: relative;
                z-index: 1;
                overflow: hidden;
                padding: 0 !important;
            }

            .product-image-photo {
                margin-left: 0;
                width: 100%;
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0 !important;
                right: 0 !important;
                margin: auto 4%;
                max-width: 92%;
                width: 100%;
                aspect-ratio: 1/1;
                object-fit: contain;
                padding: 0 1px;
                box-sizing: border-box;
                transition: all .3s linear;
            }
        }

        .product-reviews-summary {
            margin-left: auto;
            margin-right: auto;
            top: 0;
        }

        .product-item-actions,
        .price-box,
        .product.rating,
        .product-item-name {
            margin: 15px 0;
        }

        .product-addto-links {
            .action.split,
            .action.toggle {
                .lib-button-s();
            }

            .action.toggle {
                padding: 0;
            }
        }

        .product-item-actions {
            > .actions-primary {
                + .actions-secondary {
                    margin-top: @indent__s;
                }
            }
        }

        .product-item-name {
            position: relative;

            a {
                &:extend(.products-grid .product-item .product-item-info .product-item-details .product-item-name all);
            }
        }

        .product-reviews-summary {
            &:extend(.products-grid .product-item .product-item-info .product-reviews-summary all);
        }

        .price-box {
            &:extend(.products-grid .product-item .product-item-info .price-box all);
        }

        .you-save-statement {
            .lib-vendor-prefix-order(2);
            &:extend(.product-item .price-box .you-save-statement all);
        }

        .cell.product.info {
            text-align: center;
        }

        .product-item-actions {
            display: inline-block;
            width: 100%;
            text-align: center;
            margin-top: 0;

            .actions-primary {
                display: block;
                margin: auto;
            }

            .view-detail {
                background-color: @color-blue;
                border-radius: 3px;
                border: 2px solid @color-blue5;
                display: inline-block;
                vertical-align: middle;

                &:hover {
                    background-color: @color-blue3;
                    border-color: @color-blue5;
                    transition: all .3s ease-in-out;
                }

                span {
                    color: @color-white;
                    display: inline-block;
                    padding: 0 10px;
                    .lib-font-size(14);
                    .lib-line-height(30);
                    .lib-css(font-weight, @font-weight__bold);
                    text-transform: uppercase;
                    vertical-align: middle;
                }
            }
        }

        .secondary-addto-links.actions-secondary {
            display: none;
        }

        .cell.remove .action.delete > span,
        .block-compare .action.delete > span {
            position: relative;
            width: auto;
            margin: 0;
            height: auto;
        }

        .cell.attribute {
            font-size: 16px;
        }

        .cell.remove {
            text-align: left;
            padding-bottom: 15px;

            .action.delete {
                .lib-button-cta();
            }
        }

        .block-compare {
            .action.delete {
                & > span {
                    position: relative;
                    width: auto;
                    margin: 0;
                    padding: 0px 10px;
                    display: inline-block;
                    .lib-font-size(15);
                    .lib-line-height(26);
                    .lib-css(font-weight, @font-weight__bold);
                    color: @color-white;
                    vertical-align: middle;
                }
            }
        }
    }

    // Set Amasty label z-index
    .amlabel-position-wrapper {
        z-index: 8;
    }

    .catalog-category-view {
        .slick-slider {
            .slick-arrow {
                z-index: 2;
            }
        }
    }

    .full-wrapper-product {
        .category-view-description {
            order:3;
            width:100%;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .breadcrumbs {
        display: block;
        padding: 15px 18px;
        margin: 0 !important;
    }

    .page-main {
        padding-left: 15px;
        padding-right: 15px;
    }

    .columns {
        .column {
            &.main {
                .category-view {
                    .page-title-wrapper {
                        padding-top: 0 !important;

                        .page-title {
                            border-bottom: 1px solid @color-gray80;
                        }
                    }
                }

                .subcat {
                    &.wrapper {
                        .subcategories {
                            .subcategory {
                                width: 50%;
                            }
                        }
                    }
                }

                .view-all-products-container {
                    text-align: center;
                }

                #amasty-shopby-product-list,
                #instant-search {
                    .toolbar-products {
                        .toolbar-amount {
                            display: none !important;
                        }

                        .modes {
                            display: none;
                        }

                        .limiter {
                            width: 100%;
                            margin-left: 0;
                        }

                        .toolbar-sorter {
                            margin: 5px auto 0 !important;
                            float: none !important;
                        }
                    }

                    .products {
                        &~.toolbar-products {
                            .lib-css(justify-content, flex-start, 1);
                        }
                    }

                    .products-grid {
                        .products {
                            &.items {
                                .product-item {
                                    width: 33.33%;
                                    margin-left: 0;
                                }
                            }
                        }
                    }
                }

                #instant-search {
                    .toolbar-products  {
                        .toolbar-amount {
                            display: block !important;
                            width: 100%;
                            float: none !important;
                            margin: 0 !important;
                            padding: 0 !important;
                        }

                        .toolbar-sorter-wrap {
                            width: 100%;

                            .toolbar-sorter {
                                .sorter-options {
                                    width: auto;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .page-with-filter {
        .sidebar-main,
        .full-left-side {
            position: fixed;
            z-index: 1010;
            top: 0;
            right: -105%;
            background-color: @color-white;
            width: 100%;
            height: 100%;
            padding: 20px;
            overflow-x: hidden;
            overflow-y: auto;
            .lib-css(transition, all .2s ease, 1);

            &.active {
                right: 0;
            }
        }
    }

    .full-left-side,
    .sidebar-main {
        .block {
            &.filter {
                margin-bottom: 40px;

                .filter-title {
                    margin-bottom: 10px;
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap;
                    .lib-css(align-items, center, 1);

                    .filters-icon {
                        width: 25px;
                        height: 25px;
                        margin-bottom: 5px;
                        color: inherit;
                        fill: inherit;
                    }

                    strong {
                        .lib-font-size(14);
                        color: @color-gray20;
                        text-transform: uppercase;
                        font-family: @font-family-name__base;
                        .lib-css(font-weight, @font-weight__bold);
                    }
                }

                .filter-content {
                    .filter-subtitle {
                        display: none;
                    }

                    .amshopby-filter-current {
                        .filter-current-subtitle {
                            display: none;
                        }

                        .amshopby-items {
                            padding: 0 0 0 10px;
                            margin: 0;

                            .amshopby-item {
                                .lib-vendor-prefix-display;
                                .lib-vendor-prefix-flex-wrap(wrap);
                                .lib-css(align-items, center, 1);
                                padding: 0 0 0 20px;
                                position: relative;
                                margin: 10px 0;

                                .amshopby-remove {
                                    .lib-vendor-prefix-display;
                                    .lib-css(align-items, center, 1);
                                    .lib-css(justify-content, center, 1);
                                    height: 12px;
                                    left: 0;
                                    position: absolute;
                                    width: 12px;

                                    &:hover {

                                        &::before,
                                        &::after {
                                            background-color: @color-gray-darken3;
                                        }

                                        &~.amshopby-filter-value {
                                            opacity: .5;
                                        }
                                    }

                                    &::before {
                                        background-color: @color-gray-middle3;
                                        content: '';
                                        height: 1px;
                                        position: absolute;
                                        width: 100%;
                                        transform: rotate(-45deg);
                                    }

                                    &::after {
                                        background-color: @color-gray-middle3;
                                        content: '';
                                        height: 1px;
                                        position: absolute;
                                        width: 100%;
                                        transform: rotate(45deg);
                                    }
                                }

                                .amshopby-filter-name {
                                    display: none;
                                }

                                .amshopby-filter-value {
                                    color: @color-gray20;
                                    font-family: @font-family-name__base;
                                    .lib-css(font-weight, @font-weight__regular);
                                    .lib-font-size(16);
                                    .lib-line-height(20);
                                }
                            }
                        }

                        .amshopby-button-wrap {
                            display: none;
                        }
                    }

                    .filter-actions {
                        margin: 0;
                        padding-bottom: 10px;

                        .filters-icon {
                            content: '\E85A';
                            width: 25px;
                            height: 25px;
                            margin-bottom: 5px;
                            color: #2e2d76;
                            fill: #2e2d76;
                        }

                        .filter-clear {
                            .lib-vendor-prefix-display;
                            .lib-css(align-items, center, 1);
                            color: @color-light-blue5;
                            .lib-font-size(14);
                            .lib-line-height(19);

                            &:hover {
                                color: @color-light-blue6;
                                text-decoration: none;
                            }
                        }
                    }
                }

                .action-close {
                    border: 0 none;
                    outline: 0 none;
                    position: absolute;
                    top: 1.6rem;
                    right: 2.2rem;
                    display: inline-block;
                    text-decoration: none;
                    color: @color-gray-middle2;
                    display: block !important;

                    &:hover {
                        color: @color-gray35;
                    }
                }
            }
        }
    }

    .filter-options,
    .instant-search-sidebar {

        .filter-options-title {
            color: @color-gray20;
            font-family: @font-family-name__base;
            .lib-css(font-weight, @font-weight__bold);
            .lib-font-size(16);
            .lib-line-height(24);
            margin: 0;
            padding: 16px 0 12px;
            word-break: break-word;
            width: 100%;
            border-top: 1px solid @color-gray80;
            cursor: pointer;
            position: relative;
            display: inline-block;
            text-decoration: none;

            &:after {
                position: absolute;
                top: 10px;
                right: 10px;
                .lib-font-size(25);
                .lib-css(font-weight, @font-weight__bold);
                color: @secondary__color;
                content: '+';
            }

            &[aria-selected=true] {
                &:after {
                    content: '-';
                }
            }
        }

        .filter-options-content {
            margin: 0 0 15px;

            .items {
                padding: 0;
                margin: 0;
                list-style: none;

                .item {
                    display: block;
                    position: relative;
                    line-height: 24px;
                    margin-bottom: 10px;
                    font-family: @font-family-name__base;
                    margin-left: 0;
                    padding: 0;

                    &>a {
                        display: block;
                        padding: 0 !important;

                        &.amshopby-link-selected {
                            .label {
                                color: @color-blue;

                                &.checkbox-btn {
                                    &::after {
                                        .lib-css(opacity, 1, 1);
                                        .lib-css(transform, scale(1), 1);
                                    }
                                }

                                &.radio-btn {
                                    &::after {
                                        .lib-css(opacity, 1, 1);
                                        .lib-css(transform, rotate(45deg) scale(1), 1);
                                    }
                                }
                            }

                            .count {
                                color: @color-blue;
                            }
                        }


                        .label {
                            position: relative;
                            padding-left: 34px;
                            cursor: pointer;
                            display: inline-block !important;
                            .lib-font-size(16);
                            .lib-line-height(24);
                            color: @color-gray20;
                            .lib-css(font-weight, @font-weight__regular);

                            &.checkbox-btn {

                                &::before {
                                    content: '';
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    width: 22px;
                                    height: 22px;
                                    background: @color-white;
                                    border: 1px solid @color-gray-middle2;
                                    .lib-css(border-radius, 3px, 1);
                                }

                                &:after {
                                    content: '\f00c';
                                    width: auto;
                                    height: auto;
                                    line-height: 1;
                                    font-family: @font-tt-icons;
                                    background: transparent;
                                    color: @secondary__color;
                                    position: absolute;
                                    top: 4px;
                                    left: 4px;
                                    .lib-css(border-radius, 100%, 1);
                                    .lib-css(opacity, 0, 1);
                                    .lib-css(transform, scale(0), 1);
                                    .lib-css(transition, all .2s ease-in-out, 1);
                                }
                            }


                            &.radio-btn {
                                margin-top: -2px;
                                padding-top: 2px;

                                &::before {
                                    content: '';
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    width: 22px;
                                    height: 22px;
                                    background: @color-white;
                                    border: 1px solid @color-gray-middle2;
                                    .lib-css(border-radius, 50%, 1);
                                }

                                &:after {
                                    content: '';
                                    width: 10px;
                                    height: 10px;
                                    top: 7px;
                                    left: 7px;
                                    border: 0;
                                    background: @color-blue;
                                    .lib-css(opacity, 0, 1);
                                    .lib-css(transform, rotate(45deg) scale(0), 1);
                                    position: absolute;
                                }
                            }
                        }

                        &:hover {
                            .count {
                                color: @color-blue;
                            }
                        }
                    }
                }

                input,
                .amshopby-choice {
                    display: none;
                }

                .amshopby-slider-container {
                    margin: 10px 0 22px;

                    .am-slider {

                        &.ui-slider-horizontal {
                            background: @color-gray-light4;
                            height: 10px;
                            margin: 0 8px;
                            .lib-css(border-radius, 0, 1);
                            border: none;
                            width: 95%;
                            position: relative;

                            .ui-slider-range {
                                background: @color-gray-light3;
                                height: 10px;
                            }

                            .ui-slider-handle {
                                height: 20px;
                                width: 10px;
                                border: 1px solid @color-gray80;
                                top: -4px;
                                .lib-css(border-radius, 0, 1);
                                margin-left: -4px;
                                transform: translate(-8px, -3px);
                                position: absolute;
                                z-index: 2;
                            }
                        }
                    }

                    .amshopby-slider-display {
                        margin: 10px 0 0;
                        text-align: center;
                    }
                }
            }

            .count {
                .lib-font-size(16);
                .lib-line-height(24);
                font-family: @font-family-name__base;
                .lib-css(font-weight, @font-weight__regular);
                color: @color-gray20;

                &::before {
                    content: '(';
                }

                &::after {
                    content: ')';
                }

                .filter-count-label {
                    display: none;
                }
            }

            .am-filter-items-price {
                .amshopby_currency_rate {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(justify-content, center, 1);

                    .amshopby-input-wrapper {
                        max-width: 50%;
                        position: relative;
                        width: ~"calc(50% - 7px)";

                        .amshopby-currency {
                            background: @color-white;
                            bottom: ~"calc(69%)";
                            color: @color-gray-darken3;
                            .lib-font-size(14);
                            .lib-line-height(16);
                            left: 6px;
                            .lib-css(letter-spacing, 0.3px);
                            padding: 2px;
                            position: absolute;
                        }

                        .am-filter-price {
                            background-clip: padding-box;
                            border: 1px solid #dfdedd;
                            color: @color-gray-darken3;
                            .lib-font-size(14);
                            height: 40px;
                            .lib-css(letter-spacing, 0.3px);
                            text-align: center;
                            width: 100%;
                        }
                    }

                    .delimiter {
                        line-height: 40px;
                        margin: 0;
                        width: 14px;
                        text-align: center;
                    }

                    p {
                        .lib-font-size(16);
                        .lib-line-height(20);
                        margin: 15px 0 20px;
                        color: @color-gray20;
                        font-family: @font-family-name__base;
                        text-align: center;
                    }
                }
            }
        }
    }

    .instant-search-wrap {
        .instant-search-sidebar {
            .clear-filters {
                justify-content: flex-start;
            }
        }
    }

    .toolbar-products {
        .pages {
            margin-left: 20px;
        }

        #shopby-mobile {
            .lib-css(border-radius, 3px, 1);
            .lib-font-size(14);
            text-decoration: none;
            line-height: 2.3;
            padding: 2px 20px;
            text-transform: uppercase;
            border: 2px solid @color-blue5;
            background: @color-white;
            color: @secondary__color;
            .lib-css(font-weight, @font-weight__bold);
            cursor: pointer;
            .lib-vendor-prefix-display;
            .lib-css(align-items, center, 1);
            .lib-css(justify-content, center, 1);
            width: 100%;

            &:hover {
                background: @color-light-blue7;
                color: @color-white;
            }
        }
    }


    .catalog-category-view {
        &.page-layout-1column {
            .limiter {
                margin: 0;
            }

            .toolbar-products {
                #shopby-mobile {
                    display: none;
                }
            }

            .column {
                &.main {
                    #amasty-shopby-product-list {
                        .toolbar-products {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            row-gap: 8px;
                            column-gap: 15px;
                        }
                    }
                }
            }
        }
    }

    // Product Detail Page Style
    .product-info-wrap {
        .product-info-top {
            margin-bottom: 10px;
        }

        .page-title-wrapper {
            .page-title {
                .lib-font-size(22);
                .lib-line-height(28);
                margin-bottom: 3px;
            }
        }

        .price-box {

            .you-save-statement,
            .savings-label {
                margin-top: 15px;
            }
        }
    }

    .product-info-main {
        .product-column-left {
            order: 2;

            .left-column-wrapper {
                .product-attribute-icons {
                    .lib-css(align-items, center, 1);
                    margin: 0 -5px;

                    li {
                        width: 20%;
                        min-width: 30px;
                        max-width: 80px;
                        margin: 0;
                        padding: 0 5px 5px;
                    }


                }
            }
        }

        .product-column-media {
            width: 100%;
            order: 1;

            .product {
                &.media {
                    min-height: 650px;
                }
            }
        }

        .product-column-addto {
            width: 100%;
            order: 3;

            &>.product-info-price {}
        }

        .price-box {
            .price-wrapper {
                .price {
                    .lib-font-size(52);
                    .lib-line-height(52);

                    .currency-symbol,
                    .price-decimal {
                        .lib-font-size(20);
                        .lib-line-height(20);
                        top: 0;
                    }
                }
            }
        }

        .product-addto-container {
            .block {
                &.related {
                    .block-content {
                        .products-related {
                            .product-items {
                                .product-item {
                                    .product-item-info {
                                        .product-item-detail {
                                            .product-item-name {
                                                .price-box {
                                                    .lib-css(margin-top, @indent__s);

                                                    .price {
                                                        .lib-font-size(30);
                                                        .lib-line-height(30);
                                                        .lib-css(letter-spacing, 0) !important;

                                                        .price-decimal,
                                                        .currency-symbol {
                                                            .lib-font-size(16);
                                                            .lib-line-height(16);
                                                            top: 3px;
                                                            .lib-css(letter-spacing, 0) !important;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .product-addto-links {
            padding: 18px 0;

        }

        .product-column-right {
            order: 4;

            .product {
                width: 50%;
                text-align: center;
                padding: 10px 20px;
                border-top: 1px solid @color-gray90;
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap(wrap);
                .lib-css(justify-content, center, 1);
                .lib-css(align-items, center, 1);
                .lib-css(flex-direction, column, 1);

                &.low-price-guarantee,
                &.professional_tool_retailer,
                &.returns {
                    border-left: 1px solid @color-gray90;
                }

                .block-image {
                    img {
                        margin: 0 auto;
                        max-width: 120px;

                        &.desktop-only {
                            display: none;
                        }

                        &.mobile-only {
                            display: inline-block;
                        }
                    }
                }

                &.overview {
                    width: 100%;
                    text-align: left;

                    .block-info {
                        display: none !important;
                    }
                }
            }
        }

        .product-add-form {
            .product-options-wrapper {
                .fieldset {
                    .field {
                        .label {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }

    .product-addto-bar {
        bottom: 0;

        .product-addto-bar-container {
            .title-col {
                display: none;
            }

            .addto-cart-col {
                width: 100% !important;
            }
        }
    }

    .catalog-product-view {
        .box-tocart {
            .fieldset {

                .field {
                    &.qty {
                        width: 40%;
                    }
                }

                .actions {
                    width: 60%;
                    padding-left: 15px;

                    .action {
                        &.tocart {
                            padding: 8px 5px !important;
                        }
                    }
                }
            }
        }
    }

    .product-reviews-wrapper {
        .review-heading {
            .lib-font-size(28);
            .lib-line-height(42);
        }

        #product-review-container {
            .review-summary-heading {
                margin-bottom: 5px;
            }
        }

        .write-review {
            margin-top: 10px;
            margin-bottom: 30px;
            display: block;
            width: 100%;

            a {
                background: @color-white;
                border: 2px solid @secondary__color;
                cursor: pointer;
                .lib-css(font-weight, @font-weight__bold);
                padding: 6px 25px;
                .lib-css(transition, all .4s ease-out, 1);
                .lib-font-size(17);
                .lib-line-height(26);
                color: @secondary__color;
                display: block;
                width: 100%;
                text-align: center;
                .lib-css(border-radius, 3px, 1);

                &:hover {
                    background: @secondary__color;
                    border: 2px solid @color-blue6;
                    color: @color-white;
                }
            }
        }

        .review-add {
            width: 100%;
            margin: 25px 0;
            padding: 38px 30px 30px;
            .lib-css(border-radius, 5px, 1);
            display: none;
        }
    }

    // Product-giftcard page
    .page-product-giftcard {
        .product-options-bottom {
            padding-bottom: 20px;
        }
    }

    // Compare Products page CSS
    .catalog-product_compare-index {
        .page-title-wrapper {
            .page-title {
                border-bottom: 1px solid @color-gray80;
            }
		}

        .action {
            &.print {
                top: -10px !important;
            }
        }
    }

    table {
        &#product-comparison {
            tr {
                display: block;
            }

            .cell {
                min-width: 160px;
                width: 160px;
                max-width: 160px;
            }
        }
    }
    
    .instant-search-page {
        .page-main {
            min-height: ~"calc(100vh - 322.13px)";
        }

        .page-footer {
            min-height: 1376.5px;
        }
    }

    .products-list {
        .product-items {
            .product-item {
                .product-item-info {
                    .product-item-photo {
                        max-width: 80px;
                    }

                    .product-item-details {
                        width: ~"calc(100% - 80px)";
                        padding-left: 15px;
                    }

                    .product-item-inner {
                        position: relative;
                        width: 100%;
                        max-width: 240px;
                        top: auto;
                        right: auto;
                        margin-top: 10px;
                    }

                    .product-item-actions {
                        .shipping-label {
                            margin-left: 0
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .columns {
        .column {
            &.main {
                #amasty-shopby-product-list,
                #instant-search {
                    .toolbar-products {
                        .lib-vendor-prefix-display;
                        .lib-vendor-prefix-flex-wrap(wrap);
                        .lib-css(align-items, center, 1);
                        .lib-css(justify-content, space-between, 1);

                        .modes {
                            display: none !important;
                        }
                    }

                    .products {

                        &~.toolbar-products {
                            .lib-css(justify-content, center, 1);
                            row-gap: 8px;
                            column-gap: 15px;
                        }
                    }

                    .products-grid {
                        .products {
                            &.items {
                                .product-item {
                                    width: 50%;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .limiter {
        // display: none;
    }

    .toolbar-products {
        .pages {
            margin-left: 0;
        }
    }

    .products-grid {
        .product-item {
            .product-item-info {
                .product-item-actions {
                    margin-top: 0 !important;
                }
            }
        }
    }

    .product-info-main {
        #fulfilment-boxes-component {
            .fulfillment-box-content {
                padding: 15px 18px;

                &.estimate-delivery {
                    .selected-suburb-wrap {
                        .attribute {
                            top: 0 !important;
                            right: 0 !important;
                        }
                    }
                }
            }
        }

        .product-column-media {
            .product {
                &.media {
                    min-height: 491px;
                }
            }
        }
    }

    .product-reviews-wrapper {
        .review-heading {
            .lib-font-size(17);
            .lib-line-height(28);
            margin-top: 20px;
        }
    }

    .product-reviews-wrapper {
        .review-heading {
            .lib-font-size(17);
            .lib-line-height(28);
            margin-top: 20px;
        }

        #product-review-container {
            .review-summary-heading {
                margin-bottom: 5px;
            }
        }
    }

    .catalog-category-view {
        &.page-layout-1column {
            .column {
                &.main {
                    #amasty-shopby-product-list {
                        .toolbar-products {
                            .toolbar-sorter {
                                &.sorter {
                                    .sorter-options {
                                        width: 160px !important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    .columns {
        .column {
            &.main {
                #amasty-shopby-product-list,
                #instant-search {
                    .toolbar-products {
                        .toolbar-sorter {
                            .sorter-options {
                                width: 200px !important;
                            }
                        }

                        #shopby-mobile {
                            padding: 2px 15px;
                        }
                    }
                }
            }
        }
    }

    .catalog-product-view {
        .box-tocart {
            .fieldset {
                .actions {
                    width: 65%;
                    padding-left: 5px;

                    .action {
                        &.tocart {
                            padding: 8px 5px !important;
                        }
                    }
                }
            }
        }

    }

    .catalog-category-view {
        &.page-layout-1column {
            .column {
                &.main {
                    #amasty-shopby-product-list {
                        .toolbar-products {
                            .toolbar-sorter {
                                .sorter-label {
                                    white-space: nowrap;
                                }
                                
                                &.sorter {
                                    display: flex;
                                    align-items: center;
                                    width: 100%;
                                    float: unset !important;

                                    .sorter-options {
                                        width: 100% !important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .page-header {
        &.affixed {
            &~.top-container {
                .product-top-sticky-bar {
                    top: 62px !important;
                }
            }
        }
    }
    
    .product-top-sticky-bar {
        li {
            width: auto !important;
        }
    }

    .product-info-main {
        .product-add-form {
            .product-options-wrapper {
                .fieldset {
                    .field {
                        .label {
                            margin-top: 17px;
                            float: left;
                            padding-right: 5px;
                        }
                    }
                }
            }
        }
    
        .product-column-media {
            .product {
                &.media {
                    min-height: 330px;
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .breadcrumbs {
        margin: 5px auto 10px;
        max-width: 1282px;
        padding-left: 20px;
        padding-right: 20px;
    }

    .page-main {
        margin: 0 auto;
        max-width: 1282px;
        padding-left: 20px;
        padding-right: 20px;
    }

    .page-layout-2columns-left {
        .columns {
            .column {
                &.main {
                    width: 80%;
                    order: 2;
                    padding-left: 1.6%;
                }
            }
        }

        &.page-layout-1column {
            .columns {
                .column {
                    &.main {
                        width: 100%;
                        padding-left: 0;
                    }
                }
            }
        }
    }

    .columns {
        .full-left-side,
        .sidebar-main {
            width: 20%;
            margin-top: 20px;
            order: 1;
            padding-right: 1.6%;

            .block {
                &.filter {
                    margin-bottom: 40px;

                    .filter-title {
                        margin-bottom: 10px;
                        .lib-vendor-prefix-display;
                        .lib-vendor-prefix-flex-wrap;
                        .lib-css(align-items, center, 1);

                        .filters-icon {
                            width: 25px;
                            height: 25px;
                            margin-bottom: 5px;
                            color: inherit;
                            fill: inherit;
                        }

                        strong {
                            .lib-font-size(14);
                            color: @color-gray20;
                            text-transform: uppercase;
                            font-family: @font-family-name__base;
                            .lib-css(font-weight, @font-weight__bold);
                        }
                    }

                    .filter-content {
                        .filter-subtitle {
                            display: none;
                        }

                        .amshopby-filter-current {
                            .filter-current-subtitle {
                                display: none;
                            }

                            .amshopby-items {
                                padding: 0 0 0 10px;
                                margin: 0;

                                .amshopby-item {
                                    .lib-vendor-prefix-display;
                                    .lib-vendor-prefix-flex-wrap(wrap);
                                    .lib-css(align-items, center, 1);
                                    padding: 0 0 0 20px;
                                    position: relative;
                                    margin: 10px 0;

                                    .amshopby-remove {
                                        .lib-vendor-prefix-display;
                                        .lib-css(align-items, center, 1);
                                        .lib-css(justify-content, center, 1);
                                        height: 12px;
                                        left: 0;
                                        position: absolute;
                                        width: 12px;

                                        &:hover {

                                            &::before,
                                            &::after {
                                                background-color: @color-gray-darken3;
                                            }

                                            &~.amshopby-filter-value {
                                                opacity: .5;
                                            }
                                        }

                                        &::before {
                                            background-color: @color-gray-middle3;
                                            content: '';
                                            height: 1px;
                                            position: absolute;
                                            width: 100%;
                                            transform: rotate(-45deg);
                                        }

                                        &::after {
                                            background-color: @color-gray-middle3;
                                            content: '';
                                            height: 1px;
                                            position: absolute;
                                            width: 100%;
                                            transform: rotate(45deg);
                                        }
                                    }

                                    .amshopby-filter-name {
                                        display: none;
                                    }

                                    .amshopby-filter-value {
                                        color: @color-gray20;
                                        font-family: @font-family-name__base;
                                        .lib-css(font-weight, @font-weight__regular);
                                        .lib-font-size(16);
                                    }
                                }
                            }

                            .amshopby-button-wrap {
                                display: none;
                            }
                        }

                        .filter-actions {
                            margin: 0;
                            padding-bottom: 10px;

                            .filters-icon {
                                content: '\E85A';
                                width: 25px;
                                height: 25px;
                                margin-bottom: 5px;
                                color: #2e2d76;
                                fill: #2e2d76;
                            }

                            .filter-clear {
                                .lib-vendor-prefix-display;
                                .lib-css(align-items, center, 1);
                                color: @color-light-blue5;
                                .lib-font-size(14);
                                .lib-line-height(19);

                                &:hover {
                                    color: @color-light-blue6;
                                    text-decoration: none;
                                }
                            }
                        }
                    }
                }
            }
        }

        .column {
            &.main {
                min-height: 300px;


                .subcat {
                    &.wrapper {
                        .subcategories {
                            .subcategory {
                                width: 25%;
                            }
                        }
                    }
                }

                .view-all-products-container {
                    text-align: right;
                }

                #amasty-shopby-product-list,
                #instant-search {
                    .toolbar-products {

                        .toolbar-sorter {
                            margin-top: 3px;
                            float: right;

                            .sorter-label {
                                .lib-font-size(14);
                                color: @color-gray20;
                                font-family: @font-family-name__base;
                            }

                            .sorter-options {
                                margin: 0 0 0 7px;
                                width: auto;
                                height: 32px;
                                .lib-font-size(14);
                                .lib-line-height(30);
                                font-family: @font-family-name__base;
                                padding: 0 40px 0 15px;
                                .lib-css(border-radius, 3px, 1);

                            }

                            .sorter-action {
                                vertical-align: top;
                                display: inline-block;
                                text-decoration: none;

                                span {
                                    display: none;
                                }
                            }
                        }

                        .limiter {
                            float: right;
                            margin-top: 3px;
                        }

                        .toolbar-sorter-wrap {
                            .lib-vendor-prefix-display;
                            .lib-vendor-prefix-flex-wrap(wrap);
                            .lib-css(align-items, center, 1);
                            .lib-css(justify-content, flex-start, 1); 
                            .lib-css(flex-direction, row-reverse, 1);
                            
                            .limiter,
                            .toolbar-sorter {
                                float: none;
                            }
                        }
                    }

                    .products {
                        &~.toolbar-products {
                            .lib-css(justify-content, flex-start, 1);
                        }
                    }

                    .products-grid {
                        .products {
                            &.items {
                                .product-item {
                                    width: 33.33%;
                                    margin-left: 0;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .filter-options,
    .instant-search-sidebar {
        margin: 0;

        .filter-options-title {
            color: @color-gray20;
            font-family: @font-family-name__base;
            .lib-css(font-weight, @font-weight__bold);
            .lib-font-size(16);
            .lib-line-height(24);
            margin: 0;
            padding: 16px 0 12px;
            word-break: break-word;
            width: 100%;
            border-top: 1px solid @color-gray80;
            cursor: pointer;
            position: relative;
            display: inline-block;
            text-decoration: none;

            &:after {
                position: absolute;
                top: 10px;
                right: 10px;
                .lib-font-size(25);
                .lib-css(font-weight, @font-weight__bold);
                color: @secondary__color;
                content: '+';
            }

            &[aria-selected=true] {
                &:after {
                    content: '-';
                }
            }
        }

        .filter-options-content {
            margin: 0 0 15px;

            .items {
                padding: 0;
                margin: 0;
                list-style: none;

                .item {
                    display: block;
                    position: relative;
                    line-height: 24px;
                    margin-bottom: 10px;
                    font-family: @font-family-name__base;
                    margin-left: 0;
                    padding: 0;

                    > a {
                        display: block;
                        padding: 0 !important;
                        .lib-css(color, @color-gray20);

                        &.amshopby-link-selected {
                            .label {
                                color: @link__color;

                                &.checkbox-btn {
                                    &::after {
                                        top: 1px;
                                        .lib-css(opacity, 1, 1);
                                        .lib-css(transform, scale(1), 1);
                                    }
                                }

                                &.radio-btn {
                                    &::after {
                                        .lib-css(opacity, 1, 1);
                                        .lib-css(transform, rotate(45deg) scale(1), 1);
                                    }
                                }
                            }

                            .count {
                                color: @link__color;
                            }
                        }

                        .label {
                            margin-top: -2px;
                            padding-top: 2px;
                            line-height: 22px;
                            .lib-css(font-weight, @font-weight__regular);
                        }

                        &:hover {
                            .count {
                                color: @link__color;
                            }
                        }
                    }
                }

                input,
                .amshopby-choice {
                    display: none;
                }

                .amshopby-slider-container {
                    margin: 10px 0 22px;

                    .am-slider {

                        &.ui-slider-horizontal {
                            background: @color-gray-light4;
                            height: 10px;
                            margin: 0 8px;
                            .lib-css(border-radius, 0, 1);
                            border: none;
                            width: 95%;
                            position: relative;

                            .ui-slider-range {
                                background: @color-gray-light3;
                                height: 10px;
                            }

                            .ui-slider-handle {
                                height: 20px;
                                width: 10px;
                                border: 1px solid @color-gray80;
                                top: -4px;
                                .lib-css(border-radius, 0, 1);
                                margin-left: -4px;
                                transform: translate(-8px, -3px);
                                position: absolute;
                                z-index: 2;
                            }
                        }
                    }

                    .amshopby-slider-display {
                        margin: 10px 0 0;
                        text-align: center;
                    }
                }
            }

            .count {
                .lib-font-size(16);
                .lib-line-height(24);
                font-family: @font-family-name__base;
                .lib-css(font-weight, @font-weight__regular);
                color: @color-gray20;

                &::before {
                    content: '(';
                }

                &::after {
                    content: ')';
                }

                .filter-count-label {
                    display: none;
                }
            }

            .am-filter-items-price {
                .amshopby_currency_rate {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(justify-content, center, 1);

                    .amshopby-input-wrapper {
                        max-width: 50%;
                        position: relative;
                        width: ~"calc(50% - 7px)";

                        .amshopby-currency {
                            background: @color-white;
                            bottom: ~"calc(69%)";
                            color: @color-gray-darken3;
                            .lib-font-size(14);
                            .lib-line-height(16);
                            left: 6px;
                            .lib-css(letter-spacing, 0.3px);
                            padding: 2px;
                            position: absolute;
                        }

                        .am-filter-price {
                            background-clip: padding-box;
                            border: 1px solid #dfdedd;
                            color: @color-gray-darken3;
                            .lib-font-size(14);
                            height: 40px;
                            .lib-css(letter-spacing, 0.3px);
                            text-align: center;
                            width: 100%;
                        }
                    }

                    .delimiter {
                        line-height: 40px;
                        margin: 0;
                        width: 14px;
                        text-align: center;
                    }

                    p {
                        .lib-font-size(16);
                        .lib-line-height(20);
                        margin: 15px 0 20px;
                        color: @color-gray20;
                        font-family: @font-family-name__base;
                        text-align: center;
                    }
                }
            }
        }
    }

    .toolbar-products {
        .pages {
            margin-left: 20px;
        }


        #shopby-mobile {
            display: none;
        }
    }

    // Product Detail Page Style
    .product-info-wrap {
        .product-info-top {
            margin-bottom: 5px;
        }

        .page-title-wrapper {
            .page-title {
                .lib-font-size(28);
                .lib-line-height(30);
                margin-bottom: 6px;
            }
        }
    }

    .product-info-main {
        margin-bottom: 32px;

        .product-column-media {
            width: 50%;
            padding: 0 15px;

            .product {
                &.media {
                    min-height: 382px;
                }
            }

            .product-column-left {
                margin-bottom: 0;
                position: relative;
                z-index: 10;

                .left-column-wrapper {
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-vendor-prefix-flex-direction(row);
                    .lib-css(align-items, flex-end, 1);
                    width: 100%;
                    margin-top: 20px;

                    .product-attribute-icons {
                        .lib-vendor-prefix-display;
                        .lib-vendor-prefix-flex-wrap(wrap);
                        .lib-vendor-prefix-flex-direction(row);
                        width: ~"calc(100% - 60px)";

                        li {
                            width: 25%;
                            margin: 0;
                            padding-left: 6px;

                            a {
                                display: block;
                            }
                        }
                    }
                }
            }
        }

        .product-column-addto {
            width: 50%;
            margin-top: -20px;
            padding-right: 0;

            &>.product-info-price {
                .low-price-guarantee {
                    display: none;
                }
            }
        }

        .price-box {
            .price-wrapper {
                .price {
                    .lib-font-size(62);
                    .lib-line-height(62);

                    .currency-symbol,
                    .price-decimal {
                        .lib-font-size(28);
                        .lib-line-height(28);
                    }
                }
            }
        }


        .product-addto-container {
            .block {
                &.related {
                    .block-content {
                        .products-related {
                            .product-items {
                                .product-item {
                                    .product-item-info {
                                        .product-item-detail {
                                            .product-item-name {
                                                .price-box {
                                                    .price {
                                                        .lib-heading-typography (
                                                            @_font-size: 30,
                                                            @_line-height: 36,
                                                            @_letter-spacing: -1px,
                                                        );

                                                        .price-decimal,
                                                        .currency-symbol {
                                                            .lib-font-size(18);
                                                            .lib-line-height(28);
                                                            top: 2px;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .product-add-form {
            .product-options-wrapper {
                .fieldset {
                    .field {
                        margin-bottom: 15px;

                        .control {
                            display: inline-block;

                            select {
                                width: auto;
                            }
                        }
                    }
                }
            }

            .box-tocart {
                .fieldset {
                    .actions {
                        width: ~"calc(100% - 130px)";

                        .action {
                            &.tocart {
                                padding: 8px 12px;
                            }
                        }
                    }
                }
            }
        }

        .product-column-right {
            margin-top: 10px;
            border-top: 1px solid @color-gray90;
            border-bottom: 1px solid @color-gray90;
            flex-wrap: nowrap !important;

            .product {
                padding: 20px;
                width: 25%;
                border-bottom: 0;
                border-left: 1px solid @color-gray90;

                &:first-child {
                    border-left: 0;
                }

                .block-image {
                    img {
                        &.desktop-only {
                            display: none;
                        }

                        &.mobile-only {
                            display: inline-block;
                        }
                    }
                }

                &.showmore {
                    position: relative;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 12;
                    -webkit-box-orient: vertical;
                }
            }
        }

        .product-addto-links {
            padding-top: 15px;
        }

        #fulfilment-boxes-component {
            .fulfillment-box-content {
                padding: 15px 25px;
            }
        }
    }

    .product {
        &.info {
            &.detailed {

                .product {
                    &.data {
                        &.items {
                            .item {
                                &.content {
                                    .description {
                                        padding-right: 20px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .product-addto-bar {
        top: -100px;
        bottom: auto;

        .product-addto-bar-container {
            .title-col {
                display: block;
                width: 65%;

                .page-title {
                    .lib-font-size(28);
                    .lib-line-height(30);
                    margin-bottom: 6px;
                    .lib-css(letter-spacing, -1px);
                    color: @color-gray20;
                    .lib-css(font-weight, @font-weight__bold);
                }

                .product-info-stock-sku {
                    margin-top: 4px;
                }
            }

            .addto-cart-col {
                width: 35%;

                .box-tocart {
                    .action {
                        &.tocart {
                            min-width: 214px;
                        }
                    }
                }
            }
        }
    }

    .product-reviews-wrapper {
        .lib-vendor-prefix-display;
        .lib-vendor-prefix-flex-wrap;
        .lib-css(align-items, flex-start, 1);
        .lib-css(justify-content, space-between, 1);
        margin-bottom: 40px;

        .review-heading {
            .lib-font-size(28);
            .lib-line-height(42);
        }

        #product-review-container {
            width: ~"calc(66.66667% - 20px)";

            .review-summary-heading {
                .lib-font-size(17);
                text-transform: uppercase;
                .lib-css(font-weight, @font-weight__bold);
            }

            .review-summary-subheading {
                .lib-font-size(16);
                margin-bottom: 5px;
            }

            .review-list {
                margin-bottom: 30px;
            }

            .review-ratings {
                max-width: 100%;
                width: 100%;
            }
        }

        .write-review {
            display: none;
        }

        .review-add {
            width: 33.33333333%;
            .lib-css(border-radius, 2px, 1);
            padding: 35px 30px 50px;
        }
    }

    .product-top-sticky-bar {
        display: none !important;
    }

    // Product-giftcard page
    .page-product-giftcard {
        .product-options-bottom {
            padding-bottom: 5px;
        }
    }

    // Instant Search page
    .instant-search-wrap {
        .lib-vendor-prefix-display;
        .lib-vendor-prefix-flex-wrap;
        width: 100%;

        .instant-search-sidebar {
            width: 20%;
            padding-right: 1.6%;
            order: 1;

            .filter-by-text {
                margin-bottom: 10px;
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                .lib-css(align-items, center, 1);

                .filters-icon {
                    width: 25px;
                    height: 25px;
                    margin-bottom: 5px;
                    color: inherit;
                    fill: inherit;
                }

                strong {
                    .lib-font-size(14);
                    color: @color-gray20;
                    text-transform: uppercase;
                    font-family: @font-family-name__base;
                    .lib-css(font-weight, @font-weight__bold);
                }
            }

            .instantsearch-facets-toggle {
                display: none;
            }

            .filters-list {
                padding-left: 10px;
            }

            .filter-list-item {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                margin: 0;
            }

            .remove-filter-button {
                cursor: pointer;
                margin-bottom: 2px;

                &::before {
                    content: "\e906";
                    font-family: @font-tt-icons;
                    font-size: 12px;
                    margin-right: 10px;
                    color: @color-gray-middle3;
                }
            }

            .clear-filters {
                display: flex;
                align-items: center;
                padding: 0;
                margin-bottom: 10px;
                width: max-content;
                cursor: pointer;
                color: @secondary__color;
                fill: @secondary__color;

                .filters-icon {
                    width: 25px;
                    height: 25px;
                    margin-bottom: 5px;
                    color: inherit;
                    fill: inherit;
                }

                span {
                    font-size: 14px;
                    text-transform: capitalize;
                }
            }
        }

        .instant-search-results {
            width: 80%;
            order: 2;
            padding-left: 1.6%;
        }
    }


    .products-list {
        .product-items {
            .product-item {
                .product-item-info {
                    .product-item-photo {
                        max-width: 150px;
                    }

                    .product-item-details {
                        width: ~"calc(100% - 370px)";
                        padding-left: 20px;
                    }

                    .product-item-inner {
                        position: absolute;
                        width: 200px;
                        top: 10px;
                        right: 10px;
                    }

                    .product-item-actions {
                        .shipping-label {
                            margin-right: 0;
                        }
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .products-grid {
        .product-item {
            .product-item-info {
                .product-item-details {
                    .price-box {
                        min-height: 84.5px !important;
                        flex-direction: unset;
                        align-items: flex-end;

                        .old-price {
                            order: 1 !important;
                            margin: 5px 5px 0 0;
                        }

                        .special-price {
                            order: 3 !important;
                            flex-basis: 100%;
                            width: 100%;
                        }

                        .you-save-statement {
                            order: 2 !important;
                            margin: 0;
                        }
                    }
                }
            }
        }
    }

    .block.related,
    .block.upsell {
        .products-related,
        .products-upsell {
            .product-item {
                width: 20%;
            }
        }
    }

    .table-comparison {
        .price-box {
            &:extend(.products-grid .product-item .product-item-info .product-item-details .price-box all);
        }
    }

    .columns {
        .column {
            &.main {
                #amasty-shopby-product-list,
                #instant-search {
                    .products-grid {
                        .products {
                            &.items {
                                .product-item {
                                    width: 25%;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Product Detail Page Style
    .product-info-wrap {
        .product-info-top {
            width: ~"calc(100% - 192px)";
            padding-right: 20px;
        }
    }

    .product-info-main {
        .product-column-left {
            width: 7.5%;

            .left-column-wrapper {
                .product-attribute-icons {
                    li {
                        margin-bottom: 12px;
                    }
                }
            }
        }

        .product-column-media {
            width: 37%;

            .product {
                &.media {
                    min-height: 562px;
                }
            }
        }

        .product-column-addto {
            width: 34%;
            margin-top: -40px;
            padding-right: 18px;
        }

        .product-column-right {
            width: 17%;
            max-width: 192px;
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            overflow: hidden;
            border-left: 1px solid @color-gray90;
            display: block;
            border-top: 0;
            border-bottom: 0;
            flex-wrap: wrap !important;

            .product {
                width: 100%;
                padding: 10px 0 10px 20px;
                border-left: 0;
                border-bottom: 1px solid @color-gray90;

                &:first-child {
                    padding-top: 3px;
                    border-left: 0;
                }

                .block-image {
                    img {
                        &.desktop-only {
                            display: inline-block;
                        }

                        &.mobile-only {
                            display: none;
                        }
                    }
                }

                p {
                    .lib-font-size(14);
                    .lib-line-height(20);
                }

                &.overview {
                    h2 {
                        .lib-heading-typography(
                            @_font-size: 18,
                            @_line-height: 20,
                            @_color: @color-light-blue3
                        );
                        text-transform: uppercase;
                    }

                    ul,
                    ol {
                        .lib-css(padding-left, @indent__m);
                    }
                }
            }
        }
    }

    .catalog-product-view {
        .main {
            .product.info.detailed {
                .product.data.items {
                    .data.item.content {
                        display: none;
                    }
                }
            }
        }

        &.page-product-giftcard {
            .main .product.info.detailed .product.data.items {
                width: 100% !important;
                margin-left: 0 !important;
            }
        }

        .fotorama__nav {
            padding: 0 60px;
            background-color: @color-white;
            box-sizing: content-box;
            width: ~"calc(100% - 120px)" !important;
        }
    }
}


.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__ml) {
    .instant-search-page {
        .page-footer {
            min-height: 596px;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl) {
    .columns {
        .column {
            &.main {
                .subcat {
                    &.wrapper {
                        .subcategories {
                            .subcategory {
                                width: 16.72%;
                            }
                        }
                    }
                }
            }
        }
    }

    .product-info-main {
        .product-column-left {
            width: 90px;
        }

        .product-column-media {
            width: 43%;
            max-width: 530px;

            .product {
                &.media {
                    min-height: 632px;
                }
            }
        }

        .product-column-addto {
            width: 34%;
            max-width: 430px;
        }
    }
}

