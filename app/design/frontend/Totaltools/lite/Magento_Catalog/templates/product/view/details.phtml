<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php if ($detailedInfoGroup = $block->getGroupChildNames('detailed_info', 'getChildHtml')): ?>
<div class="product info detailed" id="detailed-accordion">
    <?php $layout = $block->getLayout(); ?>
    <?php
    $newPriority = array();

    foreach ($detailedInfoGroup as $name) {
        $alias = $layout->getElementAlias($name);
        $priority = $block->getChildData($alias, 'priority') ? $block->getChildData($alias, 'priority') : '10';
        array_push($newPriority, array($name, $priority));
    }
    # Sort array by priority
    usort($newPriority, function ($a, $b) {
        return $a['1'] <=> $b['1'];
    });
    ?>
    <div class="product data items" data-mage-init='{"accordion":{"openedState":"active", "collapsible": true, "active": false}}'>
        <?php foreach ($newPriority as $name): ?>
        <?php
        $name = $name[0];
        $html = $layout->renderElement($name);
        if (!trim($html)) {
            continue;
        }
        $alias = $layout->getElementAlias($name);
        $label = $block->getChildData($alias, 'title');
        ?>
        <div class="data item title" aria-labeledby="tab-label-<?= /* @escapeNotVerified */ $alias ?>-title" data-role="collapsible" id="tab-label-<?= /* @escapeNotVerified */ $alias ?>">
            <a class="data switch" tabindex="-1" data-toggle="switch" href="#<?= /* @escapeNotVerified */ $alias ?>" id="tab-label-<?= /* @escapeNotVerified */ $alias ?>-title">
                <?= /* @escapeNotVerified */ $label ?>
            </a>
        </div>
        <div class="data item content" id="<?= /* @escapeNotVerified */ $alias ?>" data-role="content">
            <?= /* @escapeNotVerified */ $html ?>
        </div>
        <?php endforeach; ?>
    </div>
</div>
<script type="text/x-magento-init">
    {
        "*": {
            "totaltools-sticky-nav": {}
        }
    }
</script>
<?php endif; ?> 
