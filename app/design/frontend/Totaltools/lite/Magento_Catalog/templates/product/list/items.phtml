<?php
// @codingStandardsIgnoreFile

/* @var $block \Magento\Catalog\Block\Product\AbstractProduct */
?>
<?php   
$brand = '';
if ($block->getProduct() && $block->getProduct()->getAttributeText('brand')){
    $brand = $block->getProduct()->getAttributeText('brand');
} 
?>

<?php
switch ($type = $block->getType()) {

    case 'related-rule':
        if ($exist = $block->hasItems()) {
            $type = 'related';
            $class = $type . ' homepage-block';
            
            $image = 'related_products_list';
            $title = __('You Might Also Need');
            $items = $block->getAllItems();
            $limit = 12; /*TOT0001-691: set maximum to 3 instead of $block->getPositionLimit() as default*/
            $shuffle = (int)$block->isShuffled();
            $canItemsAddToCart = $block->canItemsAddToCart();

            $showAddTo = true;
            $showCart = true;
            $templateType = null;
            $description = false;
        }
        break;

    case 'related':
        /** @var \Magento\Catalog\Block\Product\ProductList\Related $block */
        if ($exist = $block->getItems()->getSize()) {
            $type = 'related';
            $class = $type;

            $image = 'related_products_list';
            $title = __('Related Products');
            $items = $block->getItems();
            $limit = 12;
            $shuffle = 0;
            $canItemsAddToCart = $block->canItemsAddToCart();

            $showAddTo = true;
            $showCart = true;
            $templateType = null;
            $description = false;
        }
        break;

    case 'upsell-rule':
        if ($exist = $block->hasItems()) {
            $type = 'upsell';
            $class = $type . ' homepage-block';

            $image = 'upsell_products_list';
            $title = __("Other {$brand} Products That You May Need");
            $items = $block->getAllItems();
            $limit = $block->getPositionLimit();
            $shuffle = (int)$block->isShuffled();

            $showAddTo = true;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;;
            $description = false;
            $canItemsAddToCart = true;
        }
        break;

    case 'upsell':
        /** @var \Magento\Catalog\Block\Product\ProductList\Upsell $block */
        if ($exist = count($block->getItemCollection()->getItems())) {
            $type = 'upsell';
            $class = $type . ' homepage-block';

            $image = 'upsell_products_list';
            $title = __("Other {$brand} Products That You May Need");
            $items = $block->getItemCollection()->getItems();
            $limit = $block->getItemLimit('upsell');
            $shuffle = 0;

            $showAddTo = true;
            $showCart = false;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;;
            $description = false;
            $canItemsAddToCart = true;
        }
        break;

    case 'crosssell-rule':
        /** @var \Magento\Catalog\Block\Product\ProductList\Crosssell $block */
        if ($exist = $block->hasItems()) {
            $type = 'crosssell';
            $class = $type . ' homepage-block';

            $image = 'cart_cross_sell_products';
            $title = __('You may also need');
            $items = $block->getItemCollection();
            $limit = $block->getPositionLimit();
            $shuffle = (int)$block->isShuffled();

            $showAddTo = false;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
            $description = false;
            $canItemsAddToCart = false;
        }
        break;

    case 'crosssell':
        /** @var \Magento\Catalog\Block\Product\ProductList\Crosssell $block */
        if ($exist = $block->getItemCount()) {
            $type = 'crosssell';
            $class = $type . ' homepage-block';

            $image = 'cart_cross_sell_products';
            $title = __('You may also need');
            $items = $block->getItems();
            $limit = $block->getItemLimit('crosssell');
            $shuffle = 0;

            $showAddTo = false;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
            $description = false;
            $canItemsAddToCart = false;
        }
        break;

    case 'new':
        if ($exist = $block->getProductCollection()) {
            $type = 'new';
            $mode = 'grid';
            $type = $type . ' ' . $mode;

            $class = 'widget' . ' ' . $type;

            $image = 'new_products_content_widget_grid';
            $title = __('New Products');
            $items = $exist;

            $showAddTo = true;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
            $description = ($mode == 'list') ? true : false;
            $canItemsAddToCart = false;
        }
        break;

    case 'other':
        break;
}
?>

<?php if ($exist): ?>
<div class="block <?php echo $class; ?>" data-mage-init='{"upsellProducts":{}}' data-limit="<?php echo $limit; ?>" data-shuffle="<?php echo $shuffle; ?>" style="max-width: 100%;">
    <div class="block <?php echo $class; ?>">
        <div class="block-title title">
            <h2 id="block-<?php echo $class ?>-heading" role="heading" aria-level="2">
                <?php echo $title; ?></h2>
        </div>
        <div class="block-content content" aria-labelledby="block-<?php echo $class ?>-heading">
            <div class="products wrapper grid products-grid products-<?php echo $type; ?>">
                <ol class="products list items product-items" data-count="<?php echo count($items); ?>">
                    <?php $iterator = 1; ?>
                    <?php foreach ($items as $_item): ?>
                    <?php $available = ''; ?>
                    <?php if (!$_item->isComposite() && $_item->isSaleable() && $type == 'related'): ?>
                    <?php if (!$_item->getRequiredOptions()): ?>
                    <?php $available = 'related-available'; ?>
                    <?php endif; ?>
                    <?php endif; ?>
                    <?php if ($type == 'related' || $type == 'upsell'): ?>
                    <?php echo ($iterator++ == 1) ? '<li class="item product product-item" style="display: none;">' : '</li><li class="item product product-item" style="display: none;">' ?>
                    <?php else: ?>
                    <?php echo ($iterator++ == 1) ? '<li class="item product product-item">' : '</li><li class="item product product-item">' ?>
                    <?php endif; ?>
                    <div class="product-item-info <?php echo $available; ?>">
                        <?php echo '<!-- ' . $image . '-->' ?>
                        <a href="<?php echo $block->getProductUrl($_item) ?>" class="product photo product-item-photo">
                            <?php echo $block->getImage($_item, $image)->toHtml(); ?>
                        </a>
                        <?php if ($templateType): ?>
                        <?php if (($type == 'upsell') || ($type == 'upsell-rule') || ($type == 'crosssell-rule') || ($type == 'crosssell')): ?>
                        <?php  /*TOT0001-693: add displayIfNoReview: true to allow show review on upsell product*/?>
                        <?php echo $block->getReviewsSummaryHtml($_item, $templateType, true) ?>
                        <?php endif; ?>
                        <?php endif; ?>
                        <div class="product details product-item-details">

                            <?php if (($type == 'upsell') || ($type == 'upsell-rule') || ($type == 'crosssell-rule') || ($type == 'crosssell')): ?>
                            <?php  /* if ($_item->getAttributeText('brand')): ?>
                            <div class="brand">
                                <?php echo $_item->getAttributeText('brand');?>
                            </div>
                            <?php endif;*/ ?>
                            <?php endif; ?>

                            <strong class="product name product-item-name"><a class="product-item-link" title="<?php echo $block->escapeHtml($_item->getName()) ?>" href="<?php echo $block->getProductUrl($_item) ?>">
                                    <?php echo $block->escapeHtml($_item->getName()) ?></a>
                            </strong>

                            <?php echo $block->getProductPrice($_item); ?>

                            <?php if ($showAddTo || $showCart): ?>
                            <div class="product actions product-item-actions">
                                <?php if ($showCart): ?>
                                <div class="actions-primary">
                                    <?php if ($_item->isSaleable()): ?>
                                        <?php if ($_item->getTypeInstance()->hasRequiredOptions($_item)): ?>
                                        <button
                                            type="button"
                                            class="action todetails primary"
                                            onclick="window.location='<?= /* @noEscape */  $block->getProductUrl($_item); ?>'"
                                            title="<?php echo __('View Details') ?>"
                                        >
                                            <span>
                                                <?php echo __('View Details') ?>
                                            </span>
                                        </button>
                                        <?php else: ?>
                                        <?php $postDataHelper = $this->helper('Magento\Framework\Data\Helper\PostHelper');
                                        $postData = $postDataHelper->getPostData($block->getAddToCartUrl($_item), ['product' => $_item->getEntityId()])
                                        ?>
                                        <button class="action tocart primary" data-post='<?php echo $postData; ?>' type="button" title="<?php echo __('Add to Cart') ?>">
                                            <span>
                                                <?php echo __('Add to Cart') ?></span>
                                        </button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <button type="button"
                                            onclick="window.location='<?= /* @noEscape */  $block->getProductUrl($_item); ?>'"
                                            title="<?php echo $block->escapeHtml(__('View Details')); ?>"
                                            class="action todetails primary">
                                            <span><?php  echo __('View Details') ?></span>
                                        </button>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>

                                <?php if ($showAddTo): ?>
                                <div class="secondary-addto-links actions-secondary" data-role="add-to-links">
                                    <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                                    <?php echo $addToBlock->setProduct($_item)->getChildHtml(); ?>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php echo ($iterator == count($items) + 1) ? '</li>' : '' ?>
                    <?php endforeach ?>
                </ol>
            </div>
        </div>
    </div>
    <?php endif; ?>

<?php if (($type == 'upsell') || ($type == 'upsell-rule') || ($type == 'crosssell-rule') || ($type == 'crosssell') || ($type == 'related')): ?>
<script>
    require(['jquery', 'slick'], function($) {
        'use strict';

        $(function() {
            $('.products-<?php echo $type; ?> .product-items').slick({
                slidesToShow: 5,
                dots: false,
                infinite: false,
                arrows: true,
                responsive: [{
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 2
                    }
                }]
            });
        });
    });
</script>
<?php endif; ?>