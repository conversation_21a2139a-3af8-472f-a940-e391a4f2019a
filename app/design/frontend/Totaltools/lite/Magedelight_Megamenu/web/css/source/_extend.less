
& when (@media-common = true) {
    .ms-featured {
        .lib-css(display, none);
    }

    .nav-bar {
        .nav-bar-container {
            background: @primary__color;
        }
        
        .shop-by-menu {
            background-color: @color-white;
            position: fixed;
            z-index: 1002;
            height: 100vh;
            width: 100vw;
            top: 0;
            left: -100vw;
            padding: 0;
            overflow-x: hidden;
            overflow-y: auto;
            transition: left 0.3s ease-in-out;
            max-height: -webkit-fill-available;

            .shop-by-wrap {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                position: relative;
                order: 3;
                width: 100%;

                .section-item-content {
                    width: 100%;
                }
            }

            &.nav-menu-open {
                left: 0;
            }

            .close-navigation {
                position: sticky;
                z-index: 121;
                top: 0;
                right: 0;
                padding: 0;
                text-decoration: none;
                font-size: 35px;
                line-height: 35px;
                color: @primary__color;
                .lib-css(font-weight, @font-weight__regular);
                .lib-vendor-prefix-display;
                .lib-css(justify-content, center, 1);
                .lib-css(align-items, center, 1);
                height: 48px;
                width: 48px;
                border: 0;
                outline: unset;
                background: @color-white;
                order: 2;
            }

            .custom-tabs {
                margin: 0;
                padding: 0;
                display: none;
                overflow: auto;
                width: ~"calc(100% - 48px)";
                // width: 100%;
                background: @color-white;
                position: sticky;
                top: 0;
                left: 0;
                z-index: 120;
                order: 1;

                button {
                    border: 0;
                    padding: 0 15px;
                    letter-spacing: -0.5px;
                    color: @color-gray20;
                    position: relative;
                    border-radius: 0;
                    background: transparent;
                    border: 0;
                    .lib-heading-typography(@_font-size:16, @_line-height: 48, @_color: @color-gray20);
                    white-space: nowrap;

                    &.active {
                        color: @color-white;
                        background: @color-blue;
                    }
                }
            }
        }

        .section-item-content {

            .menu-container {
                border: 0;

                .menu{
                    &.vertical-left {
                        background: @color-white;

                        .back-btn {
				            position: absolute;
                            z-index: 115;
                            top: 0;
                            left: 0;
                            padding: 0;
                            .lib-css(font-weight, @font-weight__bold);
                            .lib-line-height(40);
                            height: 44px;
                            width: 44px;
                            background: transparent;
                            border: 0;
                            text-decoration: none;
                            outline: unset;
                            .lib-css(box-shadow, unset , 1) !important;
                            background: @color-white;
                            border-right: @color-gray4 solid 1px;
                            border-radius: 0;
                            
                            &::before {
                                content: @tt-icon-left-open;
                                color: @color-gray20;
                                font-size: 16px;
                                .lib-css(font-weight, 400);
                                .lib-css(font-family, @font-tt-icons);
                            }
                        }

                        .navigation-trigger {
                            height: auto;
                            border: 0;
                            .lib-css(padding, 0 @indent__m);
                            .lib-heading-typography(@_font-size:16, @_line-height: 48, @_color: @color-white);
                            position: relative;
                        }

                        .navigation-mobile-label {
                            .lib-css(border-bottom, 1px solid @color-gray4);
                            .lib-css(font-weight, @font-weight__bold);
                            font-family: @font-family__base;
                            background: transparent;
                            text-transform: capitalize;
                            height: auto;
                            color: @color-gray20 !important;
                            padding: 15px 45px;
                            font-size: 24px;
                            text-align: center;
                            white-space: normal;
                        }

                        & > ul {
                            background: @color-white;
                            height: ~"calc(100vh - 48px)";
                            overflow-y: auto;
                            overflow-x: hidden;

                            &.open-menu {
                                display: block;
                            }

                            & > li {
                                .lib-vendor-prefix-display;
                                .lib-vendor-prefix-flex-wrap;
                                .lib-css(align-items, center, 1);
                                position: static;
                                border-top: 1px solid @color-gray4;
                                float: unset;

                                &:first-child {
                                    border-top: 0;
                                }

                                &:hover {
                                    background: transparent;

                                    & > a {
                                        & .active {
                                            color: @color-gray20;
                                        }
                                    }
                                }

                                & > a {
                                    font-size: 0;
                                    line-height: 1;
                                    .lib-css(font-weight, 500);
                                    padding: 10px 10px;
                                    color: @color-gray20;
                                    background: transparent;
                                    width: ~"calc(100% - 44px)";

                                    .megaitemicons {
                                        .lib-font-size(16);
                                        .lib-line-height(24);
                                        white-space: normal;
                                        margin: 0;
                                        display: block;
                                    }

                                    &::before {
                                        display: none;
                                    }

                                    &.active {

                                        & ~ ul {
                                            left: 0 !important;
                                        }
                                    }
                                }

                                &>span { 
                                    &.plus {
                                        width: 44px;
                                        height: 44px;
                                        position: static;
                                        display: block;

                                        & + .plus {
                                            display: none;
                                        }
                                    }

                                    &::before {
                                        content: @tt-icon-right-open;
                                        color: @color-gray20;
                                        font-size: 14px;
                                        .lib-css(font-weight, 400);
                                        .lib-css(font-family, @font-tt-icons);
                                    }

                                    &.active{
                                        &::before {
                                            content: @tt-icon-right-open;
                                            color: @color-gray20;
                                        }
                                    }
                                }

                                .child-level-1 {
                                    li {
                                        .lib-css(flex-basis, 100%, 1);
                                    }
                                }

                                & > ul {
                                    margin: 0;
                                    position: fixed;
                                    border: 0;
                                    left: ~"calc(100% + 10px)" !important;
                                    top: 48px;
                                    padding: 0 10px;
                                    z-index: 110;
                                    animation: unset !important;
                                    width: 100%;
                                    .lib-css(box-shadow, unset , 1);
                                    height: ~"calc(100vh - 48px)";
                                    transition: 0.4s all ease;
                                    display: block;
                                    overflow-y: auto;
                                    overflow-x: hidden;

                                    &.column1 {
                                        width: 100%;
                                    }

                                    & > li {
                                        padding: 0;
                                        width: 100%;
                                        display: block;
                                        position: relative;

                                        .see-all-link {
                                            padding: 0;
                                            display: block;

                                            &::before {
                                                display: none;
                                            }

                                            & ~ .see-all-link {
                                                display: none;
                                            }
                                        }

                                        .see-all {
                                            font-size: 16px;
                                            line-height: 1.5;
                                            .lib-css(font-weight, 500);
                                            padding: 10px 0 10px 54px;
                                            color: @primary__color;
                                            background: transparent;
                                            display: block;
                                            position: relative;
                                            border-bottom: @color-gray4 solid 1px;


                                            &::before {
                                                content: "See All Products in '";
                                            }

                                            & ~ .see-all {
                                                display: none;
                                            }
                                        }

                                        ul {
                                            padding: 0;
                                            margin: 0;

                                            & > li {
                                                border-top: @color-gray4 solid 1px;
                                                float: unset;
                                                padding: 0;
                                                .lib-vendor-prefix-display;
                                                .lib-vendor-prefix-flex-wrap;
                                                .lib-css(align-items, center, 1);
                                                width: 100%;

                                                &:first-child {
                                                    border-top: 0;
                                                }

                                                &:hover {
                                                    & > a {
                                                        & .active {
                                                            color: @color-gray20;
                                                        }
                                                    }
                                                }

                                                &.rotate {
                                                    span {
                                                        transform: rotate(180deg);
                                                    }
                                                }

                                                &.has-sub-category {
                                                    & > a {
                                                        width: ~"calc(100% - 44px)";
                                                    }
                                                }

                                                & > a {
                                                    font-size: 16px;
                                                    line-height: 1.5;
                                                    font-weight: 500;
                                                    padding: 10px 0;
                                                    color: @color-gray20;
                                                    background: transparent;
                                                    display: block;
                                                    position: relative;
                                                    width: 100%;

                                                    &::before {
                                                        display: none;
                                                    }
                                                }

                                                & > span {
                                                    width: 44px;
                                                    height: 44px;
                                                    cursor: pointer;
                                                    .lib-vendor-prefix-display;
                                                    .lib-css(justify-content, center, 1);
                                                    .lib-css(align-items, center, 1);
                                                    margin: 0;
                                                    font-size: 22px;

                                                    & ~ span {
                                                        display: none;
                                                    }

                                                    &::before {
                                                        content: @tt-icon-down-open;
                                                        color: @color-gray20;
                                                        font-size: 14px;
                                                        .lib-css(font-weight, @font-weight__bold);
                                                        .lib-css(font-family, @font-tt-icons);
                                                        padding-top: 3px;
                                                    }
                                                }

                                                .child-level-2 {
                                                    display: none;
                                                    margin-left: 15px;

                                                    & > li {
                                                        border-top: @color-gray4 solid 1px;

                                                        & > a {
                                                            width: 100%;
                                                            .lib-css(font-weight, @font-weight__regular);
                                                        }

                                                        & > span {
                                                            display: none;
                                                        }
                                                    }
                                                }

                                                & > div {
                                                    padding: 0;
                                                    background-color: transparent;
                                                    margin: 0;
                                                }
                                            }
                                        }
                                    }

                                    & .menu-sidebar-right {
                                        display: none
                                    }

                                    & .menu-footer {
                                        display: none;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .navigation-open {
        body {
            height: 100%;
            overflow: hidden;
            position: relative;
        }
    }
}

//
//  Mobile
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .nav-bar {
        .section-item-content {
            .menu-container {
                .menu {
                    &.vertical-left {

                        .navigation-trigger {
                            display: none;
                            color: @color-gray20 !important;
                            
                            &.nav-bar-link {
                                &.active {
                                    background: @secondary__color;
                                    color: @color-white !important;
                                }
                            }
                        }

                        & > ul {
                            background-color: @color-white;
                            width: 100%;
                            .lib-css(box-sizing, border-box, 1);
                            transition: all 0.3s ease-in-out;
                            display: none;
                            padding: 0 10px;
                        }
                    }
                }
            }

            &.open-menu {
                .menu-container {
                    .menu {
                        &.vertical-left { 
                            & > ul {
                                display: block;
                            }
                        }
                    }
                }
            }
        }

        .shop-by-menu { 
            .custom-tabs { 
                display: flex !important;
            }
        }
    }
}

//
//  Tablets
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .nav-bar {
        .nav-bar-container {
            position: relative;
            background: transparent;
            padding: 0 20px;
        }

        .section-item-content {
            position: static !important;
            width: auto !important;
            height: auto !important;
            background: transparent !important;

            .menu-container {
                // max-width: 225px;
                width: 100%;

                .menu {
                    &.vertical-left {
                        background: transparent !important;
                        position: static;
                    }
                }

                &.shortcodemenu {
                    .menu {
                        &.vertical-left {

                            .navigation-trigger {
                                height: auto;
                                text-align: left;
                                background: @color-black-15;
                                // min-width: 225px;
                                border: 0;
                                .lib-css(padding, 0 @indent__m);
                                .lib-heading-typography(@_font-size:16, @_line-height: 48, @_color: @color-white);
                                display: none;
                                cursor: pointer;
                                position: relative;

                                &.active {
                                    background: @secondary__color;
                                }

                                &::before {
                                    font-size: 24px;
                                    line-height: inherit;
                                    color: @primary__color;
                                    content: '\e816';
                                    .lib-css(font-family, @font-tt-icons);
                                    margin: -3px 5px 0 0;
                                    vertical-align: middle;
                                    display: none;
                                    font-weight: 400;
                                    text-align: center
                                }

                                &:focus {
                                    background: @color-black-40;

                                    & ~ ul {
                                        display: block;
                                    }
                                }
                            }

                            & > ul {
                                background-color: @color-white;
                                width: 100vw;
                                .lib-css(box-sizing, border-box, 1);
                                transition: all .3s ease-in-out;
                                display: none;
                                padding: 0 10px;

                                &.open-menu {
                                    display: block !important;
                                }

                                & > li {
                                    .lib-vendor-prefix-display;
                                    .lib-vendor-prefix-flex-wrap;
                                    .lib-css(align-items, flex-start, 1);

                                    &:hover {
                                        background: @color-gray97;

                                        & > a {
                                            &::before {
                                                .lib-css(color, @primary__color);
                                            }
                                        }
                                    }

                                    &.menu-dropdown-icon {
                                        & > a {
                                            width: ~"calc(100% - 44px)";
                                        }
                                    }

                                    & > a {
                                        text-transform: capitalize;
                                        display: inline-block;
                                        width: 100%;
                                        padding: 10px 16px 10px 10px;
                                        font-family: @font-family__base;
                                        font-size: 0;
                                        color: @color-gray20;
                                        line-height: 1;
                                        .lib-css(font-weight, @font-weight__bold);

                                        .megaitemicons {
                                            margin: 0;
                                            font-size: 15px;
                                            line-height: 24px;
                                        }

                                        &::before {
                                            content: @tt-icon-right-open;
                                            color: @primary__color;
                                            .lib-css(font-weight, @font-weight__bold);
                                            display: block;
                                            .lib-css(font-family, @font-tt-icons);
                                            font-size: 14px;
                                            display: none
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            &.open-menu {
                .menu-container {
                    .menu {
                        &.vertical-left { 
                            & > ul {
                                display: block;
                            }
                        }
                    }
                }
            }
        }

        .shop-by-menu { 
            .custom-tabs { 
                display: flex !important;

                button {
                    text-transform: uppercase;
                }
            }
        }
    }

    .ms-featured {
        display: block;
        padding-left: 30px;
        padding-bottom: 20px;

        .ms-boxtitle {
            color: @color-gray20;
            font-family: @font-family__base;
            .lib-css(font-weight, @font-weight__regular);
            text-transform: capitalize;
            display: block;
            margin: 3px 0 20px;
        }

        .f-category {
            margin-bottom: 20px;
            position: relative;

            a {
                &::before {
                    display: none;
                }

                &.product-name {
                    display: block;
                    padding: 17px 12px !important;
                    border: 1px solid @color-gray10;
                    background-color: @color-gray96;
                    background-repeat: repeat-x;
                    color: @secondary__color !important;
                    background-image: linear-gradient(to bottom,@color-gray10 0%, @color-gray96 100%);
                    font-size: 16px;
                    line-height: 20px;
                    .lib-css(font-weight, @font-weight__bold);
                    text-transform: uppercase;
                    font-family: @font-family__base;
                }

                &.product-image {
                    position: absolute;
                    top: -10px;
                    right: 20px;
                    display: none;

                    img {
                        width: 60px;
                        height: 60px;
                    }
                }
            }
        }
    }
}

//
//  Small Desktop
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .nav-bar {
        .shop-by-menu {
            background: transparent;    
            position: relative;
            left: 0;
            top: 0;
            overflow: unset;
            height: auto;
            width: auto;
            z-index: 5;
            max-height: unset;

            .close-navigation {
                display: none;
            }

            .custom-tabs { 
                display: none !important;
            }
        }

        .section-item-content {
            &:first-child {
                border-right: 1px solid rgba(255, 255, 255, 0.45);
            }

            &:last-child {
                .menu-container {
                    &.shortcodemenu { 
                        .menu {
                            &.vertical-left {
                                .navigation-trigger {
                                    border-right: 0;
                                }
                            }
                        }
                    }
                }
            }
            .menu-container {
                &.shortcodemenu { 
                    .menu {
                        &.vertical-left {
                            position: relative;

                            .back-btn {
                                display: none;
                            }

                            .navigation-trigger {
                                text-transform: uppercase;
                                display: inline-block;

                                &.active {
                                    background: @color-black-40;
                                }

                                &:hover {
                                    background: @color-black-40;
                                }

                                &::before {
                                    display: inline-block;
                                }
                            }

                            .navigation-mobile-label {
                                display: none;
                            }

                            & > ul {
                                width: 270px;
                                border: @color-gray4 solid 1px;
                                border-top: 0;
                                padding: 0;
                                left: 0;
                                top: 100%;
                                position: absolute;
                                z-index: 20;
                                height: auto;
                                overflow-y: unset;
                                overflow-x: unset;

                                
                                & > li {
                                    position: static;
                                    
                                    & > a {
                                        width: 100% !important;
                                    }

                                    & > span.plus {
                                        display: none;
                                    }

                                    &.menu-dropdown-icon {
                                        & > a {
                                            &::before {
                                                display: block;
                                            }
                                        }
                                    }

                                    &:hover {
                                        & > ul {
                                            display: block !important;
                                        }
                                    }

                                    & > ul {
                                        max-width: ~"calc(100vw - 325px)";
                                        width: 972px !important;
                                        padding: 30px 20px;
                                        border-color: @color-gray10;
                                        box-shadow: 3px 3px 3px @color-black-07;
                                        display: none !important;
                                        left: 100% !important;
                                        min-height: 100%;
                                        border-left: @color-gray4 solid 1px;
                                        height: auto !important;
                                        position: absolute;
                                        top: 0;

                                        & > .menu-content {
                                            width: 66.66%;

                                            .see-all {
                                                display: none;
                                            }

                                            & > ul {
                                                display: block;
                                                column-count: 3;

                                                & > li {
                                                    padding: 3px 0;
                                                    border: 0;

                                                    &.has-sub-category {
                                                        & > a {
                                                            width: 100%;
                                                        }
                                                    }

                                                    & > a {
                                                        .lib-line-height(20);
                                                        padding: 0;

                                                        &:hover {
                                                            color: @color-dark-red;
                                                        }

                                                        &::before {
                                                            display: none;
                                                        }
                                                    }

                                                    & > span {
                                                        display: none;
                                                    }
                                                }



                                                &.child-level-1 {
                                                    &>li {
                                                        &>a {
                                                            .lib-font-size(15);
                                                            font-family: @font-family__base;
                                                            .lib-css(font-weight, @font-weight__bold);
                                                            text-transform: capitalize;
                                                        }

                                                        .child-level-2 {
                                                            display: block;
                                                            margin: 0;
                                                            padding-top: 3px;

                                                            & > li {
                                                                border-top: 0;
                                                                padding: 3px 0;

                                                                & > a {
                                                                    .lib-font-size(15);
                                                                    line-height: 2rem;
                                                                    padding: 0;

                                                                    &:hover {
                                                                        color: @color-dark-red;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        li {
                                            .sb-submenu {
                                                & > li {
                                                    padding: 3px 0;
                                                    border: 0;

                                                    & > a {
                                                        padding: 0;
                                                        font-family: @font-family__base;
                                                        .lib-css(font-weight, @font-weight__bold);
                                                        text-transform: capitalize;
                                                        .lib-font-size(15);
                                                        .lib-line-height(20);
                                                    }
                                                }
                                            }
                                        }

                                        & .menu-sidebar-right {
                                            display: block;
                                            width: 33.33%;

                                            li {
                                                width: 100%;
                                            }
                                        }

                                        & .menu-footer {
                                            display: block;
                                            padding: 10px 0;
                                            border-top: 1px solid @color-gray4;
                                            margin-top: 20px;

                                            ul {
                                                display: block;

                                                li {
                                                    display: block;
                                                }
                                            }

                                            p {
                                                color: @color-gray20;
                                                font-family: @font-family__base;
                                                .lib-css(font-weight, @font-weight__bold);
                                                text-transform: capitalize;
                                                margin: 15px 0 20px;
                                                display: block;
                                                font-size: 16px;
                                                line-height: 20px;
                                            }

                                            ol {
                                                display: flex;
                                                flex-wrap: wrap;
                                                padding: 0;

                                                li {
                                                    width: 20%;

                                                    a {
                                                        display: block;

                                                        &:hover {
                                                            padding: 0;
                                                        }
                                                        &::before {
                                                            display: none;
                                                        }

                                                        img {
                                                            width: 110px;
                                                            margin: 0 auto;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl ) {
    .nav-bar {
        .section-item-content {
            .menu-container {
                // max-width: 300px;

                &.shortcodemenu {
                    .menu{
                        &.vertical-left {
                            .navigation-trigger {
                                // min-width: 300px;
                            }
                            & > ul {
                                // max-width: 300px;

                                & > li {

                                    & > ul {
                                        // max-width: ~"calc(100vw - 340px)";
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

