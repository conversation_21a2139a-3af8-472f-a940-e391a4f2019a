<?php
/**
 * Magedelight
 * Copyright (C) 2017 Magedelight <<EMAIL>>
 *
 * @category Magedelight
 * @package Magedelight_Megamenu
 * @copyright Copyright (c) 2017 Mage Delight (http://www.magedelight.com/)
 * @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */
?>
<?php $_menu = $block->getHtml() ?>
<?php
$enabled = $block->getIsEnabled();
$menuId = $block->getMenuid();
$megaMenu = $block->getCurrentMegaMenu($menuId);
$megaMenuIdentifier = $megaMenu->getMenuIdentifier();
if($enabled) {
?>
<div class="section-item-content" id="<?= /* @noEscape */ 'shortcode_menu_'.$menuId; ?>">
    <div class="menu-container shortcodemenu">
        <div class="menu <?= /* @noEscape */ $block->getMenuDesign(); ?> <?php
        if ($block->isStickyEnable() == '1'):
            echo "stickymenu";
        endif;
        ?>">
            <div class="navigation-trigger nav-bar-link">
                <span><?= /* @noEscape */ $megaMenu->getMenuName() ?><span>
            </div>
           <ul class="topmenu-block">
                <?= /* @noEscape */ $_menu; ?>
            </ul>
            <?php
            echo $block->menuStyleHtml();
            ?>
        </div>
        <?php
        if ($block->isStickyEnable() == '1') {
            ?>            
            <div class="stickyalias"> </div>
            <?php
        }
        ?>
    </div>
</div>
<?php }?>
<script type="text/javascript" async>
    var animation_time = '<?= /* @noEscape */ $block->animationTime(); ?>';
    require(['jquery'], function ($) {

        var nav = $('.shortcodemenu');
        if (nav.length) {
            var stickyHeaderTop = nav.offset().top;
            var menuWidth = jQuery('.shortcodemenu .menu.stickymenu').width();
            //var topMargin;
            $(window).scroll(function () {
                if ($(window).width() >= 767) {
                    if ($(window).scrollTop() > stickyHeaderTop) {
                        $('.shortcodemenu .stickymenu').css({position: 'fixed', top: '0px', 'max-width': menuWidth});
                        $('.stickyalias').css('display', 'block');
                    } else {
                        $('.shortcodemenu .stickymenu').css({position: 'static', top: '0px'});
                        $('.stickyalias').css('display', 'none');
                    }
                }
            });

            $('.section-item-content .menu-container.shortcodemenu .menu > ul > li.dropdown').each(function (e) {
                $(this).children('a').after('<span class="plus"></span>');
            });
        }

        $('#shortcode_menu_<?= /* @noEscape */ $menuId ?> .vertical-left .navigation-trigger').on("click", function () {
            $('.section-item-content').removeClass('open-menu');
            $('.navigation-trigger').not($(this)).removeClass('active');
            $('.topmenu-block').not($(this).siblings('.topmenu-block')).removeClass('open-menu');
            if ( $( window ).width() > 1023 ) {
                $(this).siblings('.topmenu-block').toggleClass('open-menu');
                $(this).toggleClass('active');
            }
        });

        if ( $( window ).width() > 1023 ) {
            $(document).on("click", function(event){
                var $trigger = $(".vertical-left");
                if(!$trigger.has(event.target).length){
                    $(".topmenu-block").removeClass('open-menu');
                    $(".vertical-left .navigation-trigger").removeClass('active');
                }  
                else {
                    $(this).siblings('.topmenu-block').addClass('open-menu');
                    $(this).addClass('active');
                }            
            });
        }

        $('.shop-by-menu .close-navigation').on("click", function () {
            $(".nav-bar .nav-bar-container .shop-by-menu").removeClass('nav-menu-open');
            $("html").removeClass('nav-open');
            $(".section-item-content .menu-container .menu.vertical-left > ul > li > a").removeClass('active');
            $('.section-item-content .menu-container .menu.vertical-left > ul > li > ul > li ul > li').removeClass('rotate');
            $('.section-item-content .menu-container .menu.vertical-left > ul > li > ul > li ul > li .child-level-2').slideUp();
            setTimeout(function() { 
                $(".menu.vertical-left .navigation-mobile-label").text("<?= /* @noEscape */ $megaMenu->getMenuName() ?>");
                $("html").removeClass('navigation-open');
                $("html").removeClass('nav-before-open');
            }, 500);
        });

        $('.page-header .nav-toggle').on("click", function () {
            $('.custom-tabs button').removeClass('active');
            $('.topmenu-block').removeClass('open-menu');
            $('.section-item-content:first-child').addClass('open-menu');
            $(".custom-tabs button:first-child").addClass('active'); 
            $(".nav-bar .nav-bar-container .shop-by-menu").addClass('nav-menu-open');
            $("html").addClass('navigation-open');
        });

        $('.section-item-content .menu-container .menu.vertical-left > ul > li > span').on("click", function () {
            $(this).parent().find("> a").addClass('active');
            var category = $(this).parent().find("> a > .megaitemicons").text();
            var categoryUrl = $(this).parent().find("> a").attr('href');
            $(".see-all").text(category.trim()+ "'");
            $(".see-all-link").attr("href", categoryUrl);
            $(".menu.vertical-left .navigation-mobile-label").text(category.trim());
        });
        $( "<span></span>" ).insertAfter( ".child-level-1 .has-sub-category>a" );
        $( "<a class='see-all-link' ><span class='see-all'></span></a>" ).insertBefore( ".section-item-content .menu-container .menu.vertical-left > ul > li > ul > li.menu-content > ul" );
        $( "<button type='button' class='back-btn'></button>" ).insertBefore( ".section-item-content .menu-container .menu.vertical-left > ul > li > ul > li.menu-content > ul" );
        $('.section-item-content .menu-container .menu.vertical-left > ul > li > ul > li ul > li > span').on("click", function () {
            var $this = $(this);
            if ($this.parent().hasClass('rotate')) {
                $this.parent().removeClass('rotate');
                $this.parent().parent().find('li .child-level-2').slideUp(350);
            } else {
                $this.parent().parent().find('li').removeClass('rotate');
                $this.parent().parent().find('li .child-level-2').slideUp(350);
                $this.parent().toggleClass('rotate');
                $this.parent().find('.child-level-2').slideToggle(350);
            }
        });
        
        $('.back-btn').on("click", function () {
            $(".section-item-content .menu-container .menu.vertical-left > ul > li > a").removeClass('active');
            $(".menu.vertical-left .navigation-mobile-label").text("<?= /* @noEscape */ $megaMenu->getMenuName() ?>");
        });

        $('.custom-tabs button').on("click", function () {
            $('.custom-tabs button').not($(this)).removeClass('active');
            $('.section-item-content').removeClass('open-menu');
            $(".section-item-content").eq($(this).index()).addClass('open-menu');
            $(this).addClass('active'); 
            $(".section-item-content .menu-container .menu.vertical-left > ul > li > a").removeClass('active');
            $('.section-item-content .menu-container .menu.vertical-left > ul > li > ul > li ul > li').removeClass('rotate');
            $('.section-item-content .menu-container .menu.vertical-left > ul > li > ul > li ul > li .child-level-2').slideUp();     
        });
    });
</script>