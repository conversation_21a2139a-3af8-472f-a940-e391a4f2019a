<div
    class="fulfillment-box-content estimate-delivery"
    data-bind="blockLoader: isLoading"
>
    <div class="selected-store">
        <div class="selected-store-wrap">
            <h2
                class="selected-store-title selected-store-title-product"
                data-bind="i18n: 'Click & Collect OR In-store Pickup'"
            ></h2>
            <h2
                class="selected-store-title quick-view-title"
                data-bind="i18n: 'Click & Collect Stock Check'"
            ></h2>
            <p
                class="click-collect-stock"
                data-bind="i18n: 'Check stock availability at nearby stores'"
            ></p>
            <div class="store-form">
                <div class="store-zipcode">
                    <input
                        type="text"
                        name="estimate-change-store-postcode"
                        id="estimate-change-store-postcode-input"
                        data-bind="
                            value: selectedStoreText,
                            event: { keyup: estimateFetchSuburbs },
                            attr: {
                                placeholder: 'Enter Postcode/suburb for nearest store', autocomplete: 'off'
                            }
                        "
                    />
                    <button
                        type="submit"
                        title="Reset feild"
                        class="reset-feild"
                        data-bind="click:clearSelectedStore"
                    >
                        <span>×</span>
                    </button>
                </div>
                <!-- ko if: estimateSuburbs().length || stores().length -->
                <div class="store-options invert">
                    <ul class="store-list">
                        <!--ko foreach: { data: estimateSuburbs, as: 'suburb'} -->
                        <li class="item-suburb">
                            <a
                                data-bind="click: function(data, event) { $parent.fetchStores(city, postcode, event) }"
                                data-trigger="store-trigger"
                            >
                                <span
                                    data-bind="html: cityHtml + ' ' + postcodeHtml"
                                ></span>
                            </a>
                        </li>
                        <!-- /ko -->
                    </ul>
                </div>
                <!-- /ko -->
            </div>
            <!-- ko if: selectedStoreUpdate() && selectedStore() && selectedStore().zipcode && selectedStore().is_visible == '1'-->
            <p class="selected-store-location">
                <span data-bind=""></span
                ><a
                    data-bind="text: selectedStoreText, title: 'Change location & store'"
                ></a>
            </p>
            <small class="selected-store-address">
                <!-- ko text: selectedStore().address--><!-- /ko -->
                |
                <a
                    class="selected-store-phone"
                    data-bind="attr: { href: 'tel:' + selectedStore().phone }, text: 'Call: ' + selectedStore().phone"
                ></a>
            </small>
            <ul class="click-and-collect-messages">
                <li
                    class="availability"
                    data-bind="css: {
                        'available'      :  clickAndCollectMessage().code == '0',
                        'low-stock'      :  clickAndCollectMessage().code == '1',
                        'not-available'  :  clickAndCollectMessage().code >= '2'
                    }"
                >
                    <span
                        class="stock-status conv-test-applied"
                        data-bind="html: clickAndCollectMessage().message"
                    ></span>
                </li>
            </ul>
            <!-- /ko -->
        </div>
    </div>
    <div class="selected-suburb">
        <div class="selected-suburb-wrap">
            <div
                class="product attribute large free-shipping-icon"
                if="freeShipping() == '1'"
            >
                <img
                    class="conv-test-applied"
                    data-bind="attr:{src: freeShippingIcon}"
                    alt="Free Delivery"
                />
            </div>
            <div class="fulfillment-box-header">
                <h2
                    class="selected-suburb-title selected-suburb-title-product conv-test-applied"
                    data-bind="i18n: 'Delivery Availability'"
                ></h2>
                <h2
                    class="selected-suburb-title quick-view-title"
                    data-bind="i18n: 'Delivery Stock Check'"
                ></h2>
                <p
                    class="express-delivery-block"
                    data-bind="i18n: 'Express delivery available for certain items!'"
                ></p>
            </div>

            <div class="field search store-form">
                <div class="store-zipcode">
                    <input
                        type="text"
                        name="estimate-delivery-postcode"
                        id="estimate-delivery-postcode"
                        data-bind="value: selectedSuburbText,  event: {keyup: fetchSuburbs},
                            attr: {placeholder: 'Enter Postcode/suburb for delivery estimate', autocomplete: 'off'}"
                    />
                    <button
                        type="submit"
                        title="Reset feild"
                        class="reset-feild"
                        data-bind="click:clearSuburb"
                    >
                        <span>×</span>
                    </button>
                </div>
                <div class="store-options location-options">
                    <ul
                        class="store-list"
                        data-bind="visible: suburbs().length"
                    >
                        <!--ko foreach: { data: suburbs, as: 'suburb'} -->
                        <li class="item-suburb">
                            <a
                                data-bind="click: $parent.deliveryEstimate.bind($parent)"
                            >
                                <span
                                    data-bind="html: cityHtml + ' ' + postcodeHtml"
                                ></span>
                            </a>
                        </li>
                        <!-- /ko -->
                    </ul>
                </div>
            </div>
            <!-- ko if: suburbUpdated()  &&  selectedSuburb() && selectedSuburb().postcode -->
                <!-- ko if: estimatedQuote() -->
                    <div class="delivery-methods-wrapper">
                        <!-- ko foreach: estimatedQuote -->
                            <div data-bind="css: shipping_class">
                                <span data-bind="text: shipping_name"></span>
                                <span data-bind="text: shipping_price, css: {'free': shipping_price == 'FREE'}"></span>
                                <p data-bind="html: shipping_message"></p>
                                <!-- ko if: product_price < 99 && shipping_class == 'standard' -->
                                    <p data-bind="i18n: 'Free Shipping over $99'" class="free-shipping-note"></p>
                                <!-- /ko -->
                            </div>
                        <!-- /ko -->
                    </div>
                <!-- /ko -->
                <!-- ko if: !estimatedQuote() && deliveryMessage().code < 5 && deliveryMessage().dangerous_item != '1' && deliveryMessage().knife_compliance_item != '1' -->
                <a
                    class="get-delivery-cost"
                    data-bind="i18n: 'Click here to get the delivery estimate', click: getDeliveryCost"
                ></a>
                <!-- /ko -->
                <!-- ko if: deliveryMessage().code >= 5 || deliveryMessage().dangerous_item == '1' || deliveryMessage().knife_compliance_item == '1' -->
                <ul class="delivery-messages">
                    <li
                        class="availability"
                        data-bind="css: {
                                    'available'      :  deliveryMessage().code == '0',
                                    'low-stock'      :  deliveryMessage().code == '1',
                                    'not-available'  :  deliveryMessage().code >= '2'
                                }"
                    >
                        <span class="stock-status conv-test-applied" data-bind="html: deliveryMessage().message"></span> 
                    </li>
                </ul>
                <!-- /ko -->
            <!-- /ko -->
        </div>
    </div>
</div>
<!-- ko template: 'Totaltools_Catalog/fulfilment-popup' --><!-- /ko -->
