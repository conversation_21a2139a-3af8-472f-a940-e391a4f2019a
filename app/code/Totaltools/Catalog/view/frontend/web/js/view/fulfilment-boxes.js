define([
    'jquery',
    'ko',
    'mage/url',
    'Magento_Ui/js/form/form',
    'Totaltools_Catalog/js/model/selected-location',
    'Totaltools_Catalog/js/model/selected-store',
    'Totaltools_Geo/js/model/geolocation',
    'Totaltools_Checkout/js/model/use-current-location',
    'Totaltools_Postcodes/js/fetch-suburbs',
    'Totaltools_Storelocator/js/product/stockcheck',
    'Magento_Customer/js/customer-data',
    'Magento_Ui/js/modal/modal',
    'mage/translate',
], function (
    $,
    ko,
    url,
    Component,
    selectedLocation,
    selectedStore,
    geolocation,
    useCurrentLocation,
    fetchSuburbs,
    stockcheck,
    customerData,
    modal,
    $t
) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Catalog/fulfilment-boxes',
            isLoading: false,
        },
        clickAndCollectMessage: ko.observable({ message: '', code: -1 }),
        deliveryMessage: ko.observable({ message: '', code: -1 }),
        deliveryXhr: null,
        clickAndCollectXhr: null,
        sku: null,
        quoteLoading: ko.observable(false),
        suburbUpdated: ko.observable(false),
        selectedStoreUpdate: ko.observable(false),
        selectedStore: selectedStore.getStore(),
        selectedSuburb: selectedLocation.getSuburb(),
        selectedSuburbText: ko.observable,
        selectedStoreText: ko.observable,
        storeTextInput: ko.observable(''),
        storesModal: null,
        loadingStores: ko.observable(false),
        showUseMyCurrentLocation: useCurrentLocation.getValue(),
        suburb: ko.observable(null),
        suburbs: ko.observableArray(),
        estimateSuburbs: ko.observableArray(),
        stores: ko.observableArray(),
        postcode: ko.observable(null),
        estimatedQuote: ko.observable(),
        fulfilmentData: customerData.get('fulfilment-data'),
        isDangerousItem: ko.observable(window.isDangerousItem),
        freeShipping: ko.observable(window.freeShipping),
        freeShippingIcon: ko.observable(window.freeShippingIcon),
        regex: new RegExp('^[a-zA-Z0-9\\-\\s]+$'),
        _selectors: {
            _popup: '#change-store-modal',
            _popup_location: '#change-location-modal',
            _postcode: '#change-store-postcode-input',
            _postcode_location: '#change-location-postcode-input',
        },

        /**
         * Initializes observable properties of instance
         *
         * @returns {Object} Chainable.
         */
        initObservable: function () {
            this._super().observe([
                'isLoading',
            ]);

            return this;
        },

        initialize: function () {
            this._super();
            var _self = this;

            _self.sku = jQuery('.product-info-stock-sku .sku .value').html();

            _self._populateLocationData();

            if (_self.selectedSuburb().postcode) {
                _self._updateDeliveryMessage(_self.selectedSuburb().postcode);
            }

            if (_self.selectedStore().zipcode) {
                _self._updateClickAndCollectMessage(
                    _self.selectedStore().zipcode
                );
            }

            selectedLocation.suburb.subscribe(function (suburb) {
                if (suburb.postcode) {
                    var message = {
                        code: 0,
                        message: '',
                    };
                    _self.deliveryMessage(message);
                    _self._updateDeliveryMessage(suburb.postcode);
                }
            });

            selectedStore.store.subscribe(function (store) {
                if (store && store.stockMessage) {
                    _self.clickAndCollectMessage(store.stockMessage);
                } else if (store) {
                    _self._updateClickAndCollectMessage(store.zipcode);
                }

            });
            _self.selectedSuburbText = ko.computed(function () {
                var fulfilmentData = this.fulfilmentData();
                if (
                    _self.suburbUpdated() &&
                    _self.selectedSuburb() &&
                    _self.selectedSuburb().postcode
                ) {
                    localStorage.setItem('fulfilmentLocationBox', 1);
                    return (
                        _self.selectedSuburb().postcode +
                        ', ' +
                        _self.selectedSuburb().city
                    );
                } else if (
                    fulfilmentData.location &&
                    'object' === typeof fulfilmentData.location &&
                    localStorage.getItem('fulfilmentLocationBox') == 1
                ) {
                    var data = fulfilmentData.location;
                    _self.populateLocationFromLocalStorage(data);
                    return data.postcode
                        ? data.postcode + ', ' + data.city
                        : '';
                } else {
                    localStorage.setItem('fulfilmentLocationBox', 0);
                    return '';
                }
            }, _self);

            _self.selectedStoreText = ko.computed(function () {
                var fulfilmentData = this.fulfilmentData();
                if (
                    _self.selectedStoreUpdate() &&
                    _self.selectedStore() &&
                    _self.selectedStore()?.store_name &&
                    _self.selectedStore()?.zipcode
                ) {
                    localStorage.setItem('fulfilmentStoreBox', 1);
                    return (
                        _self.selectedStore().zipcode +
                        ', ' +
                        _self.selectedStore().store_name
                    );
                } else if (
                    fulfilmentData.store &&
                    'object' === typeof fulfilmentData.store &&
                    localStorage.getItem('fulfilmentStoreBox') == 1 &&
                    fulfilmentData?.store?.zipcode &&
                    fulfilmentData?.store?.store_name
                ) {
                    var data = fulfilmentData.store;
                    _self.populateStoreFromLocalStorage(data);
                    return data.zipcode + ', ' + data.store_name;
                } else {
                    localStorage.setItem('fulfilmentStoreBox', 0);
                    return '';
                }
            }, _self);

            _self.quoteLoading.subscribe(function(loader) {
                _self.isLoading(loader);
            });

            return this;
        },

        /**
         * Populate component store & location properties with local storage data if available.
         * @return {void}
         */
        _populateLocationData: function () {
            var data = this.fulfilmentData();

            if (!data.store && !data.location) {
                customerData.invalidate(['fulfilment-data']);
                customerData.reload(['fulfilment-data'], true);
                return;
            }

            if (data.store && 'object' === typeof data.store) {
                selectedStore.setStore(data.store);
            }

            if (data.location && 'object' === typeof data.location) {
                selectedLocation.setSuburb(data.location);
            }
        },

        _getStockcheckUrl: function () {
            return url.build(
                'totaltools_storelocator/product/stockavailabilitymessage'
            );
        },

        resetDefaults: function () {
            this.suburbs?.removeAll();
            this.estimateSuburbs?.removeAll();
            this.stores?.removeAll();
        },

        /**
         * Creates a modal instances for stores popup
         *
         * @param {HTMLElement} elem
         */
        handleModalRender: function(elem) {
            this.storesModal = $(elem).modal({
                type: 'popup',
                responsive: true,
                buttons: [],
                closed: this.resetDefaults,
                innerScroll: true
            });
        },

        fetchStores: async function (suburb, postcode, event) {
            var _self = this;

            this.suburbs.removeAll();
            this.estimateSuburbs.removeAll();
            this.postcode(postcode);
            this.suburb(suburb);
            this.loadingStores(true);

            await stockcheck.fetchStores(_self.stores, postcode, true, 20);

            $(this.storesModal).modal('openModal');
            this.loadingStores(false);
        },

        populateLocationFromLocalStorage: function (data) {
            var _self = this;
            var message = {
                code: 0,
                message: '',
            };
            _self.deliveryMessage(message);
            _self.postcode(data.postcode);
            _self.suburbUpdated(true);
        },

        populateStoreFromLocalStorage: function (store) {
            var _self = this;
            _self.resetDefaults();
            selectedStore.setStore(store);
            _self.selectedStoreUpdate(true);
        },

        selectStore: function (store, event) {
            var _self = this;
            var location = selectedLocation.getSuburb()();

            $.when(stockcheck.changeStore(store)).done(function (data) {
                if (location?.postcode) {
                    _self.changeLocation(location);
                }
                _self.storesModal.modal('closeModal');
                _self.resetDefaults();
                selectedStore.setStore(store);
                _self.selectedStoreUpdate(true);
            });
        },

        getDeliveryCost: function () {
            var _self = this;
            var data = _self.selectedSuburb();
            var requestdata = {
                sku: _self.sku,
                storelocator_id: _self.selectedStore().storelocator_id,
                postcode: data.postcode,
                city: data.city,
                region: data.region,
                country_id: data.country_id,
            };

            _self.getQuote(requestdata);
        },

        changeLocation: function (data) {
            var _self = this;

            _self.isLoading(true);
            $.ajax({
                url: url.build('totaltools/checkout/changelocation'),
                type: 'POST',
                dataType: 'json',
                data: data,
                complete: function (xhr, status) {
                    // geolocation.setLocation(location);
                    // selectedLocation.setSuburb(location);
                    _self.resetDefaults();

                    var requestdata = {
                        sku: _self.sku,
                        storelocator_id: xhr.responseJSON.store_id,
                        postcode: data.postcode,
                        city: data.city,
                        region: data.region,
                        country_id: data.country_id,
                    };
                    _self.getQuote(requestdata);
                },
                always: function() {
                    _self.isLoading(false);
                }
            });
        },

        getQuote: function (data) {
            var _self = this;

            _self.quoteLoading(true);

            $.ajax({
                url: url.build(
                    'totaltools_storelocator/product/getquote'
                ),
                type: 'GET',
                dataType: 'json',
                data: data,
                complete: function (xhr, status) {
                    _self.estimatedQuote(xhr.responseJSON);
                    _self.quoteLoading(false);
                },
                always: function () {
                    _self.quoteLoading(false);
                }
            });
        },

        deliveryEstimate: function (data) {
            var _self = this;
            var message = {
                code: 0,
                message: '',
            };
            _self.deliveryMessage(message);
            _self.changeLocation(data);
            _self.postcode(data.postcode);
            _self.suburbUpdated(true);
            this.suburbs.removeAll();
        },

        fetchSuburbs: function (data, event) {
            event.preventDefault();

            var _self = this;
            var $postcode = $(event.currentTarget);
            var query = $postcode.val();
            _self.resetDefaults();

            if (this.regex.test(query) && event.keyCode != 17) {
                $postcode.addClass('avs-active').parent().addClass('loading');
                fetchSuburbs.fetchSuburbsByPostcode(
                    query,
                    $postcode,
                    _self.suburbs
                );
            }
        },

        estimateFetchSuburbs: function (comp, event) {
            event.preventDefault();

            /**
             * If a submit button is used to execute the method, get the postcode value
             * from the sibling input
             */
            if (event.currentTarget.tagName === 'BUTTON') {
                event.currentTarget = $(event.currentTarget).prev('input')
            }

            var that = this;
            var $postcode = $(event.currentTarget);
            var query = $postcode.val();

            that.resetDefaults();

            if (this.regex.test(query) && event.keyCode != 17) {
                $postcode.addClass('avs-active').parent().addClass('loading');
                fetchSuburbs.fetchSuburbsByPostcode(
                    query,
                    $postcode,
                    that.estimateSuburbs
                );
            }
        },

        highlightMatch: function (input, pattern) {
            var regex = new RegExp(pattern, 'gi');
            return input.replace(
                regex,
                '<span class="highlighted">' + pattern + '</span>'
            );
        },

        _updateDeliveryMessage: function (postcode) {
            var _self = this;
            if (_self.deliveryXhr && _self.deliveryXhr.readystate != 4) {
                _self.deliveryXhr.abort();
            }
            var configurableSku = $.trim(
                $('.product-info-stock-sku .configurable-sku .value').html()
            );
            if (configurableSku != '' && configurableSku != 'undefined') {
                _self.sku = configurableSku;
            }

            _self.isLoading(true);

            _self.deliveryXhr = $.ajax({
                url: '/totaltools_storelocator/product/stockavailabilitymessage',
                data: {
                    sku: _self.sku,
                    storelocator_id:
                        selectedStore.store() &&
                        selectedStore.store().storelocator_id
                            ? selectedStore.store().storelocator_id
                            : 0,
                    method: 'delivery',
                    postcode: postcode,
                },
                type: 'GET',
                dataType: 'json',
            })
                .done(function (data) {
                    // API End point returns an array with key set to item id.
                    $.each(data, function (k, message) {
                        _self.deliveryMessage(message);
                    });
                })
                .fail(function (error) {
                    console.error('Error during AJAX request:', error);
                }).always(function () {
                    _self.isLoading(false);
                });
        },

        _updateClickAndCollectMessage: function (postcode) {
            var _self = this;
            if (
                _self.clickAndCollectXhr &&
                _self.clickAndCollectXhr.readystate != 4
            ) {
                _self.clickAndCollectXhr.abort();
            }
            var configurableSku = $.trim(
                $('.product-info-stock-sku .configurable-sku .value').html()
            );
            if (configurableSku != '' && configurableSku != 'undefined') {
                _self.sku = configurableSku;
            }

            _self.isLoading(true);

            _self.clickAndCollectXhr = $.ajax({
                url: '/totaltools_storelocator/product/stockavailabilitymessage',
                data: {
                    sku: _self.sku,
                    storelocator_id:
                        selectedStore.store() &&
                        selectedStore.store().storelocator_id
                            ? selectedStore.store().storelocator_id
                            : 0,
                    method: 'shippitcc_shippitcc',
                    postcode: postcode,
                },
                type: 'post',
                dataType: 'json',
            })
                .done(function (data) {
                    // API End point returns an array with key set to item id.
                    $.each(data, function (k, message) {
                        _self.clickAndCollectMessage(message);
                    });

                })
                .fail(function (error) {
                    console.error('Error during AJAX request:', error);
                }) .always(function () {
                    setTimeout(function () {
                        _self.isLoading(false);
                    }, 1000); // 1 second delay
                });
        },

        clearSuburb: function () {
            $('#estimate-delivery-postcode').val('');
            $('#estimate-delivery-postcode').focus();
            this.suburbs.removeAll();
            this.selectedSuburb({});
        },

        clearSelectedStore: function () {
            $('#estimate-change-store-postcode-input').val('');
            $('#estimate-change-store-postcode-input').focus();
            this.estimateSuburbs.removeAll();
            this.stores.removeAll();
        },
    });
});
