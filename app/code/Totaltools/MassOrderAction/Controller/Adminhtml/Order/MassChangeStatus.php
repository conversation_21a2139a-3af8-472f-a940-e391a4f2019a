<?php
namespace Totaltools\MassOrderAction\Controller\Adminhtml\Order;

use Magento\Backend\App\Action;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\Model\Auth\Session;

class MassChangeStatus extends Action
{
    public const STATE_NEW = 'new';

    public const STATUS_PENDING = 'pending';

    protected $collectionFactory;

    protected $authSession;

    public function __construct(
        Action\Context $context,
        CollectionFactory $collectionFactory,
        Session $authSession
    ) {
        parent::__construct($context);
        $this->collectionFactory = $collectionFactory;
        $this->authSession = $authSession;
    }

    public function execute()
    {
        $orderIds = $this->getRequest()->getParam('selected');
        $status = $this->getRequest()->getParam('status');
        $state = ($status == self::STATUS_PENDING) ? self::STATE_NEW : $status;
        if (!is_array($orderIds) || !$status) {
            $this->messageManager->addErrorMessage(__('Please select orders and status.'));
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setPath('sales/order/index');
        }

        $adminUser = $this->authSession->getUser();
        $adminName = $adminUser ? $adminUser->getUserName() : 'Unknown User';

        $collection = $this->collectionFactory->create()->addFieldToFilter('entity_id', ['in' => $orderIds]);
        $updated = 0;
        foreach ($collection as $order) {
            $order->setStatus($status);
            $order->setState($state);
            $order->addStatusHistoryComment(
                __('Order status changed to %1 via mass action by %2.', $status, $adminName)
            );
            $order->save();
            $updated++;
        }

        $this->messageManager->addSuccessMessage(__('%1 order(s) status changed.', $updated));
        return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setPath('sales/order/index');
    }

    /**
     * Check ACL permission for mass order actions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Totaltools_MassOrderAction::actions');
    }
}