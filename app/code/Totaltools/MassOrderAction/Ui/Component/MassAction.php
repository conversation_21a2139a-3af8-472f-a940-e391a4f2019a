<?php

namespace Totaltools\MassOrderAction\Ui\Component;

use Magento\Framework\AuthorizationInterface;

/**
 * Class MassAction
 */
class MassAction extends \Magento\Ui\Component\MassAction
{
    /**
     * @var AuthorizationInterface
     */
    protected $authorization;

    /**
     * @param \Magento\Framework\View\Element\UiComponent\ContextInterface $context
     * @param AuthorizationInterface $authorization
     * @param array $components
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\UiComponent\ContextInterface $context,
        AuthorizationInterface $authorization,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $components, $data);
        $this->authorization = $authorization;
    }

    /**
     * {@inheritdoc}
     */
    public function prepare()
    {
        parent::prepare();
        $config = $this->getData('config');
        if (isset($config['actions'])) {
            $this->applyPermissionFiltering($config['actions']);
            $this->applyCustomSort($config['actions']);
        }
        $this->setData('config', $config);
    }

    /**
     * Filter actions based on user permissions
     *
     * @param array $actions
     * @return array
     */
    protected function applyPermissionFiltering(array &$actions)
    {
        // Check if user has permission for change status action
        if (!$this->authorization->isAllowed('Totaltools_MassOrderAction::actions')) {
            // Remove change_status action if user doesn't have permission
            $actions = array_filter($actions, function($action) {
                return $action['type'] !== 'change_status';
            });
        }
        
        return $actions;
    }

    /**
     * Sort actions and apply custom ordering
     *
     * @param array $actions
     * @return array
     */
    protected function applyCustomSort(array &$actions)
    {
        // Remove 'add_order_to_archive' action
        $actions = array_filter($actions, function($action) {
            return $action['type'] !== 'add_order_to_archive';
        });

        // Find and extract 'change_status' action
        $changeStatusAction = null;
        foreach ($actions as $key => $action) {
            if ($action['type'] === 'change_status') {
                $changeStatusAction = $action;
                unset($actions[$key]);
                break;
            }
        }

        // Re-index array
        $actions = array_values($actions);

        // Insert 'change_status' at position 4
        if ($changeStatusAction) {
            array_splice($actions, 3, 0, [$changeStatusAction]);
        }

        return $actions;
    }
}
