<?php
declare(strict_types=1);

namespace Totaltools\MassOrderAction\Ui\Component\MassAction\Status;

use Magento\Framework\Phrase;
use Magento\Framework\UrlInterface;
use Magento\Sales\Model\Order\Config;
use Magento\Framework\AuthorizationInterface;

class Options implements \JsonSerializable
{
    public const STATUS_PENDING = 'pending';

    /**
     * @var array
     */
    protected $options;

    /**
     * @var Config
     */
    protected $orderConfig;

    /**
     * @var UrlInterface
     */
    protected $urlBuilder;

    /**
     * @var AuthorizationInterface
     */
    protected $authorization;

    /**
     * @param Config $orderConfig
     * @param UrlInterface $urlBuilder
     * @param AuthorizationInterface $authorization
     */
    public function __construct(
        Config $orderConfig,
        UrlInterface $urlBuilder,
        AuthorizationInterface $authorization
    ) {
        $this->orderConfig = $orderConfig;
        $this->urlBuilder = $urlBuilder;
        $this->authorization = $authorization;
    }

    /**
     * Get action options
     *
     * @return array
     */
    public function jsonSerialize(): array
    {
        if ($this->options === null) {
            // Check if user has permission for change status action
            if (!$this->authorization->isAllowed('Totaltools_MassOrderAction::actions')) {
                $this->options = [];
                return $this->options;
            }

            $options = [];
            $statuses = $this->orderConfig->getStatuses();
            $states = $this->orderConfig->getStates();

            foreach ($statuses as $code => $label) {
                if (!array_key_exists($code, $states) && $code !== self::STATUS_PENDING) {
                    unset($statuses[$code]);
                }
            }
            
            foreach ($statuses as $code => $label) {
                $options[] = [
                    'type' => 'status_' . $code,
                    'label' => $label,
                    'url' => $this->urlBuilder->getUrl(
                        'massorderaction/order/masschangestatus',
                        ['status' => $code]
                    ),
                    'confirm' => [
                        'title' => __('Are you sure?'),
                        'message' => __('Do you really want to change the status of selected orders?')
                    ]
                ];
            }
            
            $this->options = $options;
        }
        
        return $this->options;
    }
}