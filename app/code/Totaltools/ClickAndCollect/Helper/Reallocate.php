<?php

/**
 * <AUTHOR> Internet
 * @package    Totaltools_ClickAndCollect
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\ClickAndCollect\Helper;

use Exception;
use Magento\Framework\App\Area;
use Magento\Framework\DataObject;
use Psr\Log\LoggerInterface;
use Magento\Framework\Debug;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Sales\Model\OrderRepository;
use Magento\Sales\Model\Order\Address\Renderer;
use Magento\Sales\Model\Order;
use Magento\Payment\Helper\Data;
use Magento\Sales\Model\Order\Email\Container\Template;
use Totaltools\Shippit\Helper\SyncOrder as SyncOrderHelper;
use Shippit\Shipping\Model\Config\Source\Shippit\Sync\Order\SendAllOrders;
use Shippit\Shipping\Model\Config\Source\Shippit\Sync\Order\Mode;
use Totaltools\Storelocator\Model\StoreRepository;
use Totaltools\Storelocator\Model\StoreAdminEmail;
use Totaltools\Storelocator\Model\StoreShippitService;
use Magento\Framework\App\Helper\Context;
use Magento\Sales\Model\Order\Address;
use Magento\Customer\Model\Address\Config as AddressConfig;
use Totaltools\Pronto\Helper\Data as ProntoHelper;

/**
 * Class Reallocate
 * @package Totaltools\ClickAndCollect\Helper
 */
class Reallocate extends \Magento\Framework\App\Helper\AbstractHelper
{
    const XML_ENABLE_REALLOCATE = 'shippit/ccsettings/enable_reallocate';

    /**
     * Click and collect method name
     */
    const STORE_PICKUP_METHOD = 'shippitcc_shippitcc';

    /**
     * @var orderRepository
     */
    private $orderRepository;

    /**
     * @var storeRepository
     */
    private $storeRepository;

    /**
     * @var transportBuilder
     */
    private $transportBuilder;

    /**
     * @var addressRenderer
     */
    private $addressRenderer;

    /**
     * @var paymentHelper
     */
    private $paymentHelper;

    /**
     * @var templateContainer
     */
    private $templateContainer;

    /**
     * @var StoreAdminEmail
     */
    private $storeAdminEmail;

    /**
     * @var StoreShippitService
     */
    private $shippitService;

    /**
     * @var SyncOrderHelper
     */
    private $syncOrderHelper;
    /**
     * @var \Shippit\Shipping\Helper\Sync\Order
     */
    private $shippitOrdersyncHelper;
    /**
     * @var \Shippit\Shipping\Helper\Data
     */
    private $shippitHelper;

    /**
     * @var \Shippit\Shipping\Logger\Logger
     */
    private $shippitLogger;

    /**
     * @var \Shippit\Shipping\Api\Request\SyncOrderInterface
     */
    private $requestSyncOrder;

    /**
     * @var \Shippit\Shipping\Model\Api\Order
     */
    private $apiOrder;

    /**
     * @var \Shippit\Shipping\Api\Data\SyncOrderInterface
     */
    private $syncOrder;

    /**
     * @var \Shippit\Shipping\Model\Shippit
     */
    private $shippitModel;

    /**
     * @var \Magento\Store\Model\App\Emulation
     */
    private $appEmulation;

    /**
     * @var result
     */
    private $result;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var Address
     */
    protected $address;

    /**
     * @var AddressConfig
     */
    protected $addressConfig;

    /**
     * @var ProntoHelper
     */
    protected $prontoHelper;

    /**
     * SetEnvironmentCommand constructor.
     *
     * @param OrderRepository $orderRepository
     * @param StoreRepository $storeRepository
     * @param Renderer $addressRenderer
     * @param Data $paymentHelper
     * @param TransportBuilder $transportBuilder
     * @param Template $templateContainer
     * @param StoreAdminEmail $storeAdminEmail
     * @param StoreShippitService $shippitService
     * @param LoggerInterface $logger
     * @param Address $address
     * @param AddressConfig $addressConfig
     * @param ProntoHelper  $prontoHelper
     */
    public function __construct(
        OrderRepository $orderRepository,
        StoreRepository $storeRepository,
        Renderer $addressRenderer,
        Data $paymentHelper,
        TransportBuilder $transportBuilder,
        Template $templateContainer,
        StoreAdminEmail $storeAdminEmail,
        StoreShippitService $shippitService,
        SyncOrderHelper $syncOrderHelper,
        \Shippit\Shipping\Helper\Sync\Order $shippitOrdersyncHelper,
        \Shippit\Shipping\Helper\Data $shippitHelper,
        \Shippit\Shipping\Api\Request\SyncOrderInterface $requestSyncOrder,
        \Shippit\Shipping\Model\Api\Order $apiOrder,
        \Shippit\Shipping\Api\Data\SyncOrderInterface $syncOrder,
        \Shippit\Shipping\Logger\Logger $shippitLogger,
        \Shippit\Shipping\Model\Shippit $shippitModel,
        \Magento\Store\Model\App\Emulation $appEmulation,
        LoggerInterface $logger,
        Address $address,
        AddressConfig $addressConfig,
        ProntoHelper $prontoHelper,
        Context $context
    ) {
        $this->orderRepository = $orderRepository;
        $this->storeRepository = $storeRepository;
        $this->addressRenderer = $addressRenderer;
        $this->paymentHelper = $paymentHelper;
        $this->transportBuilder = $transportBuilder;
        $this->templateContainer = $templateContainer;
        $this->storeAdminEmail = $storeAdminEmail;
        $this->shippitService = $shippitService;
        $this->syncOrderHelper = $syncOrderHelper;
        $this->shippitHelper = $shippitHelper;
        $this->shippitOrdersyncHelper = $shippitOrdersyncHelper;
        $this->requestSyncOrder = $requestSyncOrder;
        $this->apiOrder = $apiOrder;
        $this->syncOrder = $syncOrder;
        $this->shippitLogger = $shippitLogger;
        $this->shippitModel = $shippitModel;
        $this->appEmulation = $appEmulation;
        $this->logger = $logger;
        $this->address = $address;
        $this->addressConfig = $addressConfig;
        $this->prontoHelper = $prontoHelper;
        $this->result = array(
            'error' => array(),
            'success' => array()
        );
        parent::__construct($context);
    }

    /**
     * Update an Orders Store Location.
     *
     * @param int $order_id
     * @param int $storelocator_id
     *
     * @return array
     * @throws InputException
     * @throws NoSuchEntityException
     */
    public function updateOrderStoreLocation($order_id = null, $storelocator_id = null)
    {
        //validate parameters and the store and order object instantiations first
        //parameters
        if ($order_id === null || $storelocator_id === null) {
            $this->result['error'][] = 'Make sure the store location ID and order ID are both present.';
            return $this->result;
        }
        //load order and store objects from parameters passed
        $order = $this->orderRepository->get($order_id);
        $store = $this->storeRepository->getById($storelocator_id);
        if (!$order->getId() || !$store->getId()) {
            $this->result['error'][] = 'Make sure the store location ID and order ID are both valid.';
            return $this->result;
        }
        //are we trying to assign the same store again?
        if ($storelocator_id === $order->getStorelocatorId()) {
            $this->result['error'][] = 'Store ID is already set. Attempting to assign the same Store Location ID to the Order\'s Store Location ID';
            return $this->result;
        }
        //do we have an order shipping address?
        $orderShippingAddress = $order->getShippingAddress();
        if (!$orderShippingAddress->getId()) {
            $this->result['error'][] = 'The Order\'s Shipping Address is not available. This should not happen.';
            return $this->result;
        }
        //does the original store exist?
        if ($order->getStorelocatorId()) {
            $originalStore = $this->storeRepository->getById($order->getStorelocatorId());
        } elseif ($order->getWarehouse()) {
            $originalStore = $this->storeRepository->getByErpId($order->getWarehouse());
        } elseif ($order->getStore()) {
            $originalStore = $this->storeRepository->getStore('erp_code', $order->getStore());
        } else {
            $originalStore = new \Magento\Framework\DataObject(['store_name' => 'N/A']);
        }

        try {
            /**
             * try to cancel existing order in shippit first
             */
            $canceled = $this->_cancelOrderInShippit($order);
            if (!$canceled) {
                array_unshift($this->result['error'], 'Can not update the order location, see the details below:');
                return $this->result;
            }

            /**
             * Update order status to let Dabus fetch and fetch it again
             */
            $order->setState(Order::STATE_PROCESSING)
                ->setStatus(SyncOrderHelper::ORDER_RELLOCATING_STATUS);
            //logic for shipping method variance
            $shippingMethod = $order->getShippingMethod();

            $customerLoyaltyData = '';
            if($order->getId()) {
                $guestCustomer = $order->getCustomerIsGuest();
                if(!$guestCustomer) {
                    $customerId = $order->getCustomerId();
                    $customerLoyaltyData = $this->prontoHelper->getCustomerLoyaltyData($customerId);
                }
            }

            //Click and Collect
            if ($shippingMethod && $shippingMethod === 'shippitcc_shippitcc') {
                /*
                * Do the shipping and order updates:
                * sales_order Table:
                *   warehouse, store, storelocator_id
                * sales_order_address Table:
                *   region_id, region, postcode, lastname, street, city, telephone, country_id
                */
                //order table
                $order->setWarehouse($store->getErpId());
                $order->setStore($store->getErpCode());
                $order->setStorelocatorId($store->getStorelocatorId());

                //add order comment
                $historyItem = $order->addStatusHistoryComment('Store Location was changed from "' . $originalStore->getStoreName() . '" to "' . $store->getStoreName() . '". Email has been sent to "' . $store->getStoreAdminEmail() . '".');
                $historyItem->setIsCustomerNotified(1)->save();
                //do save
                $order->save();
               

                //send email to the new reallocation store
                $this->sendReallocationStoreEmail($store, $order, $originalStore);
                if ($originalStore->getId()) {
                    //send email to customer
                    $this->sendCustomerEmail($order, $store, $originalStore, $customerLoyaltyData);
                    //send email to original store
                    $this->sendOriginalStoreEmail($order, $store, $originalStore, $customerLoyaltyData);
                }
                //send to shippit queue?
                $this->shippitService->sendOrder($order, $store);
                //result
                $this->result['success'][] = 'Store Location was changed from "' . $originalStore->getStoreName() . '" to "' . $store->getStoreName() . '".';
                //Delivery
            } else {
                //order table
                $order->setWarehouse($store->getErpId());
                $order->setStore($store->getErpCode());
                $order->setStorelocatorId($store->getStorelocatorId());
                //add order comment
                $order->setIsCustomerNotified(false);
                $historyItem = $order->addStatusHistoryComment('Store Location was changed from "' . $originalStore->getStoreName() . '" to "' . $store->getStoreName() . '". Email has been sent to "' . $store->getStoreAdminEmail() . '".');
                $historyItem->setIsCustomerNotified(1)->save();
                //do save
                $order->save();
                //send email to the new reallocation store
                $this->sendReallocationStoreEmail($store, $order, $originalStore);
                if ($originalStore->getId()) {
                    //send email to original store
                    $this->sendOriginalStoreEmail($order, $store, $originalStore, $customerLoyaltyData);
                }
                //send to shippit queue?
                $this->shippitService->sendOrder($order, $store);
                //result
                $this->result['success'][] = 'Store Location was changed from "' . $originalStore->getStoreName() . '" to "' . $store->getStoreName() . '".';
            }

            $billingAddress = $order->getBillingAddress();
            $shippingMethod = $order->getShippingMethod();

            if (strpos(strtolower($billingAddress->getFirstname()), 'store') !== false) {
                $this->logger->critical(
                    'Store as billing address detected for order #' . $order->getIncrementId() . ' with shipping method: ' . $shippingMethod,
                    ['backtrace' => Debug::backtrace(true, false)]
                );
            }

        } catch (Exception $exception) {
            $this->result['error'][] = 'An error has occurred while attempting to change Store Locations. Message: ' . $exception->getMessage();
        }

        return $this->result;
    }

    /**
     * Check if order is pushed to Shippit, cancel it, then add it again to Shippit
     * Confirmed: this no need to implement Store Locator API Type
     * @param Order $order
     * @deprecated this is handled by  \Totaltools\Shippit\Helper\SyncOrder::checkAndCancelOrderInShippit
     */
    private function updateToShippit(Order $order)
    {
        // start Store Emulation
        $this->appEmulation->startEnvironmentEmulation(
            $order->getStoreId(),
            Area::AREA_ADMINHTML
        );
        /**
         * then try to add new request to queue
         */
        try {
            $shippingMethod = $this->shippitOrdersyncHelper->getShippitShippingMethod($order->getShippingMethod());
            $this->shippitModel->addOrder(
                $order,
                [], // all items
                $shippingMethod,
                $this->shippitHelper->getApiKey(),
                $this->shippitOrdersyncHelper->getMode(),
                true
            );
        } catch (\Exception $e) {
            $this->shippitLogger->addError($e->getMessage());
        }
        // Stop Store Emulation
        $this->_appEmulation->stopEnvironmentEmulation();
    }

    /**
     * @param $order
     * @return bool
     */
    private function _cancelOrderInShippit($order)
    {
        try {
            $status = true;
            $result = $this->syncOrderHelper->checkAndCancelOrderInShippit($order);
            foreach ($result as $id => $details) {
                if (@$details['status']) {
                    $this->result['success'][] = "$id: " . @$details['message'];
                } else {
                    $this->result['error'][] = "$id: " . @$details['message'];
                    $status = false;
                }
            }
        } catch (\Exception $e) {
            $this->shippitLogger->addError('API - Order Cancel Request Failed - ' . $e->getMessage());
            $this->result['error'][] = $e->getMessage();
            $status = false;
        }
        return $status;
    }

    /**
     * send email to Reallocation Store.
     * @param $store
     * @param $order
     */
    private function sendReallocationStoreEmail($store, $order, $originalStore)
    {
        

        if (!empty($originalStore) && $originalStore->getId()) {
            $options = [
                'reallocate' => true,
                'originalStore'  => $originalStore->getStoreName()
            ];
        } else {
            $options = [
                'reallocate' => false,
                'originalStore'  => null
            ];
        }

        try {
            $this->storeAdminEmail->sendMail($store, $order, $options);
            $this->result['success'][] = 'Email has been sent to the Reallocation Store: "' . $store->getStoreName() . '".';
            $historyItem = $order->addStatusHistoryComment('Email has been sent to the Reallocation Store "' . $store->getStoreAdminEmail() . '".');
            $historyItem->setIsCustomerNotified(1)->save();
        } catch (Exception $exception) {
            $this->result['error'][] = 'An error has occurred while attempting to send the Reallocation Store email. Message: ' . $exception->getMessage();
        }
    }

    /**
     * send email to Original Store.
     * @param $order
     * @param $reallocatedStore
     * @param $originalStore
     */
    private function sendOriginalStoreEmail($order, $reallocatedStore, $originalStore, $customerLoyaltyData)
    {
        if ($order->getShippitAuthorityToLeave()) {
            $authToLeaveText = 'Yes';
        } else {
            $authToLeaveText = 'No';
        } 
        $shippingMethod = $order->getShippingMethod();
        $isStorePickup = (bool) ($shippingMethod == self::STORE_PICKUP_METHOD);
        $isAuthorityToLeaveActive = (bool) $this->isAuthorityToLeaveActive();
        $sender = $this->getReallocateStoreEmailSender();
        try {
            $recipient = [
                'email' => $originalStore->getStoreAdminEmail(),
                'name' => $originalStore->getStoreName(),
            ];
            $transport = [
                'order' => $order,
                'order_id' => $order->getId(),
                'reallocatedStore' => $reallocatedStore,
                'originalStore' => $originalStore,
                'billing' => $order->getBillingAddress(),
                'payment_html' => $this->getPaymentHtml($order),
                'store' => $order->getStore(),
                'created_at_formatted' => $order->getCreatedAtFormatted(10),
                'formattedShippingAddress' => $this->getFormattedShippingAddress($order),
                'formattedBillingAddress' => $this->getFormattedBillingAddress($order),
                'customerLoyaltyData' => $customerLoyaltyData,
                'authToLeaveText' => $authToLeaveText,
                'authToLeaveActive' => (!$isStorePickup && $isAuthorityToLeaveActive) ? true : false,
                'is_store_pickup' => $isStorePickup,
                'order_data' => [
                    'customer_name' => $order->getCustomerName(),
                    'is_not_virtual' => $order->getIsNotVirtual(),
                    'email_customer_note' => $order->getEmailCustomerNote(),
                    'frontend_status_label' => $order->getFrontendStatusLabel()
                ]
            ];
            $transportObject = new DataObject($transport);
            $this->templateContainer->setTemplateVars($transportObject->getData());
            $mailer = $this->transportBuilder
                ->setTemplateIdentifier($this->getReallocateStoreEmailTemplate())
                ->setTemplateOptions([
                    'area' => Area::AREA_FRONTEND,
                    'store' => $order->getStoreId(),
                ])
                ->setTemplateVars($transport)
                ->setFrom($sender)
                ->addTo($recipient['email'], $recipient['name']);
            $transport = $mailer->getTransport();
            $transport->sendMessage();
            $this->result['success'][] = 'Email has been sent to the Original Store: "' . $originalStore->getStoreName() . '".';
            $historyItem = $order->addStatusHistoryComment('Email has been sent to the Original Store "' . $originalStore->getStoreAdminEmail() . '".');
            $historyItem->setIsCustomerNotified(1)->save();
        } catch (Exception $exception) {
            $this->result['error'][] = 'An error has occurred while attempting to send the Original Store email. Message: ' . $exception->getMessage();
        }
    }

    /**
     * send email to Customer.
     * @param $order
     * @param $reallocatedStore
     * @param $originalStore
     */
    private function sendCustomerEmail($order, $reallocatedStore, $originalStore, $customerLoyaltyData)
    {
        $this->logger->debug('sendCustomerEmail');
        $sender = $this->getReallocateCustomerEmailSender();
        try {
            $recipient = [
                'email' => $order->getCustomerEmail(),
                'name' => $order->getBillingAddress()->getFirstname() . ' ' . $order->getBillingAddress()->getLastname(),
            ];
            $transport = [
                'order' => $order,
                'order_id' => $order->getId(),
                'reallocatedStore' => $reallocatedStore,
                'originalStore' => $originalStore,
                'billing' => $order->getBillingAddress(),
                'payment_html' => $this->getPaymentHtml($order),
                'store' => $order->getStore(),
                'created_at_formatted' => $order->getCreatedAtFormatted(10),
                'formattedShippingAddress' => $this->getFormattedStoreAddress($order, $reallocatedStore),
                'formattedBillingAddress' => $this->getFormattedBillingAddress($order),
                'customerLoyaltyData' => $customerLoyaltyData,
                'order_data' => [
                    'customer_name' => $order->getCustomerName(),
                    'is_not_virtual' => $order->getIsNotVirtual(),
                    'email_customer_note' => $order->getEmailCustomerNote(),
                    'frontend_status_label' => $order->getFrontendStatusLabel()
                ]
            ];

            $transportObject = new DataObject($transport);
            $this->templateContainer->setTemplateVars($transportObject->getData());
            $mailer = $this->transportBuilder
                ->setTemplateIdentifier($this->getReallocateCustomerEmailTemplate())
                ->setTemplateOptions([
                    'area' => Area::AREA_FRONTEND,
                    'store' => $order->getStoreId(),
                ])
                ->setTemplateVars($transport)
                ->setFrom($sender)
                ->addTo($recipient['email'], $recipient['name']);
            $transport = $mailer->getTransport();
            $transport->sendMessage();
            $this->result['success'][] = 'Email has been sent to Customer "' . $order->getBillingAddress()->getFirstname() . ' ' . $order->getBillingAddress()->getLastname() . '".';
            $historyItem = $order->addStatusHistoryComment('Email has been sent to Customer "' . $order->getBillingAddress()->getFirstname() . ' ' . $order->getBillingAddress()->getLastname() . '".');
            $historyItem->setIsCustomerNotified(1)->save();
        } catch (Exception $exception) {
            $this->result['error'][] = 'An error has occurred while attempting to send the Customer email. Message: ' . $exception->getMessage();
            $this->logger->error('An error has occurred while attempting to send the Customer email. Message: ' . $exception->getMessage());
        }
    }

    /**
     * @param $order
     * @return mixed|null
     */
    private function getFormattedShippingAddress($order)
    {
        return $order->getIsVirtual()
            ? null
            : $this->addressRenderer->format($order->getShippingAddress(), 'html');
    }

    /**
     * @param $order
     * @return mixed|null
     */
    private function getFormattedStoreAddress($order, $reallocatedStore)
    {
        $this->address->setRegionId($reallocatedStore->getStateId());
        $this->address->setRegion($reallocatedStore->getState());
        $this->address->setPostcode($reallocatedStore->getZipcode());
        $this->address->setFirstname($reallocatedStore->getStoreName());
        $this->address->setLastname('');
        $this->address->setStreet($reallocatedStore->getAddress());
        $this->address->setCity($reallocatedStore->getCity());
        $this->address->setTelephone($reallocatedStore->getPhone());
        $this->address->setCountryId($reallocatedStore->getCountryId());
        $this->addressConfig->setStore($order->getStoreId());
        $formatType = $this->addressConfig->getFormatByCode('html');
        return $order->getIsVirtual()
            ? null
            : $formatType->getRenderer()->renderArray($this->address);
    }

    /**
     * @param $order
     * @return mixed
     */
    private function getFormattedBillingAddress($order)
    {
        return $this->addressRenderer->format($order->getBillingAddress(), 'html');
    }

    /**
     * @param $order
     * @return string
     * @throws Exception
     */
    private function getPaymentHtml($order)
    {
        return $this->paymentHelper->getInfoBlockHtml(
            $order->getPayment(),
            $order->getStore()->getStoreId()
        );
    }

    /**
     * @param null $store
     * @return bool
     */
    public function isEnableReallocating($store = null)
    {
        return (bool) $this->scopeConfig->getValue(self::XML_ENABLE_REALLOCATE, \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $store);
    }

    /**
     * Reallocate is available for B2C orders only
     * @param Order $order
     * @return bool
     */
    public function isOrderAvailableForReallocating(\Magento\Sales\Model\Order $order)
    {
        $isNotB2b = true;
        if ($order->getExtensionAttributes()->getCompanyOrderAttributes()) {
            $isNotB2b = $order->getExtensionAttributes()->getCompanyOrderAttributes()->getCompanyId() ? false : true;
        }
        return $this->isEnableReallocating($order->getStoreId()) && $isNotB2b;
    }

    /**
     * @return mixed
     */
    public function getReallocateStoreEmailSender()
    {
        return $this->scopeConfig->getValue('storelocator/send_mail/reallocation_store_email_sender');
    }

    /**
     * @return mixed
     */
    public function getReallocateCustomerEmailSender()
    {
        return $this->scopeConfig->getValue('storelocator/send_mail/reallocation_customer_email_sender');
    }

    /**
     * @return mixed
     */
    public function getReallocateStoreEmailTemplate()
    {
        return $this->scopeConfig->getValue('storelocator/send_mail/original_store_reallocate');
    }

    /**
     * @return mixed
     */
    public function getReallocateCustomerEmailTemplate()
    {
        return $this->scopeConfig->getValue('storelocator/send_mail/customer_order_reallocate');
    }

    /**
     * @return mixed
     */
    public function isAuthorityToLeaveActive()
    {
        return $this->scopeConfig->getValue('checkout/total_tools/authority_leave_active');
    }
}
