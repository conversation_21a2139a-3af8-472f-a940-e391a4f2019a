import queryString from 'query-string';
import { FACET_TYPE_PRICE } from '../constants';
import { updatePageTitle, updatePageBreadCrumb } from '../utils';

export const getUrlParams = () => {
  const params = queryString.parse(window.location.search);
  if (params['category-filter'] && params['category-filter'].includes('>')) {
    params['category-filter'] = params['category-filter'].split('>');
  }
  const filterParams = params['filter'];
  if (filterParams) {
    const key = 'filter';
    if (typeof filterParams == 'string') {
      if (filterParams.includes(' OR ')) {
        const facet = filterParams.split(' OR ');
        params[key] = formatFacetsArray(facet);
      } else {
        const facet = filterParams.split(':');
        params[key] = { [facet[0]]: [removeStartEndStr(facet[1])] };
      }
    } else {
      if (Array.isArray(filterParams)) {
        const facets = formatFacetsArray(filterParams);
        params[key] = facets;
      }
    }
  }

  return params;
};

export const isValidate = (query) => {
  const tagRegex = /<[^>]*?>|<\/[^>]+>|<[^>]*$|^[^<]*>/g;
  const tags = query.match(tagRegex);
  if (tags) {
    return false;
  }
  return true;
};

export const sanitizedQuery = (html) => {
  const tagAndTextRegex = /<[^>]*?>[^<]*|<\/[^>]+>|<[^>]*$|>[^<]*/g;
  const sanitizedText = html.replace(tagAndTextRegex, '');
  return sanitizedText.trim() === '' ? '' : sanitizedText.trim();
};

export const getUrlParam = (name) => {
  name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
  const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
  const regexResult = regex.exec(window.location.search)?.[1];
  const query = regexResult ? decodeURIComponent(regexResult) : '';
  let result = '';
  if (query) {
    if (isValidate(query)) {
      result = query ? decodeURIComponent(query.replace(/\+/g, ' ')) : '';
    } else {
      const sanitizedQueryValue = sanitizedQuery(query);
      const url = new URL(window.location.href);
      const params = new URLSearchParams(url.search);
      params.set(name, sanitizedQueryValue || '');
      window.history.replaceState(null, '', `${url.pathname}?${params}`);
      updatePageTitle(sanitizedQueryValue);
      updatePageBreadCrumb(sanitizedQueryValue, true);
      result = sanitizedQueryValue;
    }
  }
  return result;
};

export const setUrlParams = (params) => {
  const formattedParam =
    typeof params === 'object'
      ? queryString.stringify(params, { sort: (a, b) => a.indexOf('q') >= b.indexOf('q'), skipNull: true })
      : params;
  return window.history.pushState({}, window.document.title, '?' + formattedParam);
};

export const paramsToString = (params) => {
  if (typeof params !== 'object' || params === null) {
    return params;
  }
  const url = generateUnbxdSearchURL(params);
  return url;
};

export const generateUnbxdSearchURL = (params) => {
  const queryParams = { ...params };
  const unbxdConfig = window?.unbxdConfig;
  if (!unbxdConfig) return { apiUrl: '', queryString: '' };

  let baseUrl = unbxdConfig?.searchUrl;

  if (Array.isArray(queryParams['category-filter'])) {
    queryParams['category-filter'] = queryParams['category-filter'].join('>');
  }

  if (queryParams.filter) {
    const facetFilters = [];
    if (Array.isArray(queryParams.filter)) {
      queryParams.filter.forEach((filter) => facetFilters.push(`filter=${encodeURIComponent(filter)}`));
    } else {
      if (typeof queryParams.filter == 'object') {
        for (const [fieldName, fieldValues] of Object.entries(queryParams.filter)) {
          if (Array.isArray(fieldValues)) {
            const fieldFilter = fieldValues
              .map((value) => {
                if (fieldName === FACET_TYPE_PRICE) {
                  return `${fieldName}:${value}`;
                } else {
                  const escapedValue = String(value).replace(/"/g, '\\"');
                  return `${fieldName}:"${escapedValue}"`;
                }
              })
              .join(' OR ');
            facetFilters.push(fieldFilter);
          } else {
            facetFilters.push(`${fieldName}:${fieldValues}`);
          }
        }
      } else {
        if (typeof queryParams?.filter == 'string' && !!queryParams?.filter) {
          facetFilters.push(queryParams.filter);
        }
      }
    }
    const filteredFacetFilters = facetFilters.filter(Boolean);
    if (filteredFacetFilters.length) {
      queryParams.filter = filteredFacetFilters;
    } else {
      delete queryParams.filter; // Remove the 'filter' key if there are no filters to apply
    }
  }

  const queryString = Object.entries(queryParams)
    .flatMap(([key, value]) => {
      if (Array.isArray(value)) {
        const filteredValues = value.filter((item) => !!item);
        if (filteredValues.length) {
          return filteredValues.map((item) => `${encodeURIComponent(key)}=${encodeURIComponent(item)}`);
        }
        return [];
      }
      if (value && value !== null) {
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      }
    })
    .filter(Boolean)
    .join('&');

  const finalUrl = baseUrl + '&' + queryString;

  return { apiUrl: finalUrl, queryString };
};

export const getQueryFilters = () => {
  let params = getUrlParams(),
    result = {},
    key,
    regex = /((text|price)_)|(category-filter)|(filter)/gi;
  for (key in params) {
    if (key.match(regex)) {
      const paramVal = params[key];
      result[key] = typeof paramVal == 'string' && key == 'category-filter' ? [paramVal] : paramVal;
    }
  }
  return result;
};

const formatFacetsArray = (facets_, facetsObject) => {
  const facets = facetsObject || {};
  facets_.forEach((val) => {
    if (val.includes(' OR ')) {
      const splitedFacet = val.split(' OR ');
      splitedFacet.forEach((facet) => {
        const [facetName, facetValue] = facet.split(':');
        const value = removeStartEndStr(facetValue);
        facets[facetName] = facets[facetName] ? [...facets[facetName], value] : [value];
      });
    } else {
      const [facetName, facetValue] = val.split(':');
      const value = removeStartEndStr(facetValue);
      facets[facetName] = facets[facetName] ? [...facets[facetName], value] : [value];
    }
  });
  return facets;
};

const removeStartEndStr = (value, substringToRemove = '"') => {
  const updatedValue = value?.replace(new RegExp(`^${substringToRemove}|${substringToRemove}$`, 'g'), '');
  return updatedValue;
};