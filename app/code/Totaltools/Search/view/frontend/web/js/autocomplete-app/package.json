{"name": "unbxd-search-app", "version": "1.0.1", "description": "A search client for Unbxd search.", "author": "<PERSON><PERSON>", "scripts": {"update-dep": "ncu -u", "start": "webpack-dev-server --hot --mode development", "build": "webpack --config webpack.config.js"}, "dependencies": {"axios": "^1.6.2", "html-react-parser": "^5.2.5", "html-webpack-plugin": "^5.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-equalizer": "^1.3.0", "react-redux": "^8.1.1", "redux": "^4.2.1", "redux-thunk": "^2.4.2"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "babel-loader": "^9.1.3", "babel-plugin-transform-class-properties": "^6.24.1", "eslint": "^8.44.0", "eslint-plugin-react": "^7.32.2", "react-hot-loader": "^4.13.1", "redux-logger": "^3.0.6", "terser-webpack-plugin": "^5.3.9", "webpack": "^5.88.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "browserslist": "> 0.25%, not dead", "license": "ISC"}