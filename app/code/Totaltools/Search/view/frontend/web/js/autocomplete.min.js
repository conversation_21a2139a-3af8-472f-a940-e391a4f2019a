/*! For license information please see autocomplete.min.js.LICENSE.txt */
!function(){"use strict";var e,t={124:function(e,t,r){var n=r(294),o=r(745),a=r(998),i=r(894),l=r(791);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){A(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function A(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==c(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===c(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var d=function(e){return e.replace(/[^a-zA-Z0-9]/gi,"_").toLowerCase()},p=function(e){return"string"==typeof e?e.split(" ").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join(" "):e};var v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r="recently_searched_terms",n=JSON.parse(localStorage.getItem(r)||"[]");if(!e||function(e,t){if(Array.isArray(e)){var r,n=t.toLowerCase(),o=m(e);try{for(o.s();!(r=o.n()).done;)if(r.value.toLowerCase()===n)return!0}catch(e){o.e(e)}finally{o.f()}return!1}}(n,e))return!1;t?n.includes(e)||n.unshift(e):n=n.filter((function(t){return t!==e})),n.length>5&&n.pop(),localStorage.setItem(r,JSON.stringify(n))},y=function(){var e=window.localStorage.getItem("recently_searched_terms"),t=window.localStorage.getItem("product_data_storage"),r=window.localStorage.getItem("recently_viewed_product");return{productTerms:h(e)?JSON.parse([e]):[],productData:h(t)?JSON.parse([t]):{},recentlyViewedProduct:h(r)?JSON.parse([r]):{}}},h=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},g=function(e,t){window.localStorage.setItem(e,t)},b="SEARCH_QUERY",E="SEARCH_CLEAR",w="SEARCH_REQUEST",S="SEARCH_PRODUCTS",q="SEARCH_RESULT",O="SEARCH_RESULT_ERROR",P="SEARCH_CORRECTIONS_STARTED",C="SEARCH_CORRECTION_SUCCESS",L="SEARCH_CORRECTIONS_ERROR",N="PRODUCTS_CLEAR",j="SUGGEST_CLICK",I="SUGGEST_REQUEST",k="SUGGEST_SUCCESS",Q="SUGGEST_FAILURE",B="TOGGLE_DROPDOWN",T="UPDATE_LS_PRODUCTS",Z="TOGGLE_MAIN_LOADER";function G(e){return G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},G(e)}function J(e){return function(e){if(Array.isArray(e))return D(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return D(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return D(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function x(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?x(Object(r),!0).forEach((function(t){Y(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Y(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==G(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==G(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===G(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var W,U,K=y(),R=function(e){var t=new Set;return null==e?void 0:e.filter((function(e){var r=null==e?void 0:e.term.toLowerCase(),n=t.has(r);return t.add(r),!n}))},z=F({showMainLoader:!1,isSearching:!1,showDropdown:!1,noResult:!1,query:null,products:[],productsCount:0,suggestions:{isInitialFetch:!0,fetching:!1,activeTerm:null,term:null,terms:[],products:{},redirects:{},error:!1},corrections:{terms:[],products:[],isFetchingProducts:!1,errorMsgFetchingProducts:null,redirects:{}},config:window.unbxdConfig||{},error:!1,errorMsg:null},K),H=function(){var e,t,r,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:z,o=arguments.length>1?arguments[1]:void 0,a={},i=[],l="";switch(o.type){case E:var c=y();return Object.assign({},F(F({},z),c));case b:return Object.assign({},n,{isSearching:!1,query:o.query,error:!1,errorMsg:null,noResult:!1});case w:return Object.assign({},n,{isSearching:!0,query:o.query,showDropdown:!0});case"CLEAR_ACTIVE_TERM":return Object.assign({},n,{suggestions:F(F({},n.suggestions),{},{activeTerm:null})});case q:var u=J(o.terms);if(void 0!==o.payload.products)a[d(o.query)]=o.payload.products;var s=R([].concat(J(null===(e=n.suggestions)||void 0===e?void 0:e.terms),J(u))),A=R([].concat(J(null==n||null===(t=n.corrections)||void 0===t?void 0:t.terms),J(o.payload.spellCorrections)));return Object.assign({},n,{isSearching:!1,showDropdown:!0,suggestions:F(F({},n.suggestions),{},{terms:s}),corrections:F(F({},n.corrections),{},{terms:A})});case S:if(void 0!==o.payload.products)a[d(o.query)]=o.payload.products;case O:return Object.assign({},n,{isSearching:!1,error:!0,noResult:!1,errorMsg:o.error,products:[]});case N:return Object.assign({},n,{products:[],suggestions:F(F({},n.suggestions),{},{activeTerm:null})});case j:return l=p(o.term),i=n.suggestions.products[l]||[],Object.assign({},n,{noResult:!i.length,suggestions:F(F({},n.suggestions),{},{isInitialFetch:!1,activeTerm:l})});case I:return Object.assign({},n,{noResult:!1,suggestions:F(F({},n.suggestions),{},{isInitialFetch:o.initial,fetching:!0})});case k:var m=o.payload,f=n.suggestions.redirects||{},v=p(o.term),h=p(n.query);a[v]=m.products,m.products.length&&(a[v]=m.products);var g=R([v===h?{term:v,product_count:m.queryProductsCount}:{term:h}].concat(J(null==n||null===(r=n.suggestions)||void 0===r?void 0:r.terms)));return m.redirect&&((f={})[v]=m.redirect),Object.assign({},n,{noResult:m.products.length<1&&!n.suggestions.products[v],suggestions:F(F({},n.suggestions),{},{fetching:!1,terms:g,products:F(F({},n.suggestions.products),Y({},v,m.products)),redirects:f}),corrections:F(F({},n.corrections),{},{terms:m.spellCorrections||[]})});case C:var G=o.payload.metaData.queryParams.q,D=o.payload.redirect,x={};D&&(x[p(G)]=D);var W=o.payload.products;return Object.assign({},n,{corrections:F(F({},n.corrections),{},{isFetchingProducts:!1,products:W||{},redirects:x})});case P:return Object.assign({},n,{corrections:F(F({},n.corrections),{},{isFetchingProducts:!0})});case L:return Object.assign({},n,{corrections:F(F({},n.corrections),{},{isFetchingProducts:!1,errorMsgFetchingProducts:o.error})});case Q:return Object.assign({},n,{showDropdown:!0,suggestions:F(F({},n.suggestions),{},{error:o.error})});case B:return Object.assign({},n,{showDropdown:Boolean(o.payload)});case T:var U=y();return Object.assign({},n,F({},U));case Z:var K,H=F({},n),X=null==o||null===(K=o.payload)||void 0===K?void 0:K.loading;return F(F({},H),{},{showMainLoader:X});default:return n}},X=window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__||l.qC;function M(e){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},M(e)}function V(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==M(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===M(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var _=null===(W=window)||void 0===W?void 0:W.unbxdConfig,$=null==_?void 0:_.autoCompleteUrl,ee=(null==_||_.autoSugguestUrl,null==_?void 0:_.searchUrl),te="price",re="specialPrice",ne="originalPrice",oe=(V(U={},"Default Store View",1),V(U,"Queensland Store View",2),V(U,"South Australia Store View",3),V(U,"Western Australia Store View",4),U),ae="data:image/gif;base64,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";function ie(e){return function(e){if(Array.isArray(e))return le(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return le(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return le(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function le(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ce=function(e){var t,r,n=null==e||null===(t=e.response)||void 0===t?void 0:t.products,o=null==e?void 0:e.searchMetaData,a=(null==e?void 0:e.didYouMean)&&[null==e?void 0:e.didYouMean[0].suggestion],i=null==n?void 0:n.filter((function(e){return e.hasOwnProperty("sku")&&e})),l=e.response.numberOfProducts||0,c=null==n?void 0:n.filter((function(e){return"KEYWORD_SUGGESTION"===(null==e?void 0:e.doctype)&&e})),u=null==n?void 0:n.filter((function(e){return"TOP_SEARCH_QUERIES"===(null==e?void 0:e.doctype)&&e})),s=null==n?void 0:n.filter((function(e){return"IN_FIELD"===(null==e?void 0:e.doctype)&&e})),A=null==e||null===(r=e.redirect)||void 0===r?void 0:r.value;return{metaData:o,products:i,queryProductsCount:l,autoSuggest:[].concat(ie(u),ie(c)),inFieldsSuggestion:s,spellCorrections:a,redirect:A}},ue=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e.toFixed(2).toString();if(-1===t.indexOf("."))return e;var r=t.split(".");return e=r.length>1&&"00"!==r[1]?r:r[0]},se=r(238);function Ae(e){return Ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ae(e)}function me(){me=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function u(e,t,r,o){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),l=new O(o||[]);return n(i,"_invoke",{value:E(e,r,l)}),i}function s(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var A={};function m(){}function f(){}function d(){}var p={};c(p,a,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(P([])));y&&y!==t&&r.call(y,a)&&(p=y);var h=d.prototype=m.prototype=Object.create(p);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function o(n,a,i,l){var c=s(e[n],e,a);if("throw"!==c.type){var u=c.arg,A=u.value;return A&&"object"==Ae(A)&&r.call(A,"__await")?t.resolve(A.__await).then((function(e){o("next",e,i,l)}),(function(e){o("throw",e,i,l)})):t.resolve(A).then((function(e){u.value=e,i(u)}),(function(e){return o("throw",e,i,l)}))}l(c.arg)}var a;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){o(e,r,t,n)}))}return a=a?a.then(n,n):n()}})}function E(e,t,r){var n="suspendedStart";return function(o,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return C()}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var l=w(i,r);if(l){if(l===A)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(e,t,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===A)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function w(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),A;var o=s(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,A;var a=o.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,A):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,A)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function q(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function P(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:C}}function C(){return{value:void 0,done:!0}}return f.prototype=d,n(h,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:f,configurable:!0}),f.displayName=c(d,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,c(e,l,"GeneratorFunction")),e.prototype=Object.create(h),e},e.awrap=function(e){return{__await:e}},g(b.prototype),c(b.prototype,i,(function(){return this})),e.AsyncIterator=b,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new b(u(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},g(h),c(h,l,"Generator"),c(h,a,(function(){return this})),c(h,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=P,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(q),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return i.type="throw",i.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,A):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),A},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),q(r),A}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;q(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:P(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),A}},e}function fe(e){return function(e){if(Array.isArray(e))return de(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return de(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return de(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function de(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pe(e,t,r,n,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(n,o)}var ve,ye,he=se.Z.CancelToken,ge=function(e){return function(t){return new Promise((function(r,n){t({type:b,query:e}),r(e)}))}},be=function(){return function(e){return new Promise((function(t,r){ve&&ve(),ye&&ye(),e({type:E}),t()}))}},Ee=function(e){return{type:w,query:e}},we=function(e,t,r){return{type:q,query:e,terms:t,payload:r}},Se=function(e){return{type:O,error:e}},qe=function(e){return{type:j,term:e}},Oe=function(e){return function(){var t,r=(t=me().mark((function t(r){return me().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r(Ee(e)),ye&&ye(),se.Z.get("".concat($,"&q=").concat(e),{method:"GET",headers:{"Content-Type":"application/json"},cancelToken:new he((function(e){return ye=e}))}).then((function(t){var n=t.data,o=ce(n),a=(o||{}).metaData;if(a&&a.statusCode&&400===a.statusCode){var i=a.displayMessage||a.message;r(Se(i))}else{var l=e,c=Ne(o.autoSuggest)||[],u=o.spellCorrections||[],s=Le(o.inFieldSuggestion)||[],A=je(o.inFieldSuggestion||[],[l].concat(fe(u),fe(c),fe(s))),m=Le(o.promotedSuggestion)||[],f=[].concat(fe(u),fe(m),fe(c),fe(s),fe(A));o.spellCorrections=[].concat(fe(u),fe(m),fe(s)),r(we(e,f,o))}})).catch((function(e){se.Z.isCancel(e)||r(Se("An error occurred during the request."))}));case 3:case"end":return t.stop()}}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(e){pe(a,n,o,i,l,"next",e)}function l(e){pe(a,n,o,i,l,"throw",e)}i(void 0)}))});return function(e){return r.apply(this,arguments)}}()},Pe=function(e){return function(t){t({type:P}),se.Z.get("".concat(ee,"&q=").concat(e,"&pageSize=5"),{cancelToken:new he((function(e){return ve=e}))}).then((function(r){var n=r.data,o=ce(n)||{};t(function(e,t){return{type:C,term:e,payload:t}}(e,o))})).catch((function(e){se.Z.isCancel(e)||t(function(e){return{type:L,error:e}}(e))}))}},Ce=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(r){return r(function(e,t){return{type:I,term:e,initial:t}}(e,t)),ve&&ve(),se.Z.get("".concat(ee),{method:"GET",cancelToken:new he((function(e){return ve=e})),params:{q:e,pageSize:12}}).then((function(t){var n=null==t?void 0:t.data,o=ce(n)||{};0===(o.products||[]).length&&v(e,!1),r(function(e,t){return{type:k,term:e,payload:t}}(e,o))})).catch((function(t){v(e,!1),se.Z.isCancel(t)||r(function(e){return{type:Q,error:e}}(t))}))}},Le=function(e){return null==e?void 0:e.map((function(e){var t;return null==e||null===(t=e.autosuggest)||void 0===t?void 0:t.trim()}))},Ne=function(e){return null==e?void 0:e.map((function(e){var t;return{term:null==e||null===(t=e.autosuggest)||void 0===t?void 0:t.trim(),product_count:e.result_unbxd_double}}))},je=function(e,t){for(var r=[],n=t.length?t[0]:"",o=0;o<e.length;o++)if(void 0!==e[o].brand_in){var a=e[o].brand_in.filter((function(e){var r=e.trim();return-1!==t.indexOf(r)&&r!==n})).map((function(e){return e.trim()+" "+n}));r=[].concat(fe(r),fe(a))}return r},Ie=function(e){return{type:B,payload:e}},ke=function(){return{type:T}},Qe=function(e){return{type:Z,payload:{loading:e}}},Be=function(e){var t=e.style;return n.createElement("div",{className:"spinner",style:t},n.createElement("div",{className:"bar1"}),n.createElement("div",{className:"bar2"}),n.createElement("div",{className:"bar3"}),n.createElement("div",{className:"bar4"}),n.createElement("div",{className:"bar5"}),n.createElement("div",{className:"bar6"}),n.createElement("div",{className:"bar7"}),n.createElement("div",{className:"bar8"}),n.createElement("div",{className:"bar9"}),n.createElement("div",{className:"bar10"}),n.createElement("div",{className:"bar11"}),n.createElement("div",{className:"bar12"}))},Te=function(e){var t=e.show,r=e.message;return t?n.createElement("div",null,n.createElement("p",null,r)):null},Ze=function(e){var t=e.count;return t>0?n.createElement("div",{className:"product-count"},n.createElement("span",null,t),n.createElement("span",null,t>1?"products":"product")):null};function Ge(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Je(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Je(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Je(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var De=function(e){var t=e.categories,r=e.isSearchFullType,o=(0,a.I0)(),i=(0,a.v9)((function(e){return e.suggestions})),l=i.activeTerm,c=i.fetching,u=i.redirects,s=u&&u[l]?u[l]:null,A=Ge((0,n.useState)(),2),m=(A[0],A[1]);(0,n.useEffect)((function(){s&&m(s)}),[s]);return n.createElement(n.Fragment,null,n.createElement("div",{className:"search-autocomplete-categories suggested-keywords"},r?n.createElement(n.Fragment,null,n.createElement(Te,{show:!0,message:"Continue typing to refine search suggestions & results..."}),n.createElement("h5",null,"suggested keywords")):null,t&&t.length?n.createElement("ul",{className:"categories-list"},function(e){return e.map((function(e,t){var a,i=!!l&&(null==l?void 0:l.toLowerCase())==(null==e||null===(a=e.term)||void 0===a?void 0:a.toLowerCase());return n.createElement("li",{key:t,className:"categories-list-item"},n.createElement("a",{className:"category-container ".concat(i?"active":"inactive"),onClick:function(t){return function(e,t){var r;e.preventDefault(),t&&(r="/catalogsearch/result/?q=".concat(encodeURIComponent(t))),o(Qe(!0)),o(qe(t)),v(t),o(ke()),window.location.href=r}(t,null==e?void 0:e.term)}},r?n.createElement(n.Fragment,null,n.createElement("p",null,e.term),n.createElement(Ze,{count:null==e?void 0:e.product_count}),e===l&&c&&n.createElement(Be,{style:{marginLeft:"10px"}})):e.term))}))}(t)):null))},xe=function(e){var t=e.show,r=e.message;return t?n.createElement("div",{className:"instant-search-loader"},n.createElement("img",{src:ae,alt:"loading"}),r&&n.createElement("span",null,r)):null},Fe=function(e){var t=e.showMainLoaderMask;return(0,a.v9)((function(e){return e.showMainLoader}))||t?n.createElement("div",{className:"loading-mask","data-role":"loader"},n.createElement("div",{className:"loader"},n.createElement("img",{alt:"Loading...",src:ae}),n.createElement("p",null,"Please wait..."))):null},Ye=function(e){var t=e.error,r=e.errorMsg,o=e.query,a=r&&r.replace("{{%q}}",o);return t&&o?n.createElement("div",{className:"search-autocomplete-error"},n.createElement("p",null,n.createElement("strong",null,a))):null},We=function(e){var t=e.finalPrice,r=e.currency,o=(0,e.format)(t,!1);return"undefined"!==t?n.createElement("span",{className:"product-price"},n.createElement("span",{className:"currency-symbol"},r),n.createElement("span",{className:"price-main"},Array.isArray(o)?n.createElement(n.Fragment,null,null==o?void 0:o[0],n.createElement("span",{class:"decimal-dot"},"."),n.createElement("span",{class:"price-decimal"},null==o?void 0:o[1])):o)):null},Ue=function(e){var t=e.savings,r=e.type;return r.match(/(not|n't)/gi)||t<=0||r.match(/do not/gi)?null:n.createElement("span",{className:"you-save-statement"},n.createElement("span",{className:"wrap"},function(){var t=e.savings,r=e.currency,o=e.format,a=e.type,i=e.originalPrice,l=e.finalPrice,c=o(t);return a.match(/percentage/gi)?n.createElement(n.Fragment,null,"Save ","",function(e,t){var r=(e-t)/e*100;return Math.floor(r)}(i,l),n.createElement("span",{className:"price-decimal"},"%")):n.createElement(n.Fragment,null,"Save ",n.createElement("span",{className:"currency-symbol"},r),n.createElement("span",{className:"save-price wrap"},Array.isArray(c)?n.createElement(n.Fragment,null,null==c?void 0:c[0],n.createElement("span",{class:"decimal-dot"},"."),n.createElement("span",{class:"you-save-decimal"},null==c?void 0:c[1])):c))}()))},Ke=function(e){var t=e.label;return t&&-1!==t.search(/free/gi)?n.createElement("span",{className:"shipping-label"},"Free"):null};function Re(e){return Re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Re(e)}function ze(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function He(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ze(Object(r),!0).forEach((function(t){Xe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ze(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Xe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Re(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Re(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Re(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Me=function(e){var t=e.data,r=e.placeholder,o=t.unbxdAmastyLabelTopRight,a=t.unbxdAmastyLabelTopLeft,i=t.unbxdAmastyLabelBottomRight,l=[{label:a,position:"0"},{label:o,position:"2"},{label:t.unbxdAmastyLabelBottomLeft,position:"6"},{label:i,position:"8"}],c=(0,n.useRef)(null),u=function(e){var t,r=/\.(gif|jpe?g|tiff?|png|webp|bmp)$/i;return["labeledImage","thumbnail","smallImage"].forEach((function(n){if(!t&&e[n]&&r.test(e[n]))return t=e[n]})),!t&&e.imageUrl&&e.imageUrl.length&&(t=e.imageUrl[0]),t}(t);return n.createElement("div",{className:"thumb"},n.createElement("img",{src:u,ref:c,onError:function(){c.current&&c.current.src!==r&&(c.current.src=r)}}),l.map((function(e){var t=e.label,r=e.position;if(!t)return null;var o=function(e){var t={position:"absolute",width:"45%",height:"45%",zIndex:"1",backgroundSize:"contain",backgroundRepeat:"no-repeat"},r=s(s({},t),{},{top:0,backgroundPosition:"top"}),n=s(s({},t),{},{bottom:0,backgroundPosition:"bottom"});switch(e){case"0":default:return s(s({},r),{},{left:0});case"2":return s(s({},r),{},{right:0});case"6":return s(s({},n),{},{left:0});case"8":return s(s({},n),{},{right:0})}}(r);return n.createElement("span",{key:t,className:"amasty_label_image",style:He(He({},o),{},{backgroundImage:"url(".concat(t,")")})})})))},Ve=function(e){var t=e.top,r=e.bottom,o=e.template,a=function(e){var t=o.replace("{{label_type}}",e).replace("{{img_type}}","thumbnail");return n.createElement("img",{src:t,alt:"Product Label"})};return t||r?n.createElement("span",{className:"product-labels"},t?n.createElement("span",{className:"product-label label_top"},a(t)):null,r?n.createElement("span",{className:"product-label label_bottom"},a(r)):null):null},_e=r(311);function $e(e){return $e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$e(e)}var et=function(e){var t,r,o,i=e.data,l=e.position,c=(0,a.v9)((function(e){return e.config})),u=(0,a.v9)((function(e){return e.suggestions.activeTerm||e.query})),s=function(e){var t;if(e){var r=null===(t=window)||void 0===t||null===(t=t.unbxdConfig)||void 0===t?void 0:t.storeName;if(!r)return{finalPrice:null==e?void 0:e.price,storeSpecialPrice:null==e?void 0:e.specialPrice,storeOriginalPrice:null==e?void 0:e.originalPrice};var n="Store".concat(oe[r]);return{finalPrice:(null==e?void 0:e["".concat(te).concat(n)])||(null==e?void 0:e[te]),storeSpecialPrice:(null==e?void 0:e["".concat(re).concat(n)])||(null==e?void 0:e[re]),storeOriginalPrice:(null==e?void 0:e["".concat(ne).concat(n)])||(null==e?void 0:e[ne])}}}(i),A=s.finalPrice,m=s.storeSpecialPrice,f=s.storeOriginalPrice,d=(0,a.I0)(),p=function(e,t){t.preventDefault(),d(Qe(!0)),"object"===$e(window.AEC)&&window.AEC.gtm()&&(window.dataLayer=window.dataLayer||[],window.AEC.click(t.currentTarget,window.dataLayer),window.dataLayer.push({event:"unbxdSearchQuery",searchQueryPayload:{query:u}})),window.location=e},v=c.placeholderImgUrl||"",y="bundle"===i.typeId&&m?A/(1-(100-m)/100):f,h=i.sku.endsWith("v")?"configurable":i.typeId,g=!1,b=localStorage["mage-cache-storage"],E="true"!==(null==i||null===(t=i.notForSale)||void 0===t?void 0:t.toLowerCase()),w=!(!y||y===A)&&y-A,S=void 0!==i.price&&!g&&"true"!==(null==i||null===(r=i.notForSale)||void 0===r?void 0:r.toLowerCase()),q="configurable"!==h&&w&&i.showSavedLabel&&!g&&"true"!==(null==i||null===(o=i.notForSale)||void 0===o?void 0:o.toLowerCase());if(void 0!==b){var O=JSON.parse(b);g=O.company&&O.company.has_customer_company}return n.createElement("div",{className:"search-autocomplete-product",role:"options",id:"option-"+i.sku},n.createElement("a",{className:"search-autocomplete-hit",href:i.productUrl,"data-id":i.sku,"data-name":i.title,"data-price":i.price,"data-position":l,"data-brand":i.brand&&i.brand.length?i.brand[0]:"","data-category":"Search Autosuggest","data-list":"Search Autosuggest","data-event":"productClick","data-store":c.storeName||"","data-attributes":"[]",onClick:function(e){return p(i.productUrl,e)}},n.createElement(Me,{data:i,placeholder:v}),n.createElement("div",{className:"info"},n.createElement("div",{role:"title",className:"product-title"},(0,_e.ZP)(i.title)),n.createElement("div",{className:"mpn"},"MPN: ",i.partNo),n.createElement("div",{className:"price-box"},S&&E?n.createElement(We,{currency:c.storeCurrencySymbol||"$",finalPrice:A,format:ue}):null,q&&E?n.createElement(n.Fragment,null,n.createElement(Ue,{currency:c.storeCurrencySymbol||"$",savings:w,finalPrice:A,originalPrice:y,format:ue,type:i.showSavedLabel})):null),n.createElement("div",{className:"product-item-actions"},n.createElement("button",{className:"action todetails primary","data-id":i.sku,"data-name":i.title,"data-price":i.price,"data-quantity":1,"data-position":l,"data-brand":i.brand&&i.brand.length?i.brand[0]:"","data-category":"Search Results","data-list":"Search Results","data-event":"productClick","data-store":c.storeName||"","data-attributes":"[]",onClick:function(e){return p(i.productUrl,ev)}},n.createElement("span",null,"View Details"))),n.createElement("div",{className:"labels-wrapper"},n.createElement(Ke,{label:i.shippingLabel}),c.showProductLabels?n.createElement(Ve,{top:i.labelTop,bottom:i.labelBottom,template:c.labelsUrlTemplate}):null))))},tt=function(e){var t=e.products,r=e.activeTerm,o=e.redirect,i=(document.getElementById("search_mini_form"),(0,a.I0)());return t&&t.length>0?n.createElement("div",{className:"search-autocomplete-products"},n.createElement("h5",null,"search results"),r&&n.createElement("div",{className:"search-autocomplete-more".concat(t.length<5?" hidden":"")},n.createElement("a",{className:"button button-primary__outline",onClick:function(){var e="/catalogsearch/result/?q=".concat(encodeURIComponent(r));o&&(e=o),i(Qe(!0)),v(r),window.location.href=e}},"View all results for ",n.createElement("span",null,'"'.concat(r,'"')))),n.createElement("div",null,t.map((function(e,t){return n.createElement(et,{key:t,position:t+1,data:e})})))):null},rt=function(){var e=(0,a.v9)((function(e){return e.corrections})),t=e.terms,r=e.redirects,o=(0,a.I0)();return t&&t.length?n.createElement("div",{className:"search-autocomplete-corrections"},n.createElement("span",{className:"ttl"},"Are you looking for:"),null==t?void 0:t.map((function(e,t){return n.createElement("span",{className:"opt",key:t,onClick:function(t){return function(e,t){t.preventDefault();var n,a=p(e),i=r&&r[a]?r[a]:null;n=i||"/catalogsearch/result/?q=".concat(encodeURIComponent(e)),o(Qe(!0)),v(e),window.location.href=n}(e,t)}},p(e))}))):null},nt=function(e){var t=e.show,r=e.activeTerm,o=(0,a.v9)((function(e){return e})),i=o.isSearching,l=o.corrections,c=l.products,u=l.terms,s=l.isFetchingProducts,A=(0,a.I0)();return(0,n.useEffect)((function(){u&&u[0]&&A(Pe(u[0]))}),[null==u?void 0:u.length,A]),t&&r?n.createElement("div",{className:"search-autocomplete-no-result"},n.createElement("p",null,"No results found for ",n.createElement("strong",null,'"'.concat(r,'"'))),i?null:n.createElement(rt,null),n.createElement("div",{style:{marginTop:"10px"}},n.createElement(xe,{show:s}),n.createElement(tt,{products:c}))):null};function ot(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return at(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return at(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function at(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var it=function(){var e=(0,a.v9)((function(e){return e})),t=e.productTerms,r=e.productData,o=e.recentlyViewedProduct,i=e.showDropdown,l=e.activeTerm,c=ot((0,n.useState)([]),2),u=c[0],s=c[1],A=document.getElementById("search"),m=(0,a.I0)(),f=function(){if(null!=t&&t.length){m(Ie(!i));var e=document.querySelector(".col-right");l&&l.length>1?e.style.display="block":e.style.display="none"}},d=function(){var e=document.querySelector(".col-right");A.value.length>1&&(e.style.display="block")},p=function(){m(ke())},v=function(e){"complete"===e.target.readyState&&m(ke())},y=function(){var e=Object.values(o).filter((function(e){var t=r.hasOwnProperty(e.product_id),n=new Date,o=new Date(1e3*e.added_at);return t&&e.scope_id===window.checkout.websiteId&&n-o<36e5})).sort((function(e,t){return t.added_at-e.added_at})).map((function(e){return r[e.product_id]}));e.length>5&&(e.length=5),s(e)};return(0,n.useEffect)((function(){var e,t;return y(),null===(e=window)||void 0===e||e.addEventListener("storage",p,!1),null===(t=window.document)||void 0===t||t.addEventListener("readystatechange",v,!1),function(){var e,t;null===(e=window)||void 0===e||e.removeEventListener("storage",p,!1),null===(t=window.document)||void 0===t||t.removeEventListener("readystatechange",v,!1)}}),[]),(0,n.useEffect)((function(){var e,t;(null==Object||null===(e=Object.keys(o))||void 0===e?void 0:e.length)!==(null==Object||null===(t=Object.keys(o))||void 0===t?void 0:t.length)&&y()}),[o]),(0,n.useEffect)((function(){return null==A||A.addEventListener("click",f,!1),null==A||A.addEventListener("change",d,!1),function(){null==A||A.removeEventListener("click",f,!1),null==A||A.removeEventListener("change",d,!1)}})),n.createElement("div",{className:"recently-viewed-list"},t.length>0&&n.createElement("div",{className:"searched-terms"},n.createElement(Te,{show:!0,message:"Start typing to find all tools for all trades"}),n.createElement("div",{className:"searched"},n.createElement("h5",null,"Recently Searched"),n.createElement("div",{className:"clear-all"},n.createElement("a",{onClick:function(){g("recently_searched_terms",JSON.stringify([])),m(ke())}},"Clear All"))),n.createElement("div",{className:"search-autocomplete-categories"},n.createElement("div",{className:"section"},n.createElement("div",{className:"section-content"},n.createElement("ul",{className:"categories-list"},t&&(null==t?void 0:t.map((function(e){return n.createElement("li",{key:e,className:"categories-list-item"},n.createElement("a",{onClick:function(){return t=e,r=document.querySelector(".col-right"),s([]),A.value=t,window.location.href="/catalogsearch/result/?q=".concat(t),void(r.style.display="none");var t,r},className:"recently-viewed-label"},e),n.createElement("a",{className:"recently-viewed-icons-close",onClick:function(){return r=e,n=t.filter((function(e){return e!==r})),g("recently_searched_terms",JSON.stringify(n)),void m(ke());var r,n}}))})))))))),u.length>0&&n.createElement("div",{className:"recently-viewed-products"},n.createElement("h5",null,"recently viewed products"),n.createElement("div",{className:"recently-viewd-item"},u.map((function(e,t){var r,o=ue(e.price_info.final_price);return n.createElement("div",{key:t,className:"search-autocomplete-product",role:"options",id:"options-".concat(e.id)},n.createElement("a",{className:"search-autocomplete-hit",href:e.url,"data-id":"product_id","data-name":"product.name","data-price":"product.price_info.final_price","data-position":"","data-brand":"","data-category":"","data-list":"","data-event":"","data-store":"","data-attributes":"[]"},n.createElement("div",{className:"thumb"},n.createElement("img",{src:e.extension_attributes.labeled_image||e.images[0].url,alt:e.name})),n.createElement("div",{className:"info"},n.createElement("div",{role:"title",className:"product-title"},(0,_e.ZP)(e.name)),n.createElement("div",{className:"price-block"},!0!==(null==e||null===(r=e.extension_attributes)||void 0===r?void 0:r.not_for_sale)&&n.createElement(n.Fragment,null,n.createElement("span",{className:"product-price"},n.createElement("span",{className:"currency-symbol"},"$"),n.createElement("span",{className:"price-main"},Array.isArray(o)?n.createElement(n.Fragment,null,null==o?void 0:o[0],n.createElement("span",{class:"decimal-dot"},"."),n.createElement("span",{class:"price-decimal"},null==o?void 0:o[1])):o)))),n.createElement("div",{className:"product-item-actions"},n.createElement("a",{className:"action todetails primary","data-quantity":1,"data-category":"Search Results","data-list":"Search Results","data-event":"productClick","data-attributes":"[]"},n.createElement("span",null,"View Details"))),n.createElement("div",{className:"labels-wrapper"},n.createElement(Ke,{label:e.shippingLabel})))))})))))};function lt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ct(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ct(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ct(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ut=document.getElementById("search"),st=document.getElementById("search_mini_form"),At=st.querySelector('button[type="submit"]'),mt=function(e){var t=e.isFocused,r=e.isSearchFullType,o=e.setShowMainLoaderMask,i=(0,a.v9)((function(e){return e})),l=i.showDropdown,c=i.isSearching,u=i.suggestions,s=u.activeTerm,A=u.fetching,m=u.redirects,f=m&&m[s]?m[s]:null,d=(0,a.I0)(),p=(0,n.useRef)(),y=lt((0,n.useState)(""),2),h=y[0],g=y[1];(0,n.useEffect)((function(){var e;t&&(null==p||null===(e=p.current)||void 0===e||e.focus())}),[t]);var b,E,w,S=(b=function(e){!function(e){var t,r,n,o=document.getElementById("minisearch-label"),a=document.getElementById("minisearch-control"),i=document.getElementsByClassName("col-right");st&&!st.classList.contains("opened")&&(null==st||null===(t=st.classList)||void 0===t||t.add("opened"),null==o||null===(r=o.classList)||void 0===r||r.add("opened"),null==a||null===(n=a.classList)||void 0===n||n.add("opened")),i.length&&(i[0].style.display="none"),d(ge(e));var l=e||"";!l||l.length<2?d(be()):d(be()).then((function(){return d(ge(l))})).then((function(){return d(Oe(l))})).then((function(){return d(qe(l))})).then((function(){return d(Ce(l,!0))}))}(e)},E=500,function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=this;clearTimeout(w),w=setTimeout((function(){b.apply(n,t)}),E)}),q=function(e){var t,r=e.target.value,n="";r.match(/<[^>]*?>|<\/[^>]+>|<[^>]*$|^[^<]*>/g)?n=""===(t=r.replace(/<[^>]*?>[^<]*|<\/[^>]+>|<[^>]*$|>[^<]*/g,"")).trim()?"":t.trim():n=r;ut&&(ut.value=n),g(n),S(n)},O=function(e){e.preventDefault(),e.stopPropagation(),h&&(null==h?void 0:h.length)>=2&&(null==o||o(!0),P())},P=function(){var e,t=(null==ut?void 0:ut.value)||h||"";if(t){var r=null==(e=t)?void 0:e.replace(/^\.*/,""),n=encodeURIComponent(r)?"/catalogsearch/result/?q=".concat(encodeURIComponent(r)):"";if(n)return v(r),void(window.location.href=n)}},C=function(e){e.stopPropagation(),e.preventDefault(),s&&!l&&d(Ie(!0))};return(0,n.useEffect)((function(){return null==ut||ut.addEventListener("input",q),function(){ut&&(null==ut||ut.removeEventListener("input",q),ut.removeEventListener("click",C,!1),st.removeEventListener("submit",O),null==At||At.removeEventListener("click",O))}}),[]),(0,n.useEffect)((function(){ut.addEventListener("click",C)}),[s,l]),(0,n.useEffect)((function(){null==st||st.addEventListener("submit",O),null==At||At.addEventListener("click",O)}),[h,c,A,s,f]),r?n.createElement(n.Fragment,null,n.createElement("input",{ref:p,className:"input-text",type:"text",name:"search",value:h,placeholder:"Find your tools...",maxLength:"128",role:"combobox",onChange:q,onClick:function(){var e;null==p||null===(e=p.current)||void 0===e||e.focus()}}),n.createElement("div",{className:"actions"},n.createElement("button",{type:"submit",className:"action search",onClick:function(e){return O(e)}},"search"))):null},ft=document.getElementById("minisearch-control"),dt=document.getElementById("minisearch-label"),pt=function(e){(0,n.useEffect)((function(){return document.addEventListener("mousedown",t,!1),function(){return document.removeEventListener("mousedown",t,!1)}}),[]);var t=function(e){dt&&e.target.contains(dt)&&ft&&ft.classList.toggle("opened")};return n.createElement(mt,e)},vt=function(){var e,t=null===(e=window)||void 0===e||null===(e=e.unbxdConfig)||void 0===e?void 0:e.popularSearchTerms,r=null==t?void 0:t.split(",").filter((function(e){return""!==e.trim()})),o=document.getElementById("search");return null!=r&&r.length?n.createElement("div",{className:"trending-search"},n.createElement("h5",null,"Trending Searches"),n.createElement("ul",{className:"categories-list"},null==r?void 0:r.map((function(e){return n.createElement("li",{key:e,className:"categories-list-item"},n.createElement("button",{type:"button",onClick:function(){return t=e,r=document.querySelector(".col-right"),o.value=t,window.location.href="/catalogsearch/result/?q=".concat(t),void(r.style.display="none");var t,r}},e))})))):null};function yt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ht(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ht(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ht(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var gt=document.getElementById("search_mini_form"),bt=function(){var e=(0,n.useRef)(),t=document.getElementById("minisearch-control"),r=document.getElementById("search-main"),o=document.body,i=yt((0,n.useState)([]),2),l=i[0],c=i[1],u=yt((0,n.useState)(!1),2),s=u[0],A=u[1],m=yt((0,n.useState)(!1),2),f=m[0],d=m[1],y=yt((0,n.useState)(!1),2),h=y[0],g=y[1],b=yt((0,n.useState)(""),2),E=b[0],w=b[1],q=window.innerWidth>767,O=(0,a.I0)(),P=(0,a.v9)((function(e){return e})),C=P.query,L=P.isSearching,j=P.noResult,I=P.error,k=P.errorMsg,Q=P.productTerms,B=P.suggestions,T=P.corrections.terms,Z=B.isInitialFetch,G=B.fetching,J=B.terms,D=B.activeTerm,x=B.redirects,F=B.products,Y=null==C?void 0:C.slice(0,-1),W=p(Y),U=function(){var e,n,a;h||g(!0),null==t||null===(e=t.classList)||void 0===e||e.add("active"),null==r||null===(n=r.classList)||void 0===n||n.add("active"),null==o||null===(a=o.classList)||void 0===a||a.add("search-is-active")},K=(0,n.useCallback)((function(e){if(console.log("handlePressEnter , Called Focus OUtInput"),13===(null==e?void 0:e.keyCode)){var n="/catalogsearch/result/?q=".concat(encodeURIComponent(null==e?void 0:e.target.value));if(E&&(n=E),n){var o,a;if(q)null==t||null===(o=t.classList)||void 0===o||o.remove("active"),null==r||null===(a=r.classList)||void 0===a||a.remove("active");g(!1),O(Qe(!0)),v(null==e?void 0:e.target.value),window.location.href=n}}}),[q,E]);(0,n.useEffect)((function(){var e,t=document.getElementById("search");return null==t||t.addEventListener("focus",U),null==t||t.addEventListener("keyup",K),document.addEventListener("mousedown",R,!1),null==o||null===(e=o.classList)||void 0===e||e.add("search-type-full"),function(){var e;t&&(null==t||t.removeEventListener("focus",U),null==t||t.removeEventListener("keyup",K)),document.removeEventListener("mousedown",R,!1),null==o||null===(e=o.classList)||void 0===e||e.remove("search-type-full")}}),[]),(0,n.useEffect)((function(){var e=E?F[W]:F[D];d(!1),D&&!G&&(null!=e&&e.length||null!=T&&T.length)&&(d(!0),c(e))}),[G,F,D,Z,null==T?void 0:T.length,J.length]);var R=function(n){var a,i,l;gt&&e&&e.current&&!gt.contains(n.target)&&!e.current.contains(n.target)&&(g(!1),null==t||null===(a=t.classList)||void 0===a||a.remove("active"),null==r||null===(i=r.classList)||void 0===i||i.remove("active"),null==o||null===(l=o.classList)||void 0===l||l.remove("search-is-active"))},z=x&&x[D]?x[D]:null,H=!(L||G||J.length||C),X=J.length>0||(null==Q?void 0:Q.length)>0;return(0,n.useEffect)((function(){w(z||"")}),[D,z]),(0,n.useEffect)((function(){E&&O(function(e){return function(t){return new Promise((function(r,n){t(Ce(e)).then((function(n){t({type:S,query:e,payload:{products:n}}),r(n)})).catch((function(e){console.error("Error fetching products:",e),n(e)}))}))}}(Y))}),[E]),n.createElement("div",{className:"new-search-autocomplete search-autocomplete-dropdown ".concat(h?"open":""),ref:e},n.createElement("div",{className:"close-btn-wrap"},n.createElement("button",{className:"close-search-btn",type:"button",onClick:function(e){return(n=e).preventDefault(),n.stopPropagation(),g(!1),null==t||null===(a=t.classList)||void 0===a||a.remove("active"),null==r||null===(i=r.classList)||void 0===i||i.remove("active"),void(null==o||null===(l=o.classList)||void 0===l||l.remove("search-is-active"));var n,a,i,l}},"close")),n.createElement("div",{className:"search-wrapper"},n.createElement(pt,{isFocused:h,setShowMainLoaderMask:A,isSearchFullType:!0})),n.createElement(Fe,{showMainLoaderMask:s}),n.createElement("div",{className:"search-autocomplete-wrapper  search-result"},n.createElement("div",{className:"col-left","aria-busy":L},!X&&!D&&n.createElement("div",{className:"search-autocomplete-categories suggested-keywords"},n.createElement(Te,{show:!0,message:"Start typing to find all tools for all trades"}),n.createElement("div",{className:"searched"},n.createElement("h5",null,"Recently Searched"),n.createElement("p",null,"No recent search"))),H&&n.createElement(n.Fragment,null,n.createElement(it,null),n.createElement(vt,null)),null!=J&&J.length?n.createElement(De,{categories:J,isSearchFullType:!0}):null,n.createElement(xe,{show:L})),n.createElement("div",{className:"col-right ".concat(D?"show":""," ").concat(F&&F[D]?"search-result":""),"aria-busy":G,style:{display:f?"block":"none"}},n.createElement("span",{className:"overlay",onClick:function(){return O(function(e){return{type:N,query:e}}())}}),n.createElement(nt,{show:j,query:C,activeTerm:D}),n.createElement(Ye,{error:I,query:C,errorMsg:k}),j?null:n.createElement(tt,{products:l,activeTerm:D,redirects:x,redirect:z}))))},Et=function(){return n.createElement(bt,null)},wt=function(){return n.createElement(n.Fragment,null,n.createElement(Et,null))},St=function(e){return(0,l.MT)(H,e,X((0,l.md)(i.Z)))}(),qt=function(){return n.createElement(wt,null)},Ot=o.createRoot(document.getElementById("search-autocomplete-container"));Ot&&Ot.render(n.createElement(a.zt,{store:St},n.createElement(qt,null)))}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}};return t[e].call(a.exports,a,a.exports,n),a.exports}n.m=t,e=[],n.O=function(t,r,o,a){if(!r){var i=1/0;for(s=0;s<e.length;s++){r=e[s][0],o=e[s][1],a=e[s][2];for(var l=!0,c=0;c<r.length;c++)(!1&a||i>=a)&&Object.keys(n.O).every((function(e){return n.O[e](r[c])}))?r.splice(c--,1):(l=!1,a<i&&(i=a));if(l){e.splice(s--,1);var u=o();void 0!==u&&(t=u)}}return t}a=a||0;for(var s=e.length;s>0&&e[s-1][2]>a;s--)e[s]=e[s-1];e[s]=[r,o,a]},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){var e={413:0};n.O.j=function(t){return 0===e[t]};var t=function(t,r){var o,a,i=r[0],l=r[1],c=r[2],u=0;if(i.some((function(t){return 0!==e[t]}))){for(o in l)n.o(l,o)&&(n.m[o]=l[o]);if(c)var s=c(n)}for(t&&t(r);u<i.length;u++)a=i[u],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(s)},r=self.webpackChunkunbxd_search_app=self.webpackChunkunbxd_search_app||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}();var o=n.O(void 0,[425],(function(){return n(124)}));o=n.O(o)}();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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