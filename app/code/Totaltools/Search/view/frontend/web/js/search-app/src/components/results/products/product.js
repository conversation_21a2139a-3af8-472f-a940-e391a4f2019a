import React from 'react';
import { useSelector } from 'react-redux';

import Price from './product.price';
import ShippingLabel from './product.shipping.label';
import ProductImage from './product.img';
import { getProductPriceAttributes } from '../../../utils';
import parse from 'html-react-parser';

export const Product = ({ data, position, setRef, setPriceRef }) => {
  const config = useSelector((state) => state.search.config);

  const handleTracking = (url, e) => {
    e.preventDefault();
    let context = e.currentTarget;

    if (
      typeof window.AEC === 'object' &&
      window.AEC.gtm() &&
      (context.tagName.toLowerCase() === 'a' || context.tagName.toLowerCase() === 'button')
    ) {
      window.AEC.click(context, window.dataLayer || []);
    }

    window.location = url;
  };

  const { placeholderImgUrl, showPrice } = config;
  const isForSale = data?.notForSale?.toLowerCase() !== 'true';
  var isB2BCustomer = false;
  var mageCacheStorage = localStorage['mage-cache-storage'];
  if (typeof mageCacheStorage != 'undefined') {
    let mageCacheObj = JSON.parse(mageCacheStorage);
    isB2BCustomer = mageCacheObj.company && mageCacheObj.company.has_customer_company;
  }
  const { storePrice, storeSpecialPrice, storeOriginalPrice } = getProductPriceAttributes(data);
  return data ? (
    <li className="item product product-item myelement">
      <div className="product-item-info" style={{ height: '100%', position: 'relative' }}>
        <a
          className="product photo product-item-photo"
          href={data.productUrl}
          data-id={data.sku}
          data-name={data.title}
          data-price={data.price}
          data-quantity={1}
          data-position={position}
          data-brand={data.brand && data.brand.length ? data.brand[0] : ''}
          data-category={'Search Results'}
          data-list={'Search Results'}
          data-event={'productClick'}
          data-store={config.storeName || ''}
          data-attributes={'[]'}
          onClick={(e) => handleTracking(data.productUrl, e)}
          style={{ display: 'block' }}
        >
          <ProductImage data={data} placeholder={placeholderImgUrl} />
        </a>
        <div className="product details product-item-details">
          <strong className="product name product-item-name" ref={setRef}>
            <a
              href={data.productUrl}
              className="product-item-link"
              data-name={data.title}
              data-price={data.price}
              data-quantity={1}
              data-position={position}
              data-brand={(data.brand && data.brand.length && data.brand[0]) || ''}
              data-category={'Search Results'}
              data-list={'Search Results'}
              data-event={'productClick'}
              data-store={''}
              data-attributes={'[]'}
              onClick={(e) => handleTracking(data.productUrl, e)}
            >
              {parse(data.title)}
            </a>
          </strong>
          <div className="product-item-footer">
            {showPrice && !isB2BCustomer ? (
              <Price
                price={storePrice}
                originalPrice={storeOriginalPrice}
                specialPrice={storeSpecialPrice}
                currency={config.storeCurrencySymbol || '$'}
                productId={data.sku}
                setRef={setPriceRef}
                showSavedLabel={data.showSavedLabel}
                sku={data.sku}
                typeId={data.typeId}
                isForSale={isForSale}
              />
            ) : null}
            <div className="product-labels-wrapper" />
          </div>
          <div className="product-item-inner">
            <div className="product-item-actions">
              <button
                className="action todetails primary"
                data-id={data.sku}
                data-name={data.title}
                data-price={data.price}
                data-quantity={1}
                data-position={position}
                data-brand={data.brand && data.brand.length ? data.brand[0] : ''}
                data-category={'Search Results'}
                data-list={'Search Results'}
                data-event={'productClick'}
                data-store={config.storeName || ''}
                data-attributes={'[]'}
                onClick={(e) => handleTracking(data.productUrl, e)}
              >
                <span>View Details</span>
              </button>
              <ShippingLabel label={data.shippingLabel} />
            </div>
          </div>
        </div>
      </div>
    </li>
  ) : null;
};
