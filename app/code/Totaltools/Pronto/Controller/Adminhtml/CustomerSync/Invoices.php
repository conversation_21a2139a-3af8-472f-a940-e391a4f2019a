<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Controller\Adminhtml\CustomerSync;

use Exception;
use \Totaltools\Pronto\Api\Data\CustomerSyncInterface;
use \Totaltools\Pronto\Api\CustomerSyncRepositoryInterface;
use \Magento\Ui\Component\MassAction\Filter;
use \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;
use Magento\Framework\Controller\ResultFactory;

/**
 * Class Index
 * @package Totaltools\Pronto\Controller\Adminhtml
 */
class Invoices  extends \Magento\Backend\App\Action
{
    /**
     * @var \Totaltools\Pronto\Model\Request\OrderHistory
     */
    protected $orderHistory;

    /**
     * @var \Magento\Customer\Model\Customer
     */
    protected $customer;

    /**
     * @var CustomerSyncInterface
     */
    protected $customerInterface;

    /**
     * @var CustomerSyncRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var Filter
     */
    protected $filter;

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * pushCustomers constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param CustomerSyncInterface $customerSyncInterface
     * @param CustomerSyncRepositoryInterface $customerRepository
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Totaltools\Pronto\Model\Request\OrderHistory $orderHistory,
        \Magento\Customer\Model\Customer $customer
    ) {
        $this->orderHistory = $orderHistory;
        $this->customer = $customer;
        parent::__construct($context);
    }

    /**
     * @return \Magento\Backend\Model\View\Result\Redirect|\Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        $customerId = $this->getRequest()->getParam('id', null);
        try {
            $customer = $this->getCustomerData($customerId);
            if ($customer && $customer->getId()) {
                $this->orderHistory->updateCustomerData($customer, true);
                $now = date('Y-m-d H:i:s');
                $customer->setLoyaltyLastUpdated($now);
                $customer->setData('loyalty_last_updated', $now);
                $customer->getResource()->saveAttribute($customer, 'loyalty_last_updated');
                $this->messageManager->addSuccessMessage(__('Invoices synced'));
            }
        } catch (Exception $exception) {
            $this->messageManager->addErrorMessage('Failed to update invoices');
        }
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setPath('customer/index');
        return $resultRedirect;
    }


    /**
     * @param int $customerId
     * @return \Magento\Customer\Model\Customer
     */
    public function getCustomerData($customerId)
    {
        return  $this->customer->load($customerId);
    }
}
