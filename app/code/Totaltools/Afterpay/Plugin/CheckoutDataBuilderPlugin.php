<?php
namespace Totaltools\Afterpay\Plugin;
use Psr\Log\LoggerInterface;
use Magento\Checkout\Model\Cart;
use Magento\Quote\Model\Quote\Address\Total;
use Magento\Checkout\Model\Session as CheckoutSession;
class CheckoutDataBuilderPlugin
{
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @var Magento\Checkout\Model\Cart $cart
     */
    private $cart;
    /**
     * @var Magento\Quote\Model\Quote\Address\Total
     */
    private $total;
    /**
     * PricePlugin constructor.
     *
     * @param LoggerInterface $logger
     */
    /**
     * @var CheckoutSession
     */
     protected $checkoutSession;

    public function __construct(LoggerInterface $logger, Cart $cart,Total $total, CheckoutSession $checkoutSession)
    {
        $this->logger = $logger;
        $this->cart = $cart;
        $this->total = $total;
        $this->checkoutSession = $checkoutSession;
    }
    /**
     * This method will run before the execute method of the original class.
     *
     * @param \Afterpay\Afterpay\Gateway\Request\Checkout\CheckoutDataBuilde $subject
     * @param array $result
     * @return array
     */
    public function afterBuild(
        \Afterpay\Afterpay\Gateway\Request\Checkout\CheckoutDataBuilder $subject,
        $result
    ) {
        $cartQuote = $this->cart->getQuote();
        $cartBaseTotal  = $cartQuote->getBaseGrandTotal();
        $this->logger->info('Afterpay request Data:', ['Data' => $result ]);
        $addressBaseTotal = $this->total->getBaseGrandTotal();
        $checkoutSessionQuote = $this->checkoutSession->getQuote()->getBaseGrandTotal();

        if($result['amount']['amount'] != $cartBaseTotal){
            $this->logger->info('total-mismatch:'.$result['amount']['amount'].':cart base total:'.$cartBaseTotal);
        }
        $this->logger->info('quote base total:'.$result['amount']['amount'].':cart base total:'.$cartBaseTotal.":quote address total:".$addressBaseTotal.":checkout session total:".$checkoutSessionQuote);
        return $result; 
    }
}