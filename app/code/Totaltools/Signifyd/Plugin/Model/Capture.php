<?php

namespace Totaltools\Signifyd\Plugin\Model;

use Magento\Sales\Model\Order;
use Signifyd\Connect\Model\UpdateOrder\Capture as UpdateOrderCapture;
use Magento\GiftCard\Model\Catalog\Product\Type\Giftcard as ProductGiftCard;
use Totaltools\Signifyd\Model\GenerateGiftCardAccountsInvoice;
use Magento\Framework\Exception\NotFoundException;
use Shippit\Shipping\Model\ResourceModel\Sync\Order\CollectionFactory;
use Totaltools\Vii\Model\RewriteGiftCardAccount as Giftcardaccount;
use Totaltools\Vii\Model\RewriteGiftCardAccountFactory;
use Magento\Framework\Serialize\Serializer\Json;
use Signifyd\Connect\Helper\OrderHelper;

class Capture
{
    /**
     * @var \Totaltools\Signifyd\Model\Logger
     */
    protected $logger;

    /**
     * @var \Totaltools\Signifyd\Helper\Config
     */
    protected $configHelper;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;

    /**
     * @var \Totaltools\Storelocator\Model\StoreShippitService
     */
    protected $shippitService;

    /**
     * @var \Magento\Sales\Model\Order\Email\Sender\OrderSender
     */
    protected $orderSender;

    /**
     * @var GenerateGiftCardAccountsInvoice
     */
    protected $generateGiftCardVii;

    /**
     * @var \Signifyd\Connect\Model\ResourceModel\Casedata
     */
    protected $casedataResourceModel;

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @var RewriteGiftCardAccountFactory
     */
    protected $giftCAFactory;
    /**
     * Instance of serializer.
     *
     * @var Json
     */
    private $serializer;
    /**
     * @var OrderHelper
     */
    protected $orderHelper;

    public function __construct(
        \Totaltools\Signifyd\Model\Logger $logger,
        \Totaltools\Signifyd\Helper\Config $configHelper,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreShippitService $shippitService,
        \Magento\Sales\Model\Order\Email\Sender\OrderSender $orderSender,
        GenerateGiftCardAccountsInvoice $generateGiftCardVii,
        CollectionFactory $collectionFactory,
        \Signifyd\Connect\Model\ResourceModel\Casedata $casedataResourceModel,
        RewriteGiftCardAccountFactory $giftCAFactory,
        Json $serializer,
        OrderHelper $orderHelper
    ) {
        $this->logger = $logger;
        $this->configHelper = $configHelper;
        $this->storeRepository = $storeRepository;
        $this->shippitService = $shippitService;
        $this->orderSender = $orderSender;
        $this->generateGiftCardVii = $generateGiftCardVii;
        $this->collectionFactory  = $collectionFactory;
        $this->casedataResourceModel = $casedataResourceModel;
        $this->giftCAFactory = $giftCAFactory;
        $this->serializer = $serializer;
        $this->orderHelper = $orderHelper;
    }

    public function around__invoke(
        UpdateOrderCapture $subject,
        callable $proceed,
        Order $order,
        $case,
        $enableTransaction,
        $completeCase
    ) {
        $this->logger->info('Signifyd invoking Capture::around__invoke order_id: ' . $order->getIncrementId());
        if (in_array($case->getMagentoStatus(), ['processing_response_from_magento', 'completed'])) {
            $this->logger->info('Order: ' . $order->getIncrementId() . ' case is already in ' . $case->getMagentoStatus() . ' status');
            $result = false;
            return $result;
        }
        $case->setMagentoStatus('processing_response_from_magento')->setUpdated(Date('Y-m-d H:i:s', time()));
        $this->casedataResourceModel->save($case);
        $result = false;

        if (!$order->hasInvoices()) {
            $result = $proceed($order, $case, $enableTransaction, $completeCase);
        }

        $this->logger->info(
            __('Warehouse data for order before update by signifyd Capture::around__invoke plugin' . $order->getIncrementId() . ' '
                . ' warehouse-' . $order->getWarehouse() . ' store- ' . $order->getData('store') . 'location id-' . $order->getData('storelocator_id'))
        );

        if ($this->configHelper->isEnabled()) {
            $this->logger->info('Signify Capture::around__invoke Plugin hit ');
            try {
                $order = $case->getOrder(true);
                if (($order->getStatus() == Order::STATE_PROCESSING || $order->getStatus() == Order::STATE_COMPLETE) && $result) {
                    $firstInvoice = false;
                    $isVirtualOrder = false;
                    foreach ($order->getInvoiceCollection() as $invoice) {
                        $firstInvoice = $invoice;
                        break;
                    }
                    
                    foreach ($order->getAllItems() as $orderItem) {
                        if ($orderItem->getProductType() === ProductGiftCard::TYPE_GIFTCARD) {
                            $isVirtualOrder = true;
                        }
                    }
                    if (!$isVirtualOrder) {
                        $cards = $order->getGiftCards() ? $this->serializer->unserialize($order->getGiftCards()) : [];
                        if(!empty($cards)) {
                            $this->redeemGiftCards($order, $cards);
                        }
                        $shippitCollection = $this->collectionFactory->create()
                            ->addFieldToFilter('order_id', $order->getId());
                        if ($shippitCollection->getSize() < 1) {
                            $store = $this->getOrderStore($order);
                            $this->logger->info(
                                __('Warehouse data for order before push to shippit by plugin' . $order->getIncrementId() . ' '
                                    . ' warehouse-' . $order->getWarehouse() . ' store- ' . $order->getData('store') . 'location id-' . $order->getData('storelocator_id'))
                            );
                            $this->logger->info('push order to the Shippit' . $order->getIncrementId());
                            try {
                                $this->shippitService->sendOrder($order, $store);
                            } catch (NotFoundException $e) {
                                $this->logger->info('Shippit Error while pushing order from signifyd. Order Id -' . $order->getIncrementId());
                                $this->logger->info($e->getMessage());
                            }
                        }
                    } else {
                        $this->generateGiftCardVii->pushOrder($order);
                    }
                    $this->orderSender->send($order, true);
                }
            } catch (\Exception $e) {
                $this->logger->info($e->__toString(), ['entity' => $case]);
                return false;
            }
        }

        return $result;
    }

    /**
     * @param Order $order
     * @return \Totaltools\Storelocator\Model\Store
     */
    protected function getOrderStore(Order $order)
    {
        $storeId = (int)$order->getStorelocatorId();
        $store = $this->storeRepository->getById($storeId);
        return $store;
    }

    private function redeemGiftCards($order, $cards)
    {
        if (!empty($cards) && is_array($cards)) {
            foreach ($cards as &$card) {
                /** @var \Totaltools\Vii\Model\RewriteGiftCardAccount $giftCard */
                $giftCard = $this->giftCAFactory->create();
                $giftCard->loadByCode($card[Giftcardaccount::ID], $card[Giftcardaccount::PIN]);
                if ($giftCard->getId() && isset($card[Giftcardaccount::PRE_AUTH_CODE])) {
                    $giftCard->setExternalReference($order->getIncrementId());
                    $giftCard->charge($card[Giftcardaccount::BASE_AMOUNT], $card[Giftcardaccount::PRE_AUTH_CODE]);
                    unset($card[Giftcardaccount::PRE_AUTH_CODE]);
                }
            }
            $order->setGiftCards($this->serializer->serialize($cards))->save();
            $message = "Gift Cards Redeemed";
            $this->orderHelper->addCommentToStatusHistory($order, $message);
        }
    }
}
