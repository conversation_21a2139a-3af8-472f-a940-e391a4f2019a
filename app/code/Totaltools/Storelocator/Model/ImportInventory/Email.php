<?php
/**
 * Total Tools Store Locator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Model\ImportInventory;

use Exception;
use Magento\Framework\App\Area;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\DataObject;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Sales\Model\Order\Email\Container\Template;
use Magento\Store\Model\Store;
use Totaltools\Storelocator\Model\Config\Source\Inventory as InventoryConfig;
use Totaltools\Storelocator\Model\Inventoryimportreport;

/**
 * Class Email
 * @package Totaltools\Storelocator\Model\ImportInventory
 */
class Email
{
    /**
     * @var TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var Template
     */
    private $templateContainer;

    /**
     * @var InventoryConfig
     */
    private $inventoryConfig;

    /**
     * Email constructor.
     * @param TransportBuilder $transportBuilder
     * @param ScopeConfigInterface $scopeConfig
     * @param Template $templateContainer
     * @param InventoryConfig $inventoryConfig
     */
    public function __construct(
        TransportBuilder $transportBuilder,
        ScopeConfigInterface $scopeConfig,
        Template $templateContainer,
        InventoryConfig $inventoryConfig
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->scopeConfig = $scopeConfig;
        $this->templateContainer = $templateContainer;
        $this->inventoryConfig = $inventoryConfig;
    }

    /**
     * @param string $message
     * @param string $templateIdentifier
     */
    public function sendFullImportEmail($message, $templateIdentifier, $apiRecipient = false)
    {
        if ($apiRecipient) {
            $recipientConfig = $this->inventoryConfig->getDefaultValue(InventoryConfig::CONFIG_INVENTORY_API_EMAIL_RECIPIENT);
        } else {
            $recipientConfig = $this->inventoryConfig->getDefaultValue(InventoryConfig::CONFIG_INVENTORY_EMAIL_RECEIVER);
        }
        if ($recipientConfig) {
            $listRecipients = explode(',', $recipientConfig);
            foreach ($listRecipients as $recipient) {
                try {
                    $transport = ['message' => $message];
                    $transportObject = new DataObject($transport);
                    $this->templateContainer->setTemplateVars($transportObject->getData());
                    $mailer = $this->transportBuilder
                        ->setTemplateIdentifier($templateIdentifier)
                        ->setTemplateOptions([
                            'area' => Area::AREA_ADMINHTML,
                            'store' => Store::DEFAULT_STORE_ID,
                        ])
                        ->setTemplateVars($transport)
                        ->setFrom('general')
                        ->addTo(trim($recipient));
                    $transport = $mailer->getTransport();
                    $transport->sendMessage();
                } catch (Exception $exception) {
                    continue;
                }
            }
        }
    }

    /**
     * @param Inventoryimportreport $import
     * @param string $error
     */
    public function sendPartialImportErrorEmail(Inventoryimportreport $import, string $error = '')
    {
        $message =
            '<p>Filename: '.$import->getFilename().'<br>'.
            'Line count: '.$import->getRowCountInFile().'<br>'.
            'Error: '.$error.'</p>';
        $templateIdentifier = 'import_inventory_partial_error_email';
        $this->sendFullImportEmail($message, $templateIdentifier);
    }

    /**
     * @param Inventoryimportreport $import
     * @param string $error
     */
    public function sendFullImportErrorEmail(Inventoryimportreport $import, string $error = '')
    {
        $message =
            '<p>Filename: '.$import->getFilename().'<br>'.
            'Line count: '.$import->getRowCountInFile().'<br>'.
            'Error: '.$error.'</p>';
        $templateIdentifier = 'import_inventory_error_email';
        $this->sendFullImportEmail($message, $templateIdentifier);
    }

    /**
     * @param string $error
     */
    public function sendErrorEmail(string $error = '')
    {
        $message = '<p>Error: '.$error.'</p>';
        $templateIdentifier = 'error_email';
        $this->sendFullImportEmail($message, $templateIdentifier);
    }

    /**
     * @param Inventoryimportreport $import
     * @param array $resultData
     * @throws Exception
     */
    public function sendFullImportSuccessEmail(Inventoryimportreport $import, array $resultData)
    {
        $message =
            '<p>Filename: '.$import->getFilename().'<br>'.
            'Line count: '.$import->getRowCountInFile().'</p>';
        $message .= '
            <table style="border: 1px solid black; border-collapse: collapse; width:100%">
                <tr style="border: 1px solid black;">
                    <th style="border: 1px solid black;">ERP ID</th>
                <th style="border: 1px solid black;">SKU Count</th>
                </tr>';
        foreach ($resultData as $item) {
            $message .= '
                <tr style="border: 1px solid black;">
                    <td style="border: 1px solid black;">'.$item[array_keys($item)[0]].'</td>
                    <td style="border: 1px solid black;">'.$item[array_keys($item)[1]].'</td>
                </tr>';
        }
        $message .= '
            </table>';
        $templateIdentifier = 'import_inventory_success_email';
        $this->sendFullImportEmail($message, $templateIdentifier);
    }

    /**
     * @param array $records
     * @throws Exception
     */
    public function sendSummaryOfImportedRecordsEmail($records)
    {
        $message = '
            <table style="border: 1px solid black; border-collapse: collapse; width:100%">
                <tr style="border: 1px solid black;">
                    <th style="border: 1px solid black;">ERP ID</th>
                    <th style="border: 1px solid black;">Store Name</th>
                    <th style="border: 1px solid black;">No. of Records Imported</th>
                </tr>';
        foreach ($records as $store) {
            $message .= '
                <tr style="border: 1px solid black;">
                    <td style="border: 1px solid black;">'.$store['erp_id'].'</td>
                    <td style="border: 1px solid black;">'.$store['store_name'].'</td>
                    <td style="border: 1px solid black;">'.$store['total'].'</td>
                </tr>';
        }
        $message .= '
            </table>';
        $templateIdentifier = 'import_inventory_api_success_email';
        $this->sendFullImportEmail($message, $templateIdentifier, true);
    }

    /**
     * Send SOH API failure alert email
     *
     * @param array $failedStores
     * @throws Exception
     */
    public function sendSohApiFailureEmail($failedStores)
    {
        if (empty($failedStores)) {
            return;
        }

        $message = '<p><strong>SOH API Import Failures Detected</strong></p>';
        $message .= '<p>The following stores failed during the SOH API import process:</p>';
        $message .= '
            <table style="border: 1px solid black; border-collapse: collapse; width:100%">
                <tr style="border: 1px solid black; background-color: #f2f2f2;">
                    <th style="border: 1px solid black; padding: 8px;">ERP ID</th>
                    <th style="border: 1px solid black; padding: 8px;">Store Name</th>
                    <th style="border: 1px solid black; padding: 8px;">Error Message</th>
                    <th style="border: 1px solid black; padding: 8px;">Timestamp</th>
                </tr>';

        foreach ($failedStores as $store) {
            $message .= '
                <tr style="border: 1px solid black;">
                    <td style="border: 1px solid black; padding: 8px;">' . htmlspecialchars($store['erp_id']) . '</td>
                    <td style="border: 1px solid black; padding: 8px;">' . htmlspecialchars($store['store_name']) . '</td>
                    <td style="border: 1px solid black; padding: 8px; color: #d32f2f;">' . htmlspecialchars($store['error']) . '</td>
                    <td style="border: 1px solid black; padding: 8px;">' . htmlspecialchars($store['timestamp']) . '</td>
                </tr>';
        }

        $message .= '</table>';
        $message .= '<p><strong>Total Failed Stores: ' . count($failedStores) . '</strong></p>';
        $message .= '<p>Please check the system logs and API endpoints for these stores.</p>';

        $templateIdentifier = 'import_inventory_api_failure_email';
        $this->sendFullImportEmail($message, $templateIdentifier, false);
    }

    /**
     * Send Delta SOH API failure alert email
     *
     * @param array $failedStores
     * @throws Exception
     */
    public function sendDeltaSohApiFailureEmail($failedStores)
    {
        if (empty($failedStores)) {
            return;
        }

        $message = '<p><strong>Delta SOH API Import Failures Detected</strong></p>';
        $message .= '<p>The following stores failed during the Delta SOH API import process:</p>';
        $message .= '<p><em>Note: Empty responses from the Delta SOH API are normal and indicate no inventory changes. Only actual errors are reported below.</em></p>';
        $message .= '
            <table style="border: 1px solid black; border-collapse: collapse; width:100%">
                <tr style="border: 1px solid black; background-color: #f2f2f2;">
                    <th style="border: 1px solid black; padding: 8px;">ERP ID</th>
                    <th style="border: 1px solid black; padding: 8px;">Store Name</th>
                    <th style="border: 1px solid black; padding: 8px;">Error Message</th>
                    <th style="border: 1px solid black; padding: 8px;">Timestamp</th>
                </tr>';

        foreach ($failedStores as $store) {
            $message .= '
                <tr style="border: 1px solid black;">
                    <td style="border: 1px solid black; padding: 8px;">' . htmlspecialchars($store['erp_id']) . '</td>
                    <td style="border: 1px solid black; padding: 8px;">' . htmlspecialchars($store['store_name']) . '</td>
                    <td style="border: 1px solid black; padding: 8px; color: #d32f2f;">' . htmlspecialchars($store['error']) . '</td>
                    <td style="border: 1px solid black; padding: 8px;">' . htmlspecialchars($store['timestamp']) . '</td>
                </tr>';
        }

        $message .= '</table>';
        $message .= '<p><strong>Total Failed Stores: ' . count($failedStores) . '</strong></p>';
        $message .= '<p>Please check the system logs and API endpoints for these stores.</p>';
        $message .= '<p><strong>Important:</strong> This alert is throttled and will only be sent once per configured interval to prevent spam.</p>';

        $templateIdentifier = 'import_inventory_delta_api_failure_email';
        $this->sendDeltaImportEmail($message, $templateIdentifier);
    }

    /**
     * Send delta import email with specific recipient configuration
     *
     * @param string $message
     * @param string $templateIdentifier
     * @throws Exception
     */
    private function sendDeltaImportEmail($message, $templateIdentifier)
    {
        $recipientConfig = $this->inventoryConfig->getDefaultValue(InventoryConfig::CONFIG_INVENTORY_DELTA_SOH_API_EMAIL_RECIPIENT);

        // Fallback to regular SOH API recipients if delta recipients not configured
        if (!$recipientConfig) {
            $recipientConfig = $this->inventoryConfig->getDefaultValue(InventoryConfig::CONFIG_INVENTORY_API_EMAIL_RECIPIENT);
        }

        if ($recipientConfig) {
            $listRecipients = explode(',', $recipientConfig);
            foreach ($listRecipients as $recipient) {
                try {
                    $transport = ['message' => $message];
                    $transportObject = new DataObject($transport);
                    $this->templateContainer->setTemplateVars($transportObject->getData());
                    $mailer = $this->transportBuilder
                        ->setTemplateIdentifier($templateIdentifier)
                        ->setTemplateOptions([
                            'area' => Area::AREA_ADMINHTML,
                            'store' => Store::DEFAULT_STORE_ID,
                        ])
                        ->setTemplateVars($transport)
                        ->setFrom('general')
                        ->addTo(trim($recipient));
                    $transport = $mailer->getTransport();
                    $transport->sendMessage();
                } catch (Exception $exception) {
                    continue;
                }
            }
        }
    }

    /**
     * @param string $message
     * @param string $file
     * @throws Exception
     */
    public function sendEmail($message, $file)
    {
        $recipientConfig = $this->inventoryConfig->getDefaultValue(InventoryConfig::CONFIG_INVENTORY_EMAIL_RECEIVER);
        if ($recipientConfig) {
            $listRecipients = explode(',', $recipientConfig);
            foreach ($listRecipients as $recipient) {
                try {
                    $transport = [
                        'message' => $message,
                        'file' => $file
                    ];
                    $transportObject = new DataObject($transport);

                    $this->templateContainer->setTemplateVars($transportObject->getData());

                    $mailer = $this->transportBuilder
                        ->setTemplateIdentifier('import_inventory_mv_error_email')
                        ->setTemplateOptions([
                            'area' => Area::AREA_FRONTEND,
                            'store' => Store::DEFAULT_STORE_ID,
                        ])
                        ->setTemplateVars($transport)
                        ->setFrom('general')
                        ->addTo(trim($recipient));
                    $transport = $mailer->getTransport();
                    $transport->sendMessage();
                } catch (Exception $exception) {
                    throw $exception;
                }
            }
        }
    }
}
