<?php

namespace Totaltools\Storelocator\Model;

use Exception;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Storelocator\Model\ImportInventory\Email;
use Magento\Framework\App\ResourceConnection;
use Totaltools\Pronto\Model\Api\SohApi;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Totaltools\Storelocator\Model\Config\Source\Inventory as InventoryConfig;
use Zend_Db_Exception;

/**
 * @api
 */
class SohProcessor
{
    /**
     * @var TimezoneInterface
     */
    private $localeDate;

    /**
     * @var Email
     */
    private $transport;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var SohApi
     */
    private $sohApi;

    /**
     * @var array
     */
    private $failedStores = [];

    /**
     * @var array
     */
    private $errorMessages = [];

    /**
     * @var array
     */
    private $deltaFailedStores = [];

    /**
     * @var CacheInterface
     */
    private $cache;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * SohProcessor constructor.
     * @param TimezoneInterface $localeDate
     * @param Email $email
     * @param LoggerInterface $logger
     * @param ResourceConnection $resource
     * @param SohApi $sohApi
     * @param CacheInterface $cache
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        TimezoneInterface $localeDate,
        Email $email,
        LoggerInterface $logger,
        ResourceConnection $resource,
        SohApi $sohApi,
        CacheInterface $cache,
        ScopeConfigInterface $scopeConfig
    ) {

        $this->localeDate = $localeDate;
        $this->transport = $email;
        $this->logger = $logger;
        $this->resource = $resource;
        $this->sohApi = $sohApi;
        $this->cache = $cache;
        $this->scopeConfig = $scopeConfig;
    }

    public function importInventory($store)
    {
        $storeFullSohApiEndpoint = $store['full_soh_api_endpoint'];
        $erpId = $store['erp_id'];
        $storeName = $store['store_name'] ?? 'Unknown Store';

        try {
            if (empty($storeFullSohApiEndpoint)) {
                $this->addFailedStore($erpId, $storeName, 'No API endpoint configured');
                return false;
            }

            $response = $this->sohApi->sendApiSohRequest($storeFullSohApiEndpoint, $erpId, 'full_soh');

            if (empty($response)) {
                $this->addFailedStore($erpId, $storeName, 'Empty response from API');
                return false;
            }

            if (empty($response['Items']['Item'])) {
                $this->addFailedStore($erpId, $storeName, 'No items found in API response');
                return false;
            }

            $items = $response['Items']['Item'];
            $items = isset($items['ItemCode']) ? [$items] : $items;
            $data = [];
            foreach ($items as $item) {
                $prontoErpId = $item['Warehouse'];
                if ($prontoErpId == $erpId) {
                    $data[] = [
                        'erp_id'    => $erpId,
                        'sku'       => $item['ItemCode'],
                        'units'     => $item['SOH']
                    ];
                }
            }

            if (empty($data)) {
                $this->addFailedStore($erpId, $storeName, 'No matching inventory data found for store');
                return false;
            }

            $this->cleanupInventoryForStore($erpId);
            $this->importSohInventoryDataForStore($data);

            $this->logger->info("Successfully imported " . count($data) . " items for store {$storeName} (ERP ID: {$erpId})");
            return true;

        } catch (\Exception $e) {
            $errorMessage = "Exception during import: " . $e->getMessage();
            $this->addFailedStore($erpId, $storeName, $errorMessage);
            $this->logger->error("Failed to import inventory for store {$storeName} (ERP ID: {$erpId}): " . $errorMessage);
            return false;
        }
    }

    public function updateInventory($store)
    {
        $updatedRecordsCount = 0;
        $storeDeltaSohApiEndpoint = $store['delta_soh_api_endpoint'];
        $erpId = $store['erp_id'];
        $storeName = $store['store_name'] ?? 'Unknown Store';
        if($erpId == 645) {
            $this->logger->info("Skipping Delta SOH for store {$storeName} (ERP ID: {$erpId}) - this is a test store");
            // return 0; // Skip test store
        }
        try {
            if (empty($storeDeltaSohApiEndpoint)) {
                $this->addDeltaFailedStore($erpId, $storeName, 'No delta API endpoint configured');
                return 0;
            }

            $response = $this->sohApi->sendApiSohRequest($storeDeltaSohApiEndpoint, $erpId, 'delta_soh');

            if (empty($response)) {
                $this->logger->info("Delta SOH: No response from API for store {$storeName} (ERP ID: {$erpId}) - this is normal when there are no changes");
                return 0;
            }


            // Check if response has error indicators
            if (isset($response['error']) || isset($response['Error'])) {
                $errorMsg = $response['error'] ?? $response['Error'] ?? 'Unknown API error';
                $this->addDeltaFailedStore($erpId, $storeName, "API returned error: {$errorMsg}");
                return 0;
            }

            if (!empty($response['Items']['Item'])) {
                $items = $response['Items']['Item'];
                // check for single result
                $items = isset($items['ItemCode']) ? [$items] : $items;
                foreach ($items as $item) {
                    $prontoErpId = $item['Warehouse'];
                    if ($prontoErpId == $erpId) {
                        $data = [
                            'erp_id'    => $erpId,
                            'sku'       => $item['ItemCode'],
                            'units'     => $item['SOH']
                        ];
                        $updatedRecordsCount++;
                        $this->importDelteSohInventoryDataForStore($data);
                    }
                }

                if ($updatedRecordsCount > 0) {
                    $this->logger->info("Delta SOH: Successfully updated {$updatedRecordsCount} items for store {$storeName} (ERP ID: {$erpId})");
                }
            }
        } catch (\Exception $e) {
            $this->addDeltaFailedStore($erpId, $storeName, "Exception during delta update: " . $e->getMessage());
            $this->logger->error("Delta SOH error for store {$storeName} (ERP ID: {$erpId}): " . $e->getMessage());
        }

        return $updatedRecordsCount;
    }

    public function cleanupInventoryTable()
    {
        $connection = $this->resource->getConnection();
        $tableName = $this->resource->getTableName('magestore_storelocator_store_inventory');
        $connection->truncateTable($tableName);
    }

    public function cleanupInventoryForStore($erpId)
    {
        $connection = $this->resource->getConnection();
        $tableName = $this->resource->getTableName('magestore_storelocator_store_inventory');
        $connection->delete($tableName, ['erp_id = ?' => $erpId]);
    }

    public function importSohInventoryDataForStore($data)
    {
        $connection = $this->resource->getConnection();
        $tableName = $this->resource->getTableName('magestore_storelocator_store_inventory');
        if (empty($data)) {
            return;
        }
        
        $connection->insertMultiple($tableName, $data);
    }

    public function importDelteSohInventoryDataForStore($data)
    {
        $connection = $this->resource->getConnection();
        $tableName = $this->resource->getTableName('magestore_storelocator_store_inventory');
        $connection->insertOnDuplicate($tableName, $data);
    }

    public function getSummaryOfImportedRecords()
    {
        $connection = $this->resource->getConnection();
        $query = "SELECT i.erp_id, s.store_name as store_name, COUNT(i.erp_id) AS total FROM magestore_storelocator_store_inventory i INNER JOIN magestore_storelocator_store s 
        ON i.erp_id = s.erp_id GROUP BY i.erp_id, s.store_name ORDER BY i.erp_id";
        $result = $connection->fetchAll($query);
        return $result;
    }

    public function prepareSummaryOfImportedRecordsEmail()
    {
        $records = $this->getSummaryOfImportedRecords();
        $this->transport->sendSummaryOfImportedRecordsEmail($records);
    }

    /**
     * Add a failed store to the tracking array
     *
     * @param string $erpId
     * @param string $storeName
     * @param string $errorMessage
     * @return void
     */
    private function addFailedStore($erpId, $storeName, $errorMessage)
    {
        $this->failedStores[] = [
            'erp_id' => $erpId,
            'store_name' => $storeName,
            'error' => $errorMessage,
            'timestamp' => $this->localeDate->date()->format('Y-m-d H:i:s')
        ];

        $this->errorMessages[] = "Store: {$storeName} (ERP ID: {$erpId}) - {$errorMessage}";
    }

    /**
     * Get failed stores
     *
     * @return array
     */
    public function getFailedStores()
    {
        return $this->failedStores;
    }

    /**
     * Check if there are any failed stores
     *
     * @return bool
     */
    public function hasFailures()
    {
        return !empty($this->failedStores);
    }

    /**
     * Reset failure tracking
     *
     * @return void
     */
    public function resetFailureTracking()
    {
        $this->failedStores = [];
        $this->errorMessages = [];
    }

    /**
     * Send failure alert email
     *
     * @return void
     */
    public function sendFailureAlert()
    {
        if ($this->hasFailures()) {
            $this->transport->sendSohApiFailureEmail($this->failedStores);
        }
    }

    /**
     * Add a failed store to the delta tracking array
     *
     * @param string $erpId
     * @param string $storeName
     * @param string $errorMessage
     * @return void
     */
    private function addDeltaFailedStore($erpId, $storeName, $errorMessage)
    {
        $this->deltaFailedStores[] = [
            'erp_id' => $erpId,
            'store_name' => $storeName,
            'error' => $errorMessage,
            'timestamp' => $this->localeDate->date()->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Get delta failed stores
     *
     * @return array
     */
    public function getDeltaFailedStores()
    {
        return $this->deltaFailedStores;
    }

    /**
     * Check if there are any delta failed stores
     *
     * @return bool
     */
    public function hasDeltaFailures()
    {
        return !empty($this->deltaFailedStores);
    }

    /**
     * Reset delta failure tracking
     *
     * @return void
     */
    public function resetDeltaFailureTracking()
    {
        $this->deltaFailedStores = [];
    }

    /**
     * Check if delta failure email should be sent based on throttle interval
     *
     * @return bool
     */
    private function shouldSendDeltaFailureEmail()
    {
        $throttleInterval = $this->scopeConfig->getValue(
            InventoryConfig::CONFIG_INVENTORY_DELTA_SOH_EMAIL_THROTTLE_INTERVAL,
            ScopeInterface::SCOPE_STORE
        ) ?: 60; // Default to 60 minutes

        $cacheKey = 'delta_soh_last_failure_email_sent';
        $lastSentTime = $this->cache->load($cacheKey);

        if (!$lastSentTime) {
            return true;
        }

        $currentTime = time();
        $timeDiff = ($currentTime - $lastSentTime) / 60; // Convert to minutes

        return $timeDiff >= $throttleInterval;
    }

    /**
     * Send delta failure alert email with throttling
     *
     * @return void
     */
    public function sendDeltaFailureAlert()
    {
        if ($this->hasDeltaFailures() && $this->shouldSendDeltaFailureEmail()) {
            $this->transport->sendDeltaSohApiFailureEmail($this->deltaFailedStores);

            // Update cache with current timestamp
            $cacheKey = 'delta_soh_last_failure_email_sent';
            $this->cache->save(time(), $cacheKey, [], 86400); // Cache for 24 hours
        }
    }
}
