<?php

namespace Totaltools\Storelocator\Cron;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Class DeltaSoh
 * @package Totaltools\Storelocator\Cron
 */
class DeltaSoh
{
    /**
     * API SOH Enabled
     */
    const XML_PATH_API_SOH_ENABLED = 'totaltools_pronto/general/enable_api_soh';

    /**
     * @var \Totaltools\Storelocator\Model\SohProcessor
     */
    private $sohProcessor;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory
     */
    private $storeCollectionFactory;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * FullSoh constructor.
     * @param \Totaltools\Storelocator\Model\SohProcessor $sohProcessor
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        \Totaltools\Storelocator\Model\SohProcessor $sohProcessor,
        \Psr\Log\LoggerInterface $logger,
        \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->sohProcessor = $sohProcessor;
        $this->logger = $logger;
        $this->storeCollectionFactory = $storeCollectionFactory;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @return $this|bool
     * @throws \Exception
     */
    public function execute()
    {
        if ($this->isApiCronSohEnabled()) {
            try {
                // Reset delta failure tracking for this run
                $this->sohProcessor->resetDeltaFailureTracking();

                $storeCollection = $this->storeCollectionFactory->create();
                if ($storeCollection->getSize()) {
                    $stores = [];
                    foreach ($storeCollection->getData() as $store) {
                        $erpId = $store['erp_id'] ?? null;
                        if ($erpId == 0 || empty($erpId)) {
                            continue;
                        }

                        $updatedRecordsCount = $this->sohProcessor->updateInventory($store);
                        if ($updatedRecordsCount > 0) {
                            $stores[] = [
                                $store['erp_id'],
                                $store['store_name'],
                                $updatedRecordsCount
                            ];
                        }
                    }
                }

                // Send failure alert email if there are failures (with throttling)
                if ($this->sohProcessor->hasDeltaFailures()) {
                    $this->sohProcessor->sendDeltaFailureAlert();
                    $failedStores = $this->sohProcessor->getDeltaFailedStores();
                    $this->logger->warning(__('Delta SOH API import completed with failures. Alert email sent for %1 stores (throttled).', count($failedStores)));
                }

            } catch (\Exception $e) {
                $this->logger->critical(__('Something went wrong with the delta SOH cronjob.\\n %1', $e->getMessage()));
            }

            return $this;
        }
    }

    /**
     * @inheritdoc
     */
    public function isApiCronSohEnabled() : ?bool
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_API_SOH_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }
}
