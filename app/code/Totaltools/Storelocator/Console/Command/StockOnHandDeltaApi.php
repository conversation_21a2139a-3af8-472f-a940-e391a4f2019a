<?php

namespace Totaltools\Storelocator\Console\Command;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\State;
use Magento\Store\Model\ScopeInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Totaltools\Storelocator\Model\SohProcessor;

class StockOnHandDeltaApi extends Command
{
    /**
     * API SOH Enabled
     */
    const XML_PATH_API_SOH_ENABLED = 'totaltools_pronto/general/enable_api_soh';

    /**
     * @var SohProcessor
     */
    private $sohProcessor;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory
     */
    private $storeCollectionFactory;

    /**
     * @var State
     */
    private $state;

    /**
     * StockOnHandDeltaApi constructor.
     * @param SohProcessor $sohProcessor
     * @param ScopeConfigInterface $scopeConfig
     * @param \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory
     * @param State $state
     * @param string|null $name
     */
    public function __construct(
        SohProcessor $sohProcessor,
        ScopeConfigInterface $scopeConfig,
        \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory,
        State $state,
        $name = null
    ) {
        parent::__construct($name);
        $this->sohProcessor = $sohProcessor;
        $this->scopeConfig = $scopeConfig;
        $this->storeCollectionFactory = $storeCollectionFactory;
        $this->state = $state;
    }

    /**
     * configure
     *
     * @return void
     */
    protected function configure()
    {
        $this->setName('totaltools:sohapidelta:import');
        $this->setDescription("Import stock on hand from pronto API");
        parent::configure();
    }

    /**
     * execute
     *
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        if ($this->isApiCronSohEnabled()) {
            $output->writeln("Pronto Delta API SOH Import started\n");

            $successCount = 0;
            $failureCount = 0;
            $totalUpdatedRecords = 0;

            try {
                $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);

                // Reset delta failure tracking for this run
                $this->sohProcessor->resetDeltaFailureTracking();

                $storeCollection = $this->storeCollectionFactory->create();
                if ($storeCollection->getSize()) {
                    $stores = [];
                    $totalStores = $storeCollection->getSize();
                    $processedStores = 0;

                    $output->writeln("Processing {$totalStores} stores for delta inventory updates...\n");

                    foreach ($storeCollection->getData() as $store) {
                        $processedStores++;
                        $erpId = $store['erp_id'] ?? null;
                        $storeName = $store['store_name'] ?? 'Unknown Store';

                        if ($erpId == 0 || empty($erpId)) {
                            $output->writeln("Skipping store {$processedStores}/{$totalStores}: {$storeName} (No ERP ID)");
                            continue;
                        }

                        $output->writeln("Processing store {$processedStores}/{$totalStores}: {$storeName} (ID: {$erpId})");

                        try {
                            $updatedRecordsCount = $this->sohProcessor->updateInventory($store);

                            if ($updatedRecordsCount > 0) {
                                $stores[] = [
                                    $erpId,
                                    $storeName,
                                    $updatedRecordsCount
                                ];
                                $totalUpdatedRecords += $updatedRecordsCount;
                                $output->writeln("  → Updated {$updatedRecordsCount} inventory records");
                                $successCount++;
                            } else {
                                $output->writeln("  → No inventory changes detected (normal for delta imports)");
                                $successCount++;
                            }
                        } catch (\Exception $e) {
                            $output->writeln("  → <error>Error: " . $e->getMessage() . "</error>");
                            $failureCount++;
                        }
                        // break; // Remove this break to process all stores
                    }
                }

                // Send failure alert email if there are failures (with throttling)
                if ($this->sohProcessor->hasDeltaFailures()) {
                    $this->sohProcessor->sendDeltaFailureAlert();
                    $failedStores = $this->sohProcessor->getDeltaFailedStores();
                    $output->writeln("\n<comment>Delta failure alert email sent for " . count($failedStores) . " failed stores (throttled).</comment>");
                }

            } catch (\Exception $exception) {
                $output->writeln("<error>Critical error: " . $exception->getMessage() . "</error>");
                $failureCount++;
            }

            $output->writeln("\nPronto Delta API SOH Import finished");
            $output->writeln("Summary: {$successCount} successful, {$failureCount} failed");
            $output->writeln("Total inventory records updated: {$totalUpdatedRecords}");

            return $failureCount > 0 ?
                \Magento\Framework\Console\Cli::RETURN_FAILURE :
                \Magento\Framework\Console\Cli::RETURN_SUCCESS;
        } else {
            $output->writeln("Pronto API SOH Import is disabled");
            return \Magento\Framework\Console\Cli::RETURN_FAILURE;
        }
    }

    /**
     * @inheritdoc
     */
    public function isApiCronSohEnabled() : ?bool
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_API_SOH_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }
}
