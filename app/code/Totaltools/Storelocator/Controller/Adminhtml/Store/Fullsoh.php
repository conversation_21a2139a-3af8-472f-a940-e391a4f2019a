<?php

namespace Totaltools\Storelocator\Controller\Adminhtml\Store;

class Fullsoh extends \Magento\Backend\App\Action
{
    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;

    /**
     * @var \Totaltools\Storelocator\Model\SohProcessor
     */
    private $sohProcessor;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Fullsoh constructor.
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\SohProcessor $sohProcessor
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\SohProcessor $sohProcessor,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->storeRepository = $storeRepository;
        $this->sohProcessor = $sohProcessor;
        $this->logger = $logger;
    }

    
    public function execute()
    {
        $storeId = $this->getRequest()->getParam('storelocator_id');
        $this->sohProcessor->resetFailureTracking();
        try {
            $store = $this->storeRepository->getById($storeId);
            $result = $this->sohProcessor->importInventory($store->getData());
            
            if ($result) {
                $this->messageManager->addSuccessMessage(__('The full SOH import for this store has been completed.'));
            } else {
                $failedStore = $this->sohProcessor->getFailedStores();
                if (!empty($failedStore)) {
                    $this->messageManager->addWarningMessage(__($failedStore[0]['error']));
                } else {
                    $this->messageManager->addErrorMessage(__('SOH import failed for this store. Please check logs for details.'));
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical(__('SOH import failed for store ID %1: %2', $storeId, $e->getMessage()));
            $this->messageManager->addErrorMessage(__('SOH import failed: %1', $e->getMessage()));
        }

        return $this->resultRedirectFactory->create()->setPath(
            'storelocatoradmin/store/edit',
            [
                'storelocator_id' => $storeId
            ]
        );
    }
}
