<?php

namespace Totaltools\Storelocator\Controller\Adminhtml\Store;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class AllStoresFullSohImport extends \Magento\Backend\App\Action
{
    /**
     * API SOH Enabled
     */
    const XML_PATH_API_SOH_ENABLED = 'totaltools_pronto/general/enable_api_soh';

    /**
     * @var \Totaltools\Storelocator\Model\SohProcessor
     */
    private $sohProcessor;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory
     */
    private $storeCollectionFactory;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * AllStoresFullSohImport constructor.
     * @param \Totaltools\Storelocator\Model\SohProcessor $sohProcessor
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Totaltools\Storelocator\Model\SohProcessor $sohProcessor,
        \Psr\Log\LoggerInterface $logger,
        \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory,
        ScopeConfigInterface $scopeConfig
    ) {
        parent::__construct($context);
        $this->sohProcessor = $sohProcessor;
        $this->logger = $logger;
        $this->storeCollectionFactory = $storeCollectionFactory;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @return $this|bool
     * @throws \Exception
     */
    public function execute()
    {
        if ($this->isApiCronSohEnabled()) {
            try {
                $this->sohProcessor->resetFailureTracking();

                $storeCollection = $this->storeCollectionFactory->create();
                if ($storeCollection->getSize()) {
                    foreach ($storeCollection->getData() as $store) {
                        $erpId = $store['erp_id'] ?? null;
                        if ($erpId == 0 || empty($erpId)) {
                            continue;
                        }
                        $this->sohProcessor->importInventory($store);
                    }
                }

                
                $this->sohProcessor->prepareSummaryOfImportedRecordsEmail();

                
                if ($this->sohProcessor->hasFailures()) {
                    $this->sohProcessor->sendFailureAlert();
                    $failedStores = $this->sohProcessor->getFailedStores();
                    $this->messageManager->addWarning(
                        __('SOH import completed with %1 failures. Alert email sent. Check logs for details.', count($failedStores))
                    );
                } else {
                    $this->messageManager->addSuccess(__('All Stores Full SOH import has been completed successfully.'));
                }

                return $this->resultRedirectFactory->create()->setPath('storelocatoradmin/store/index');
            } catch (\Exception $e) {
                $this->logger->critical(__('Something went wrong.\\n %1', $e->getMessage()));
            }
        }
    }

    /**
     * @inheritdoc
     */
    public function isApiCronSohEnabled() : ?bool
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_API_SOH_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }   
}
