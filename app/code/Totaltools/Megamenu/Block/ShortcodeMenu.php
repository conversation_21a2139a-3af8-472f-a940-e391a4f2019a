<?php
/**
 * UpgradeSchema
 *
 * @category  Totaltools
 * @package   Totaltools_Megamenu
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Megamenu\Block;

use Exception;
use Magento\Framework\Data\Tree\NodeFactory;
use Magento\Framework\Data\TreeFactory;
use Magento\Framework\View\Element\Template;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Megamenu\Helper\Cache;

class ShortcodeMenu extends \Magedelight\Megamenu\Block\ShortcodeMenu 
{
    const MENU_TEMPLATE_DEFAULT = 'Magedelight_Megamenu::menu/shortcode.phtml';
    const MENU_TEMPLATE_STATIC_BLOCK = 'Magedelight_Megamenu::menu/menu_block.phtml';
    const USE_STATIC_BLOCK = 'magedelight/general/use_static_block';
    const PARENT_MEGAMENU_BLOCK = 'magedelight/general/parent_mega_menu_block';

    /**
     * @var  Template\Context 
     *
     */
    protected $context;
    
     /**
     * @var  NodeFactory 
     * 
     */
    protected $nodeFactory;
    
     /**
     * @var  TreeFactory 
     *
     */
    protected $treeFactory;

     /**
     * @var  \Magento\Framework\Registry 
     *
     */
    protected $registry;

     /**
     * @var  \Magento\Customer\Model\SessionFactory 
     *
     */
    protected $session;

     /**
     * @var  \Magento\Cms\Model\Page 
     * 
     */
    protected $page;

     /**
     * @var  \Magedelight\Megamenu\Helper\Data 
     *
     */
    protected $helper;

    /**
     * @var \Magedelight\Megamenu\Model\MegamenuManagement
     */
    protected $megamenuManagement;
    /**
     * @var \Magento\Catalog\Helper\Output
     */
    protected $output;

    /**
     * @var \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory
     */
    protected $_categoryCollectionFactory;

    /**
     * @var Cache
     */
    protected $cacheHelper;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * Topmenu constructor.
     * @param Template\Context $context
     * @param NodeFactory $nodeFactory
     * @param TreeFactory $treeFactory
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Customer\Model\SessionFactory $session
     * @param \Magento\Cms\Model\Page $page
     * @param \Magedelight\Megamenu\Helper\Data $helper
     * @param \Magedelight\Megamenu\Model\MegamenuManagement $megamenuManagement
     * @param \Magento\Catalog\Helper\Output $output,
     * @param \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollectionFactory
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        NodeFactory $nodeFactory,
        TreeFactory $treeFactory,
        \Magento\Framework\Registry $registry,
        \Magento\Customer\Model\SessionFactory $session,
        \Magento\Cms\Model\Page $page,
        \Magedelight\Megamenu\Helper\Data $helper,
        \Magedelight\Megamenu\Model\MegamenuManagement $megamenuManagement,
        \Magento\Catalog\Helper\Output $output,
        \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollectionFactory,
        StoreManagerInterface $storeManager,
        \Magento\Catalog\Api\CategoryRepositoryInterface $categoryRepository,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Magento\Framework\View\Element\TemplateFactory $templateFactory,
        Cache $cacheHelper,
        array $data = []
    ) {
        parent::__construct($context, $nodeFactory, $treeFactory, $registry,  $session, $page, $helper,$megamenuManagement,$output,$categoryRepository, $categoryFactory, $templateFactory,$data,$cacheHelper,$storeManager);
        $this->helper = $helper;
        $this->megamenuManagement = $megamenuManagement;
        $this->_categoryCollectionFactory = $categoryCollectionFactory;
        $this->setMenuTemplate();
    }

    public function setMenuTemplate()
    {
        $template = $this->_scopeConfig->isSetFlag(self::USE_STATIC_BLOCK) ? self::MENU_TEMPLATE_STATIC_BLOCK : self::MENU_TEMPLATE_DEFAULT ;
        $this->setTemplate($template);  
    }
    
    /**
     * setCategoryColumn
     *
     * @param  mixed $item
     * @param  mixed $columnCount
     * @param  mixed $itemCategoryId
     * @return string
     */
    public function setCategoryColumn($item, $columnCount = 0,  $itemCategoryId = 0)
    {
        $level = 1;
        $itemCategories = array_map('intval', explode(',', $item->getShowChildCat()??'')); 
        $categoryCollection = $this->getCategoryCollection($itemCategories);
        $levelOne = $this->getLevelOneCategories($categoryCollection, $itemCategories, $itemCategoryId);
        $levelTwo = array_diff($itemCategories, $levelOne);
        if(!$levelOne) {
            return '';
        }
        $html = $this->addItem($categoryCollection, $item, $columnCount, $level,  $levelOne, $levelTwo);        
        return $html;
    }
    
    /**
     * getFeaturedCategories
     *
     * @param  mixed $featuredCategories
     * @return string
     */
    public function getFeaturedCategories($featuredCategories = '') {
        $html = '';
        $featuredCategoryIds = explode(',', $featuredCategories);
        foreach ($featuredCategoryIds as $featuredCategoryId) {
            $_imgHtml   = '';
           
            try {
                $_category = $this->megamenuManagement->getCategoryById($featuredCategoryId);
                if ($_category) {
                    if ($_imgUrl = $_category->getImageUrl()) {
                        $_imgHtml = '<a class="product-image text-center form-group" href="'.$_category->getUrl().'">
                                            <img class="menu-featured-image" title="'.$_category->getName().'" alt="'.$_category->getName().'" src="'. $_imgUrl .'" width="110">
                                        </a>';
                    }
                    $html .= '<div class="f-category col-sm-12 col-xs-12 col-tn-12">
                                '.$_imgHtml.'
                                <a class="product-name form-group" href="'.$_category->getUrl().'">
                                    <strong>'.$_category->getName().'</strong>
                                </a>
                            </div>';
                }
            } catch (Exception $e) {
                $this->_logger->debug($e->getMessage()); 
            }
            
        }
        return $html;
    }
    
    /**
     * getIsEnabled
     *
     * @return bool
     */
    public function getIsEnabled()
    {
        return $this->helper->isEnabled();
    }
    
    /**
     * getCategoryCollection
     *
     * @param  mixed $categoryIds
     * @return CollectionFactory
     */
    public function getCategoryCollection($categoryIds = [])
    {    
        $collection = $this->_categoryCollectionFactory->create()
            ->addAttributeToSelect('*')
            ->addFieldToFilter('entity_id', ['in' => $categoryIds])
            ->addFieldToFilter('is_active', 1)
            ->setOrder('position','ASC');
        return $collection;
    }
        
    /**
     * getLevelOneCategories
     *
     * @param  mixed $categoryCollection
     * @param  mixed $itemCategories
     * @param  mixed $itemCategoryId
     * @return array
     */
    public function getLevelOneCategories($categoryCollection, $itemCategories, $itemCategoryId)
    {
        $levelOne = [];

        foreach ($categoryCollection as $category) {
            $parentId = $category->getParentId();
            if ($parentId && !in_array($parentId, $itemCategories) || $parentId == $itemCategoryId) {
                $levelOne[] = $category->getId();
            }
        }
        return $levelOne;
    }
    
    /**
     * addItem
     *
     * @param  mixed $categoryCollection
     * @param  mixed $item
     * @param  mixed $columnCount
     * @param  mixed $level
     * @param  mixed $levelOne
     * @param  mixed $levelTwo
     * @param  mixed $parentCatId
     * @return string
     */
    public function addItem($categoryCollection, $item, $columnCount, $level,  $levelOne, $levelTwo, $parentCatId = 0) 
    {
        $ulClass = '';
        $html = '';
        $ulClass .= 'child-level-'.$level;
        $html .= '<ul class="'.$ulClass.'">';
        foreach ($categoryCollection as $key => $category) {
            if ($parentCatId != 0 && $category->getParentId() != $parentCatId) {
                continue;
            }
            if ($parentCatId == 0 && in_array($category->getId(), $levelTwo)) {
                continue;
            }
            
            $verticalclass = $category->getId() == $this->getCurrentCat() ? 'active' : '';
            
            $uniqueClass = 'category-item nav-'.$item->getItemId().'-'.$category->getId();
            $liClass = $uniqueClass.' '.$verticalclass;
            $chidrenIds = explode(',', $category->getChildren()?? '');
            $chidrenIds = array_map('intval', $chidrenIds);
            $childCategories = array_intersect($chidrenIds, $levelTwo);
            $liClass .= count($childCategories) ? ' has-sub-category': '';
            $html .= '<li class="'.$liClass.'">';
            $html .= '<a href="'.$category->getUrl().'">'.__($category->getName()).'</a>';
            if(in_array($category->getId(), $levelOne) && $category->getChildrenCount() && count($childCategories)) {
                $level = 2;
                $html .= $this->addItem($categoryCollection, $item, $columnCount, $level, $levelOne, $levelTwo, $category->getId());
            }
            $html .= '</li>';
            $categoryCollection->removeItemByKey($key);
        }
        $html .= '</ul>';
        return $html;
    }

    /**
     * Retrieves the current mega menu by its ID.
     *
     * @param int $menuId The ID of the mega menu.
     * @return MegaMenu|null The mega menu object, or null if not found.
     */
    public function getCurrentMegaMenu($menuId)
    {
        return $this->megamenuManagement->loadMenuById($menuId);
    }

    /**
     * Retrieve the parent mega menu block value from the configuration.
     *
     * @return string|null The value of the parent mega menu block, or null if not found.
     */
    public function getParentMegaMenuBlock()
    {
        return $this->_scopeConfig->getValue(
            self::PARENT_MEGAMENU_BLOCK,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Override the parent getCacheKeyInfo to avoid URL-specific cache keys
     * 
     * @return array
     */
    public function getCacheKeyInfo()
    {
        $cacheKey = [
            'SHORTCODE_MENU',
            $this->getMenuid(),
            $this->_storeManager->getStore()->getId(),
            $this->_design->getDesignTheme()->getId()
        ];
        
        return $cacheKey;
    }
}
